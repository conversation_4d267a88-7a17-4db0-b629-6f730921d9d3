namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for position queries - contains only business logic, no HTTP concerns
/// </summary>
public class PositionFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in position name or code
    /// </summary>
    public string? Keyword { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}
