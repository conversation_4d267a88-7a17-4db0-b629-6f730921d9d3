using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.Persistence.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Services;

public partial interface IProjectScheduleService
{
    Task<ResultDto<ScheduledOutSourceShiftResponseDto>> CreateScheduledOutSourceShift(CreateScheduledOutSourceShiftRequestDto2 dto);
    Task<ResultDto<ScheduledOutSourceShiftResponseDto>> CreateScheduledOutSourceShift(Guid projectScheduleId, CreateScheduledOutSourceShiftRequestDto dto);
    Task<ResultDto<ScheduledOutSourceShiftResponseDto>> UpdateScheduledOutSourceShift(Guid outSourceShiftId, UpdateScheduledOutSourceShiftRequestDto dto);
    Task<ResultDto<ScheduledOutSourceShiftResponseDto>> DuplicateScheduledOutSourceShift(Guid outSourceShiftId, DuplicateScheduledOutSourceShiftRequestDto dto);
    Task<ResultDto<ScheduledOutSourceShiftResponsesDto>> DuplicateScheduledOutSourceShifts(Guid outSourceShiftId, DuplicateScheduledOutSourceShiftsRequestDto dto);
    Task<ResultDto<bool>> DeleteScheduledOutSourceShift(Guid outSourceShiftId);
}

public partial class ProjectScheduleService : BaseService<ProjectScheduleService>, IProjectScheduleService
{
    public async Task<ResultDto<ScheduledOutSourceShiftResponseDto>> CreateScheduledOutSourceShift(Guid projectScheduleId, CreateScheduledOutSourceShiftRequestDto dto)
    {
        var existSchedule = await projectScheduleRepository.GetById(projectScheduleId, new ScheduleQueryableOptions
        {
            IncludedProject = false,
            IncludedEmployeeShifts = false,
            IncludedOutSourceShifts = false
        });
        if (existSchedule is null)
        {
            logger.Error("Project schedule not found for schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }
        var newOutSourceShift = dto.ToEntity(existSchedule);
        if (newOutSourceShift is null)
        {
            logger.Error("Failed to create outsource shift on schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var createdOutSourceShift = await outSourceShiftRepository.Create(newOutSourceShift, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
            IncludedProjectSchedule = true,
        });
        if (createdOutSourceShift is null)
        {
            logger.Error("Failed to create outsource shift on schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var result = createdOutSourceShift.ToScheduledOutSourceShiftResponseDto();
        return new SuccessResultDto<ScheduledOutSourceShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledOutSourceShiftResponseDto>> CreateScheduledOutSourceShift(CreateScheduledOutSourceShiftRequestDto2 dto)
    {
        var existSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
            dto.ProjectId, dto.WorkingDate, new ScheduleQueryableOptions
            {
                IncludedProject = false,
                IncludedEmployeeShifts = false,
                IncludedOutSourceShifts = false
            });
        existSchedule ??= new ProjectSchedule
        {
            ProjectScheduleUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = dto.ProjectId,
            WorkingDate = dto.WorkingDate,
            PlannedWorkload = 0,
            PresignedWorkload = 0,
            IsDeleted = false,
        };

        var newOutSourceShift = dto.ToEntity(existSchedule);
        if (newOutSourceShift is null)
        {
            logger.Error("Failed to create outsource shift on project id {ProjectId} and date {WorkingDate}", dto.ProjectId, dto.WorkingDate);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var createdOutSourceShift = await outSourceShiftRepository.Create(newOutSourceShift, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
            IncludedProjectSchedule = true,
        });
        if (createdOutSourceShift is null)
        {
            logger.Error("Failed to create outsource shift on project id {ProjectId} and date {WorkingDate}", dto.ProjectId, dto.WorkingDate);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var result = createdOutSourceShift.ToScheduledOutSourceShiftResponseDto();
        return new SuccessResultDto<ScheduledOutSourceShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledOutSourceShiftResponseDto>> UpdateScheduledOutSourceShift(Guid outSourceShiftId, UpdateScheduledOutSourceShiftRequestDto dto)
    {
        var outSourceShift = await outSourceShiftRepository.GetById(outSourceShiftId, new OutSourceShiftQueryableOptions
        {
            IncludedProjectSchedule = true,
            IncludedOutSource = true
        });
        if (outSourceShift is null)
        {
            logger.Error("Out source shift not found for id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        var startWorkingTime = new DateTime(
            outSourceShift.ProjectSchedule.WorkingDate.Year,
            outSourceShift.ProjectSchedule.WorkingDate.Month,
            outSourceShift.ProjectSchedule.WorkingDate.Day,
            dto.StartTime.Hour, dto.StartTime.Minute, 0);
        outSourceShift.ScheduledStartTime = startWorkingTime;

        var endWorkingTime = new DateTime(
            outSourceShift.ProjectSchedule.WorkingDate.Year,
            outSourceShift.ProjectSchedule.WorkingDate.Month,
            outSourceShift.ProjectSchedule.WorkingDate.Day,
            dto.EndTime.Hour, dto.EndTime.Minute, 0);
        outSourceShift.ScheduledEndTime = endWorkingTime;

        outSourceShift.AssignedWorkload = dto.AssignedWorkload ?? outSourceShift.AssignedWorkload;
        outSourceShift.Role = dto.AssignedRole ?? outSourceShift.Role;

        var updatedOutSourceShift = await outSourceShiftRepository.Update(outSourceShift, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
            IncludedProjectSchedule = true,
        });
        if (updatedOutSourceShift is null)
        {
            logger.Error("Failed to update outsource shift for shift id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_UPDATE_FAILED);
        }

        var result = updatedOutSourceShift.ToScheduledOutSourceShiftResponseDto();
        return new SuccessResultDto<ScheduledOutSourceShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledOutSourceShiftResponseDto>> DuplicateScheduledOutSourceShift(Guid outSourceShiftId, DuplicateScheduledOutSourceShiftRequestDto dto)
    {

        var outSourceShift = await outSourceShiftRepository.GetById(outSourceShiftId, new OutSourceShiftQueryableOptions
        {
            IncludedProjectSchedule = true,
            IncludedOutSource = true
        });
        if (outSourceShift is null)
        {
            logger.Error("Out source shift not found for id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        var destinationSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
            dto.ProjectId, dto.WorkingDate, new ScheduleQueryableOptions
            {
                IncludedProject = false,
                IncludedEmployeeShifts = false,
                IncludedOutSourceShifts = false
            });
        if (destinationSchedule is null)
        {
            logger.Error("Project schedule not found for project id {ProjectId} at date {WorkingDate}",
                dto.ProjectId, dto.WorkingDate);
            var newProjectSchedule = new ProjectSchedule
            {
                ProjectUid = dto.ProjectId,
                WorkingDate = dto.WorkingDate,
                PlannedWorkload = 0,
                PresignedWorkload = 0,
            };
            var createdProjectSchedule = await projectScheduleRepository.Create(newProjectSchedule);
            if (createdProjectSchedule is null)
            {
                logger.Error("Failed to create project schedule for project id {ProjectId}", dto.ProjectId);
                return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
            }
            destinationSchedule = createdProjectSchedule;
        }

        var newScheduledStart = new DateTime(
            dto.WorkingDate.Year,
            dto.WorkingDate.Month,
            dto.WorkingDate.Day,
            outSourceShift.ScheduledStartTime.Hour,
            outSourceShift.ScheduledStartTime.Minute,
            0);

        var newScheduledEnd = new DateTime(
            dto.WorkingDate.Year,
            dto.WorkingDate.Month,
            dto.WorkingDate.Day,
            outSourceShift.ScheduledEndTime.Hour,
            outSourceShift.ScheduledEndTime.Minute,
            0);

        var newOutSourceShift = new OutSourceShift
        {
            OutSourceUid = outSourceShift.OutSourceUid,
            ProjectScheduleUid = destinationSchedule.ProjectScheduleUid,
            ScheduledStartTime = newScheduledStart,
            ScheduledEndTime = newScheduledEnd,
            AssignedWorkload = outSourceShift.AssignedWorkload,
            Role = outSourceShift.Role,
        };

        var createdOutSourceShift = await outSourceShiftRepository.Create(newOutSourceShift, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
            IncludedProjectSchedule = true,
        });
        if (createdOutSourceShift is null)
        {
            logger.Error("Failed to create outsource shift for shift id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<ScheduledOutSourceShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var result = createdOutSourceShift.ToScheduledOutSourceShiftResponseDto();
        return new SuccessResultDto<ScheduledOutSourceShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledOutSourceShiftResponsesDto>> DuplicateScheduledOutSourceShifts(Guid outSourceShiftId, DuplicateScheduledOutSourceShiftsRequestDto dto)
    {
        var response = new List<ScheduledOutSourceShiftResponseDto>();
        for (var workingDate = dto.WorkingDateFrom; workingDate <= dto.WorkingDateTo; workingDate = workingDate.AddDays(1))
        {
            var duplicatedShift = await DuplicateScheduledOutSourceShift(outSourceShiftId, new DuplicateScheduledOutSourceShiftRequestDto
            {
                ProjectId = dto.ProjectId,
                WorkingDate = workingDate,
            });
            if (duplicatedShift is null || duplicatedShift.Data is null)
            {
                logger.Error("Failed to duplicate shift for shift id {OutSourceShiftId}", outSourceShiftId);
                return new ErrorResultDto<ScheduledOutSourceShiftResponsesDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
            }
            response.Add(duplicatedShift.Data);
        }
        var result = new ScheduledOutSourceShiftResponsesDto
        {
            Items = response
        };
        return new SuccessResultDto<ScheduledOutSourceShiftResponsesDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteScheduledOutSourceShift(Guid outSourceShiftId)
    {
        var outSourceShift = await outSourceShiftRepository.GetById(outSourceShiftId, new OutSourceShiftQueryableOptions
        {
            IncludedProjectSchedule = true,
            IncludedOutSource = true
        });
        if (outSourceShift is null)
        {
            logger.Error("Out source shift not found for id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }
        outSourceShift.IsDeleted = true;
        var updatedOutSourceShift = await outSourceShiftRepository.Update(outSourceShift, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
            IncludedProjectSchedule = true,
        });
        if (updatedOutSourceShift is null)
        {
            logger.Error("Failed to delete outsource shift for shift id {OutSourceShiftId}", outSourceShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_REMOVE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}