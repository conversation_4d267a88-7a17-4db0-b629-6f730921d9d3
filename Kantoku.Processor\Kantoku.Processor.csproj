<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Kantoku.Processor</RootNamespace>
  </PropertyGroup>

  <ItemGroup>

    <PackageReference Include="FirebaseAdmin" Version="3.1.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <!-- Hangfire packages -->
    <PackageReference Include="Hangfire.Core" Version="1.8.14" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.14" />
    <PackageReference Include="Hangfire.Console" Version="1.4.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EventBus\EventBus.csproj" />
    <ProjectReference Include="..\EventBus.Kafka\EventBus.Kafka.csproj" />
    <ProjectReference Include="..\Kantoku.Persistence\Kantoku.Persistence.csproj" />
    <ProjectReference Include="..\Kantoku.SharedKernel\Kantoku.SharedKernel.csproj" />
  </ItemGroup>

</Project>
