namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for outsource price queries - contains only business logic, no HTTP concerns
/// </summary>
public class OutSourcePriceFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in outsource price details
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter prices from this start date (yyyy-MM-dd format)
    /// </summary>
    public string? StartDate { get; set; }

    /// <summary>
    /// Filter prices to this end date (yyyy-MM-dd format)
    /// </summary>
    public string? EndDate { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Validate date formats
        StartDate = ValidateDate(StartDate);
        EndDate = ValidateDate(EndDate);
    }

    private static string? ValidateDate(string? dateString)
    {
        if (!string.IsNullOrWhiteSpace(dateString) && !DateTime.TryParse(dateString, out _))
        {
            return null; // Invalid date, ignore
        }
        return dateString;
    }
}