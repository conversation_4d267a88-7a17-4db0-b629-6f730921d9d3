namespace Kantoku.Processor.Services.Interfaces;

/// <summary>
/// Service for geocoding and reverse geocoding
/// </summary>
public interface IGeoCodeService
{
    /// <summary>
    /// Get coordinates for an address
    /// </summary>
    Task<Coordinates?> GetCoordinatesAsync(string? address, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get address for coordinates
    /// </summary>
    Task<string?> GetAddressAsync(double latitude, double longitude, CancellationToken cancellationToken = default);
}

public record Coordinates(double Latitude, double Longitude);
