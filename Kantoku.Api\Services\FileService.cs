using File = Kantoku.Domain.Models.File;
using Kantoku.Persistence.Repositories;
using Minio;
using Kantoku.SharedKernel.Helpers;
using Minio.DataModel.Args;
using Kantoku.Api.Utils.Constants;
using Microsoft.Extensions.Options;
using Kantoku.Api.Factories.Stream;
using Microsoft.AspNetCore.StaticFiles;
using Kantoku.Api.Dtos.File;
using Minio.Exceptions;
using Minio.DataModel;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.Base;

namespace Kantoku.Api.Services;

public interface IFileService
{
    Task<ResultDto<FileMetadataResponseDto>> GetFileMetadataAsync(string fileUrl, string? bucketName = null);
    Task<ResultDto<FilesMetadataResponseDto>> ListFilesAsync(string path, int pageNum, int pageSize);
    Task<ResultDto<FileMetadataResponseDto>> UploadFileAsync(IFormFile file, string objectName, string? bucketName = null, Dictionary<string, string?>? userMetadata = null);
    Task<ResultDto<FileMetadataResponseDto>> UploadFileAsync(Stream stream, string objectName, string? bucketName = null, Dictionary<string, string?>? userMetadata = null);
    Task<ResultDto<FilesMetadataResponseDto>> UploadFilesAsync(IEnumerable<IFormFile> files, string path, string? bucketName = null, Func<IFormFile, Dictionary<string, string?>>? userMetadatas = null);
    Task<ResultDto<MemoryStream>> DownloadFileAsStream(string fileUrl, string? bucketName = null);
    Task<ResultDto<FileResponseDto>> DownloadFile(string fileUrl, string? bucketName = null);
    Task<ResultDto<bool>> DeleteFileAsync(string fileUrl, string? bucketName = null);
    Task<ResultDto<bool>> DeleteFilesAsync(IEnumerable<string> fileUrls, string? bucketName = null);
}

[Service(ServiceLifetime.Scoped)]
public class FileService : BaseService<FileService>, IFileService
{
    private readonly IMinioClient minioClient;
    private readonly IFileRepository fileRepository;

    public FileService(Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IOptions<Configurations.MinioConfig> minioConfig,
        IFileRepository fileRepository)
        : base(logger, httpContextAccessor)
    {
        this.fileRepository = fileRepository;
        minioClient = new MinioClient()
                .WithEndpoint(minioConfig.Value.Endpoint)
                .WithCredentials(minioConfig.Value.AccessKey, minioConfig.Value.SecretKey)
                .Build();
    }

    public async Task<ResultDto<FileMetadataResponseDto>> GetFileMetadataAsync(string fileUrl, string? bucketName = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        var statObjectArgs = new StatObjectArgs()
            .WithBucket(bucketName)
            .WithObject(fileUrl);

        try
        {
            var objectStat = await minioClient.StatObjectAsync(statObjectArgs);
            if (objectStat is null)
            {
                logger.Error("File metadata not found for {FileUrl}", fileUrl);
                return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_METADATA_NOT_EXIST);
            }
            return new SuccessResultDto<FileMetadataResponseDto>(FileMetadataResponseDtoParser(objectStat));
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error occurred while getting file metadata for {FileUrl}", fileUrl);
            return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_METADATA_NOT_EXIST);
        }
    }

    public async Task<ResultDto<FilesMetadataResponseDto>> ListFilesAsync(string path, int pageNum, int pageSize)
    {

        var (files, totalRecords) = await fileRepository.GetByPath(path, pageNum, pageSize);
        if (files is null || !files.Any() || totalRecords == 0)
        {
            logger.Error("No files found for path: {Path}", path);
            return new ErrorResultDto<FilesMetadataResponseDto>(ResponseCodeConstant.FILE_METADATA_NOT_EXIST);
        }
        var result = new FilesMetadataResponseDto
        {
            Items = files.Select(FileMetadataResponseDtoParser),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
        return new SuccessResultDto<FilesMetadataResponseDto>(result);
    }


    public async Task<ResultDto<FilesMetadataResponseDto>> UploadFilesAsync(
        IEnumerable<IFormFile> files,
        string path,
        string? bucketName = null,
        Func<IFormFile, Dictionary<string, string?>>? userMetadatas = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        var results = new List<FileMetadataResponseDto>();
        var tasks = new List<Task<UploadFileResponseDto>>();

        foreach (var file in files)
        {
            var objectName = path + file.FileName;
            var metadata = userMetadatas?.Invoke(file) ?? new Dictionary<string, string?>
            {
                ["originalFileName"] = file.FileName,
                ["contentType"] = file.ContentType,
                ["uploadedAt"] = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            tasks.Add(UploadFileToMinio(file, objectName, bucketName, metadata));
        }

        try
        {
            var uploadResults = await Task.WhenAll(tasks);
            var fileMetadataResponseDtos = await VerifyAndCreateMetadatas(uploadResults);
            results.AddRange(fileMetadataResponseDtos);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error during batch file upload");
            return new ErrorResultDto<FilesMetadataResponseDto>(ResponseCodeConstant.FILE_UPLOAD_FAILED);
        }

        var result = new FilesMetadataResponseDto
        {
            Items = results,
            PageNum = 1,
            PageSize = results.Count,
            TotalRecords = results.Count
        };
        return new SuccessResultDto<FilesMetadataResponseDto>(result);
    }

    public async Task<ResultDto<FileMetadataResponseDto>> UploadFileAsync(
        IFormFile file,
        string objectName,
        string? bucketName = null,
        Dictionary<string, string?>? userMetadata = null)
    {
        var uploadFileResponseDto = await UploadFileToMinio(file, objectName, bucketName, userMetadata);
        if (uploadFileResponseDto is null || uploadFileResponseDto.ExpectedSize == 0)
        {
            logger.Error("Failed to upload file to Minio");
            return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_UPLOAD_FAILED);
        }
        var verifyResult = await VerifyAndCreateMetadatas(new[] { uploadFileResponseDto });
        if (verifyResult is null || !verifyResult.Any())
        {
            logger.Error("Failed to create file metadata for {ObjectName}", objectName);
            return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_UPLOAD_FAILED);
        }
        return new SuccessResultDto<FileMetadataResponseDto>(verifyResult.First());
    }

    public async Task<ResultDto<FileMetadataResponseDto>> UploadFileAsync(
        Stream stream,
        string objectName,
        string? bucketName = null,
        Dictionary<string, string?>? userMetadata = null)
    {
        var uploadFileResponseDto = await UploadFileToMinio(stream, objectName, bucketName, userMetadata);
        if (uploadFileResponseDto is null || uploadFileResponseDto.ExpectedSize == 0)
        {
            logger.Error("Failed to upload file to Minio");
            return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_UPLOAD_FAILED);
        }
        var verifyResult = await VerifyAndCreateMetadatas(new[] { uploadFileResponseDto });
        if (verifyResult is null || !verifyResult.Any())
        {
            logger.Error("Failed to create file metadata for {ObjectName}", objectName);
            return new ErrorResultDto<FileMetadataResponseDto>(ResponseCodeConstant.FILE_UPLOAD_FAILED);
        }
        return new SuccessResultDto<FileMetadataResponseDto>(verifyResult.First());
    }

    public async Task<ResultDto<MemoryStream>> DownloadFileAsStream(string fileUrl, string? bucketName = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        var fileMetadata = await GetFileMetadataAsync(fileUrl, bucketName);
        if (fileMetadata is null || fileMetadata.Data is null)
        {
            logger.Error("File metadata not found for {FileUrl}", fileUrl);
            return new ErrorResultDto<MemoryStream>(ResponseCodeConstant.FILE_METADATA_NOT_EXIST);
        }

        var dataStream = RecyclableStreamFactory.GetStream(nameof(FileService));
        try
        {
            var getObjectArgs = new GetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(fileMetadata.Data.FileUrl)
                .WithCallbackStream(async (stream, cancellationToken) =>
                {
                    await stream.CopyToAsync(dataStream, cancellationToken);
                    dataStream.Position = 0;
                });

            var objectStream = await minioClient.GetObjectAsync(getObjectArgs);
        }
        catch (MinioException ex)
        {
            logger.Error(ex, "Error occurred while downloading file from Minio");
            return new ErrorResultDto<MemoryStream>(ResponseCodeConstant.FILE_DOWNLOAD_FAILED);
        }
        return new SuccessResultDto<MemoryStream>(dataStream);
    }

    public async Task<ResultDto<FileResponseDto>> DownloadFile(string fileUrl, string? bucketName = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        var fileMetadata = await GetFileMetadataAsync(fileUrl, bucketName);
        if (fileMetadata is null || fileMetadata.Data is null)
        {
            logger.Error("File metadata not found for {FileUrl}", fileUrl);
            return new ErrorResultDto<FileResponseDto>(ResponseCodeConstant.FILE_METADATA_NOT_EXIST);
        }
        var dataStream = await DownloadFileAsStream(fileUrl, bucketName);
        if (dataStream is null || dataStream.Data is null)
        {
            logger.Error("File data not found for {FileUrl}", fileUrl);
            return new ErrorResultDto<FileResponseDto>(ResponseCodeConstant.FILE_DOWNLOAD_FAILED);
        }
        var result = new FileResponseDto
        {
            FileName = fileMetadata.Data.FileName,
            FileType = fileMetadata.Data.FileType ?? MimeTypeConstant.DefaultMimeType,
            DataAsBytes = dataStream.Data.ToArray(),
            DataAsBase64 = Convert.ToBase64String(dataStream.Data.ToArray()),
            Metadata = fileMetadata.Data.Metadata
        };
        return new SuccessResultDto<FileResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteFileAsync(string fileUrl, string? bucketName = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();

        try
        {
            var removeObjectArgs = new RemoveObjectArgs()
                .WithBucket(bucketName)
                .WithObject(fileUrl);
            await minioClient.RemoveObjectAsync(removeObjectArgs);
        }
        catch (MinioException ex)
        {
            logger.Error(ex, "Error occurred while deleting file from Minio");
            return new ErrorResultDto<bool>(ResponseCodeConstant.FILE_DELETE_FAILED);
        }

        var isDeleted = await fileRepository.Delete(fileUrl);
        if (!isDeleted)
        {
            logger.Error("Failed to delete file metadata from database");
            return new ErrorResultDto<bool>(ResponseCodeConstant.FILE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteFilesAsync(IEnumerable<string> fileUrls, string? bucketName = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        var args = new RemoveObjectsArgs()
            .WithBucket(bucketName)
            .WithObjects(fileUrls.ToList());

        try
        {
            var errors = await minioClient.RemoveObjectsAsync(args);
            if (errors.Any())
            {
                logger.Error("Failed to delete files from Minio");
                return new ErrorResultDto<bool>(ResponseCodeConstant.FILE_DELETE_FAILED);
            }
        }
        catch (MinioException ex)
        {
            logger.Error(ex, "Error occurred while deleting files from Minio");
            return new ErrorResultDto<bool>(ResponseCodeConstant.FILE_DELETE_FAILED);
        }
        var isDeleted = await fileRepository.Delete(fileUrls);
        if (!isDeleted)
        {
            logger.Error("Failed to delete file metadata from database");
            return new ErrorResultDto<bool>(ResponseCodeConstant.FILE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<UploadFileResponseDto> UploadFileToMinio(
        IFormFile file,
        string objectName,
        string? bucketName = null,
        Dictionary<string, string?>? userMetadata = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();
        try
        {
            var putObjectArgs = new PutObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithStreamData(file.OpenReadStream())
                .WithObjectSize(file.Length)
                .WithContentType(file.ContentType)
                .WithHeaders(userMetadata ?? new Dictionary<string, string?>
                {
                    { "originalFileName", file.FileName },
                    { "uploadedAt", DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ") }
                });

            var uploadResult = await minioClient.PutObjectAsync(putObjectArgs);
            return new UploadFileResponseDto
            {
                BucketName = bucketName,
                ObjectName = objectName,
                Metadata = userMetadata ?? [],
                ExpectedSize = file.Length,
                ContentType = file.ContentType
            };
        }
        catch (MinioException ex)
        {
            logger.Error(ex, "Error occurred while uploading file to Minio");
            return new UploadFileResponseDto
            {
                BucketName = bucketName,
                ObjectName = objectName,
                Metadata = userMetadata ?? [],
                ExpectedSize = 0,
                ContentType = file.ContentType
            };
        }
    }

    private async Task<UploadFileResponseDto> UploadFileToMinio(
        Stream stream,
        string objectName,
        string? bucketName = null,
        Dictionary<string, string?>? userMetadata = null)
    {
        bucketName ??= GetCurrentOrgUid().ToString();

        long streamLength;
        string contentType = GetMimeType(objectName);
        try
        {
            if (stream.CanSeek)
            {
                var initialPosition = stream.Position;
                stream.Position = 0;
                streamLength = stream.Length;
                stream.Position = initialPosition;
            }
            else
            {
                // For non-seekable streams, buffer to get length
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                streamLength = memoryStream.Length;
                stream = memoryStream;
                stream.Position = 0;
            }

            var putObjectArgs = new PutObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithStreamData(stream)
                .WithObjectSize(streamLength)
                .WithContentType(contentType)
                .WithHeaders(userMetadata ?? new Dictionary<string, string?>
                {
                    ["contentType"] = contentType,
                    ["uploadedAt"] = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ")
                });

            await minioClient.PutObjectAsync(putObjectArgs);
            return new UploadFileResponseDto
            {
                BucketName = bucketName,
                ObjectName = objectName,
                Metadata = userMetadata ?? [],
                ExpectedSize = streamLength,
                ContentType = contentType
            };
        }
        catch (MinioException ex)
        {
            logger.Error(ex, "Error uploading file from stream: {ObjectName}", objectName);
            return new UploadFileResponseDto
            {
                BucketName = bucketName,
                ObjectName = objectName,
                Metadata = userMetadata ?? [],
                ExpectedSize = 0,
                ContentType = contentType
            };
        }
    }

    private async Task<IEnumerable<FileMetadataResponseDto>> VerifyAndCreateMetadatas(IEnumerable<UploadFileResponseDto> uploadFileResponseDtos)
    {
        var statObjectTasks = new List<Task<ObjectStat>>();
        var cleanupTasks = new List<Task>();
        var fileEntities = new List<File>();

        var uploadedFileMap = uploadFileResponseDtos.ToDictionary(
            dto => dto.ObjectName,
            dto => dto
        );

        var statObjectArgs = uploadFileResponseDtos.Select(dto => new StatObjectArgs()
            .WithBucket(dto.BucketName)
            .WithObject(dto.ObjectName));

        statObjectTasks.AddRange(statObjectArgs.Select(args => minioClient.StatObjectAsync(args)));

        try
        {
            var objectStatResults = await Task.WhenAll(statObjectTasks);
            foreach (var objectStatResult in objectStatResults)
            {
                if (objectStatResult.Size == uploadedFileMap[objectStatResult.ObjectName].ExpectedSize)
                {
                    fileEntities.Add(new File
                    {
                        FileUid = GuidHelper.GenerateUUIDv7(),
                        FileName = Path.GetFileName(uploadedFileMap[objectStatResult.ObjectName].ObjectName),
                        FileType = uploadedFileMap[objectStatResult.ObjectName].ContentType,
                        FileSize = objectStatResult.Size,
                        FileUrl = uploadedFileMap[objectStatResult.ObjectName].ObjectName,
                        FileMetadata = uploadedFileMap[objectStatResult.ObjectName].Metadata
                    });
                }
                else
                {
                    var bucketName = uploadedFileMap[objectStatResult.ObjectName].BucketName;
                    cleanupTasks.Add(CleanupMinioFile(bucketName, uploadedFileMap[objectStatResult.ObjectName].ObjectName));
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "File upload verification failed");
            cleanupTasks.AddRange(uploadFileResponseDtos.Select(dto => CleanupMinioFile(dto.BucketName, dto.ObjectName)));
            await Task.WhenAll(cleanupTasks);
            return [];
        }

        var createdFileMetadata = await fileRepository.Upsert(fileEntities);
        if (createdFileMetadata is null || !createdFileMetadata.Any())
        {
            logger.Error("Failed to create file metadata");
            return [];
        }

        return createdFileMetadata.Select(FileMetadataResponseDtoParser);
    }

    private async Task CleanupMinioFile(string bucketName, string fileUrl)
    {
        try
        {
            await minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(bucketName)
                .WithObject(fileUrl));
            logger.Information("Successfully cleaned up Minio file after failure: {FileUrl}", fileUrl);
        }
        catch (MinioException cleanupEx)
        {
            logger.Error(cleanupEx, "Failed to cleanup Minio file: {FileUrl}", fileUrl);
        }
    }

    private static string GetMimeType(string fileName)
    {
        var provider = new FileExtensionContentTypeProvider();
        if (!provider.TryGetContentType(fileName, out var contentType))
        {
            contentType = MimeTypeConstant.DefaultMimeType;
        }
        return contentType;
    }

    private FileMetadataResponseDto FileMetadataResponseDtoParser(ObjectStat objectStat)
    {
        try
        {
            return new FileMetadataResponseDto
            {
                FileId = objectStat.ObjectName,
                FileName = objectStat.ObjectName,
                FileSize = objectStat.Size,
                FileType = objectStat.ContentType,
                FileUrl = objectStat.ObjectName,
                Metadata = objectStat.MetaData
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error occurred while mapping file info for {FileName}", objectStat.ObjectName);
            return new FileMetadataResponseDto();
        }
    }

    private FileMetadataResponseDto FileMetadataResponseDtoParser(File file)
    {
        try
        {
            return new FileMetadataResponseDto
            {
                FileId = file.FileUid.ToString(),
                FileName = file.FileName,
                FileSize = file.FileSize,
                FileType = file.FileType,
                FileUrl = file.FileUrl,
                Metadata = file.FileMetadata,
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error occurred while mapping file info for {FileName}", file.FileName);
            return new FileMetadataResponseDto();
        }
    }
}
