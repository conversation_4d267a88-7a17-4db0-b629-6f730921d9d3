using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class UserInfoQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedAccount { get; set; } = false;
}

public interface IUserInfoQueryable
{
    IQueryable<UserInfo> GetUserInfoQuery(
        UserInfoQueryableOptions options
    );
    IQueryable<UserInfo> GetUserInfoQueryIncluded(
        UserInfoQueryableOptions options,
        IQueryable<UserInfo>? query = null
    );
}

public class UserInfoQueryable(ApplicationDbContext context) :
    BaseQueryable<UserInfo>(context), IUserInfoQueryable
{
    public IQueryable<UserInfo> GetUserInfoQuery(
        UserInfoQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<UserInfo> GetUserInfoQueryIncluded(
        UserInfoQueryableOptions options,
        IQueryable<UserInfo>? query = null
    )
    {
        query ??= GetUserInfoQuery(options);

        if (options.IncludedAccount || options.IncludedAll)
        {
            query = query.Include(s => s.Account);
        }

        return query;
    }
}
