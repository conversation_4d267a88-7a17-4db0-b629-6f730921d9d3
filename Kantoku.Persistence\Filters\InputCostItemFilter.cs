﻿namespace Kantoku.Persistence.Filters;

public class InputCostItemCategoryFilter : InputCostItemFilter
{
    /// <summary>
    /// The category ID
    /// </summary>
    public Guid? CategoryId { get; set; }
}

public class InputCostItemFilter : BaseFilter
{
    /// <summary>
    /// The project ID
    /// </summary>
    public Guid? ProjectId { get; set; }

    /// <summary>
    /// The construction ID
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// The input cost ID
    /// </summary>
    public Guid? InputCostId { get; set; }

    /// <summary>
    /// The vendor ID
    /// </summary>
    public Guid? VendorId { get; set; }

    /// <summary>
    /// The date from
    /// </summary>
    public string? DateFrom { get; set; }

    /// <summary>
    /// The date to
    /// </summary>
    public string? DateTo { get; set; }
}
