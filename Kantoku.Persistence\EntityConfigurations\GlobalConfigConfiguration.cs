using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class GlobalConfigConfiguration(string schema) : IEntityTypeConfiguration<GlobalConfig>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<GlobalConfig> builder)
    {
        builder.HasKey(e => e.Key).HasName("global_config_pkey");

        builder.ToTable("global_config", Schema, tb => tb.HasComment("Thông tin cấu hình chung"));

        builder.Property(e => e.Key)
            .HasColumnName("key");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.Value)
            .HasColumnName("value");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org).WithMany(p => p.GlobalConfigs)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("global_config_org_id_fkey");
    }
}
