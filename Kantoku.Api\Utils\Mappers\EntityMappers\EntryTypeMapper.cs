using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.EntryType.Request;
using Kantoku.Api.Dtos.EntryType.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EntryTypeMapper
{
    #region Entity to DTO mappings

    // Map EntryType entity to EntryTypeResponseDto
    public static EntryTypeResponseDto ToEntryTypeResponseDto(this EntryType entryType)
    {
        if (entryType == null)
            return new EntryTypeResponseDto();

        return new EntryTypeResponseDto
        {
            EntryTypeId = entryType.EntryTypeUid.ToString(),
            EntryTypeName = entryType.EntryTypeName,
            Description = entryType.Description
        };
    }

    // Map collection of EntryType entities to collection of EntryTypeResponseDto
    public static EntryTypesResponseDto ToEntryTypesResponseDto(this IEnumerable<EntryType> entryTypes, int pageNum, int pageSize, int totalRecords)
    {
        if (entryTypes == null)
            return new EntryTypesResponseDto();

        return new EntryTypesResponseDto
        {
            EntryTypes = entryTypes.Select(et => et.ToEntryTypeResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    // Map CreateEntryTypeRequestDto to EntryType entity
    public static EntryType? ToEntity(this CreateEntryTypeRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new EntryType
        {
            EntryTypeUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            EntryTypeName = dto.EntryTypeName,
            Description = dto.Description,
            IsDeleted = false
            // Assuming AuditableEntity fields (CreatedAt, CreatedBy, etc.) are handled elsewhere
        };

        return entity;
    }

    // Map UpdateEntryTypeRequestDto to EntryType entity
    public static void UpdateFromDto(this EntryType entity, UpdateEntryTypeRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.EntryTypeName != null)
            entity.EntryTypeName = dto.EntryTypeName;

        if (dto.Description != null) // Check if Description is provided in the DTO
            entity.Description = dto.Description;
        else if (dto.Description == null && entity.Description != null) // If DTO Description is null, potentially clear existing Description
             entity.Description = null; // Or keep existing if null means no change - clarify requirement if needed

        // Assuming AuditableEntity fields (ModifiedAt, ModifiedBy) are handled elsewhere
    }

    #endregion
} 