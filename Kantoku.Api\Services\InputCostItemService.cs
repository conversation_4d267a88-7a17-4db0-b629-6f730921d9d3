﻿
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.InputCostItem.Request;
using Kantoku.Api.Dtos.InputCostItem.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using DomainFilter = Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IInputCostItemService
{
    Task<ResultDto<InputCostItemResponseDto>> GetById(Guid inputCostItemId);
    Task<ResultDto<InputCostItemsResponseDto>> GetByFilter(ApiFilter.InputCostItemCategoryFilter filter);
    Task<ResultDto<InputCostItemsResponseDto>> GetByCategory(Guid categoryId, ApiFilter.InputCostItemFilter filter);
    Task<ResultDto<InputCostItemResponseDto>> Create(CreateInputCostItemRequestDto requestDto);
    Task<ResultDto<InputCostItemResponseDto>> Update(Guid inputCostItemId, UpdateInputCostItemRequestDto requestDto);
    Task<ResultDto<bool>> UpdateMultiple(UpdateMultipleInputCostItemsRequestDto requestDtos);
    Task<ResultDto<bool>> Delete(Guid inputCostItemId);
}

[Service(ServiceLifetime.Scoped)]
public class InputCostItemService : BaseService<InputCostItemService>, IInputCostItemService
{
    private readonly IInputCostItemRepository inputCostItemRepository;
    private readonly ICategoryRepository categoryRepository;
    private readonly IOutSourceShiftRepository outSourceShiftRepository;
    private readonly IOutSourcePriceRepository outSourcePriceRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IProjectDailyReportRepository projectDailyReportRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IOutSourceRepository outSourceRepository;
    private readonly IFilterMapper<ApiFilter.InputCostItemCategoryFilter, DomainFilter.InputCostItemCategoryFilter> inputCostItemCategoryFilterMapper;
    private readonly IFilterMapper<ApiFilter.InputCostItemFilter, DomainFilter.InputCostItemFilter> inputCostItemFilterMapper;
    public InputCostItemService(
        IInputCostItemRepository inputCostItemRepository,
        ICategoryRepository categoryRepository,
        IOutSourceShiftRepository outSourceShiftRepository,
        IOutSourcePriceRepository outSourcePriceRepository,
        IRequestRepository requestRepository,
        IProjectDailyReportRepository projectDailyReportRepository,
        IEmployeeRepository employeeRepository,
        IOutSourceRepository outSourceRepository,
        IFilterMapper<ApiFilter.InputCostItemCategoryFilter, DomainFilter.InputCostItemCategoryFilter> inputCostItemCategoryFilterMapper,
        IFilterMapper<ApiFilter.InputCostItemFilter, DomainFilter.InputCostItemFilter> inputCostItemFilterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.inputCostItemRepository = inputCostItemRepository;
        this.categoryRepository = categoryRepository;
        this.outSourceShiftRepository = outSourceShiftRepository;
        this.outSourcePriceRepository = outSourcePriceRepository;
        this.requestRepository = requestRepository;
        this.projectDailyReportRepository = projectDailyReportRepository;
        this.employeeRepository = employeeRepository;
        this.outSourceRepository = outSourceRepository;
        this.inputCostItemCategoryFilterMapper = inputCostItemCategoryFilterMapper;
        this.inputCostItemFilterMapper = inputCostItemFilterMapper;
    }

    public async Task<ResultDto<InputCostItemsResponseDto>> GetByFilter(ApiFilter.InputCostItemCategoryFilter filter)
    {
        var domainFilter = inputCostItemCategoryFilterMapper.MapToDomain(filter);
        var (total, inputCostItems) = await inputCostItemRepository.GetByFilter(domainFilter, new InputCostItemQueryableOptions
        {
            IncludedItem = true,
            IncludedVendor = true,
        });
        if (inputCostItems.Any() == false || total == 0)
        {
            logger.Information("No input cost item found");
            return new ErrorResultDto<InputCostItemsResponseDto>(ResponseCodeConstant.INPUTCOSTITEM_NOT_EXIST);
        }

        var result = inputCostItems.ToInputCostItemsResponseDto(total, filter.PageNum, filter.PageSize);
        return new SuccessResultDto<InputCostItemsResponseDto>(result);
    }

    public async Task<ResultDto<InputCostItemsResponseDto>> GetByCategory(Guid categoryId, ApiFilter.InputCostItemFilter filter)
    {
        var domainFilter = inputCostItemFilterMapper.MapToDomain(filter);
        var category = await categoryRepository.GetById(categoryId, new CategoryQueryableOptions());
        if (category is null)
        {
            logger.Error("Category not found");
            return new ErrorResultDto<InputCostItemsResponseDto>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }

        if (category.CategoryCode == CategoryConstant.EMPLOYEE)
        {
            return await GetEmployeeCost(filter);
        }
        if (category.CategoryCode == CategoryConstant.OUTSOURCE_DAILY)
        {
            return await GetOutsourceCost(filter);
        }
        //TODO: Implement overtime cost
        // if (category.CategoryCode == CategoryConstant.OVERTIME)
        // {
        //     return await GetOvertimeCost(filter);
        // }
        var categoryFilter = new ApiFilter.InputCostItemCategoryFilter
        {
            ProjectId = filter.ProjectId,
            ConstructionId = filter.ConstructionId,
            InputCostId = filter.InputCostId,
            VendorId = filter.VendorId,
            DateFrom = filter.DateFrom,
            DateTo = filter.DateTo,
            CategoryId = categoryId,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
        };
        return await GetByFilter(categoryFilter);
    }

    private async Task<ResultDto<InputCostItemsResponseDto>> GetEmployeeCost(ApiFilter.InputCostItemFilter filter)
    {
        var domainFilter = new DomainFilter.ProjectDailyReportFilter
        {
            ProjectId = filter.ProjectId,
            ReportFrom = filter.DateFrom,
            ReportTo = filter.DateTo,
        };
        var (dailyReports, _) = await projectDailyReportRepository.GetByFilter(
            domainFilter,
            new ProjectDailyReportQueryableOptions()
        );
        if (dailyReports.Any() == false)
        {
            logger.Information("No daily report found");
            return new ErrorResultDto<InputCostItemsResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST);
        }
        var employeeUids = dailyReports.SelectMany(dr => dr.EmployeeWorkload ?? []).Select(e => e.EmployeeUid).Distinct().ToList();

        var employees = await employeeRepository.GetByIds(employeeUids, new EmployeeQueryableOptions
        {
            IncludedEmployeeRanks = true,
            IncludedEmployeeCosts = true,
        });

        var employeeCosts = employees.Join(
            dailyReports.SelectMany(dr => dr.EmployeeWorkload ?? []),
            e => e.EmployeeUid,
            ew => ew.EmployeeUid,
            (e, ew) => new
            {
                e.EmployeeUid,
                e.Ranking?.RankingName,
                ew.MainConsWorkload,
                ew.SubConsWorkload,
                e.Ranking?.AverageValue,
                e.EmployeeCosts.OrderByDescending(ec => ec.EffectiveDate)
                    .Where(ec => ec.EffectiveDate <= DateOnly.Parse(filter.DateTo ?? DateTime.Now.ToString("yyyy-MM-dd")))
                    .Where(ec => ec.EffectiveDate >= DateOnly.Parse(filter.DateFrom ?? DateTime.Now.ToString("yyyy-MM-dd")))
                    .FirstOrDefault()?.DailyCostAmount,
            }).ToList();

        var calculatedEmployeeCosts = employeeCosts
            .Select(ec => new InputCostItemResponseDto
            {
                ItemName = ec.RankingName,
                Quantity = ec.MainConsWorkload + ec.SubConsWorkload,
                Price = ec.DailyCostAmount,
                AveragePrice = ec.AverageValue,
                TotalNonTaxed = (long)(ec.MainConsWorkload + ec.SubConsWorkload) * ec.DailyCostAmount,
                TotalAverageAmount = (long)(ec.MainConsWorkload + ec.SubConsWorkload) * ec.AverageValue,
            }).ToList();

        var employeeCostsByRank = calculatedEmployeeCosts
            .GroupBy(ec => ec.ItemName, (key, ec) => new InputCostItemResponseDto
            {
                ItemName = key,
                Quantity = ec.Sum(e => e.Quantity),
                Price = (int?)Math.Round(ec.Average(e => e.Price ?? 0)),
                AveragePrice = (int?)Math.Round(ec.Average(e => e.AveragePrice ?? 0)),
                TotalAverageAmount = ec.Sum(e => e.TotalAverageAmount),
                TotalNonTaxed = ec.Sum(e => e.TotalNonTaxed),
            }).ToList();

        var result = new InputCostItemsResponseDto
        {
            Items = employeeCostsByRank,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            TotalRecords = employeeCostsByRank.Count
        };

        return new SuccessResultDto<InputCostItemsResponseDto>(result);
    }

    private async Task<ResultDto<InputCostItemsResponseDto>> GetOutsourceCost(ApiFilter.InputCostItemFilter filter)
    {
        var domainFilter = new DomainFilter.ProjectDailyReportFilter
        {
            ProjectId = filter.ProjectId,
            ReportFrom = filter.DateFrom,
            ReportTo = filter.DateTo,
        };
        var (dailyReports, _) = await projectDailyReportRepository.GetByFilter(
            domainFilter,
            new ProjectDailyReportQueryableOptions()
        );
        if (dailyReports.Any() == false)
        {
            logger.Information("No daily report found");
            return new ErrorResultDto<InputCostItemsResponseDto>(ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST);
        }
        var outSourceIds = dailyReports.SelectMany(dr => dr.OutSourceWorkload?.Select(os => os.OutSourceUid) ?? []).Distinct().ToList();

        var outSources = await outSourceRepository.GetByIds(outSourceIds, new OutSourceQueryableOptions
        {
            IncludedOutSourcePrice = true,
        });

        var outSourceCosts = outSources.Join(
            dailyReports.SelectMany(dr => dr.OutSourceWorkload ?? []),
            os => os.OutSourceUid,
            oc => oc.OutSourceUid,
            (os, oc) => new
            {
                os.OutSourceUid,
                os.OutSourceName,
                oc.MainConsWorkload,
                oc.SubConsWorkload,
                os.OutSourcePrices.OrderByDescending(op => op.EffectiveDate)
                    .Where(op => op.EffectiveDate <= DateOnly.Parse(filter.DateTo ?? DateTime.Now.ToString("yyyy-MM-dd")))
                    .Where(op => op.EffectiveDate >= DateOnly.Parse(filter.DateFrom ?? DateTime.Now.ToString("yyyy-MM-dd")))
                    .FirstOrDefault()?.PricePerDay,
            }).ToList();

        var outSourceCostsByOutSource = outSourceCosts
            .GroupBy(os => os.OutSourceName)
            .Select(group => new InputCostItemResponseDto
            {
                ItemName = group.Key,
                Quantity = group.Sum(os => os.MainConsWorkload + os.SubConsWorkload),
                Price = (int?)Math.Round(group.Average(os => os.PricePerDay ?? 0)),
                TotalNonTaxed = (long)group.Sum(os => (os.MainConsWorkload + os.SubConsWorkload) * (os.PricePerDay ?? 0)),
            }).ToList();

        var result = new InputCostItemsResponseDto
        {
            Items = outSourceCostsByOutSource,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            TotalRecords = outSourceCostsByOutSource.Count
        };
        return new SuccessResultDto<InputCostItemsResponseDto>(result);
    }

    //TODO: Implement overtime cost
    // private async Task<ResultDto<InputCostItemsResponseDto>> GetOvertimeCost(InputCostItemFilter filter)
    // {
    //     var (otRequests, _) = await requestRepository.GetByFilter(new RequestFilter
    //     {
    //         FromDate = filter.DateFrom,
    //         ToDate = filter.DateTo,
    //         RequestTypeCode = RequestTypeConstants.OVERTIME,
    //         StatusCode = StatusConstants.APPROVED,
    //     }, new RequestQueryableOptions
    //     {
    //         IncludedAuthor = true,
    //     });
    //     if (otRequests.Any() == false)
    //     {
    //         logger.Information("No overtime request found");
    //         return new ErrorResultDto<InputCostItemsResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
    //     }
    //     var authorIds = otRequests.Select(r => r.AuthorUid).Distinct().ToList();
    //     var authors = await employeeRepository.GetByIds(authorIds, new EmployeeQueryableOptions());

    //     var otCosts = otRequests.Join(
    //         authors,
    //         r => r.AuthorUid,
    //         e => e.EmployeeUid,
    //         (r, e) => new
    //         {
    //             e.EmployeeName,
    //             r.RequestFrom,
    //             r.RequestTo,

    //         })
    //         .ToList();

    //     foreach (var otCost in otCosts)
    //     {
    //         var employee = otCost.Employee;

    //         var overtimeCosts = new InputCostItemResponseDto
    //         {
    //             ItemName = employee.EmployeeName,
    //             Quantity = (float)requestAmount,
    //             Price = approximateHourlyEmployeeCost,
    //             TotalNonTaxed = (long)(requestAmount * approximateHourlyEmployeeCost),
    //         };
    //         totalCostsByRank.Add(overtimeCosts);
    //     }

    // }

    public async Task<ResultDto<InputCostItemResponseDto>> GetById(Guid inputCostItemId)
    {
        var inputCostItem = await inputCostItemRepository.GetById(inputCostItemId, new InputCostItemQueryableOptions
        {
            IncludedItem = true,
            IncludedVendor = true,
        });
        if (inputCostItem is null)
        {
            logger.Error("Input cost item not found");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INPUTCOSTITEM_NOT_EXIST);
        }
        var result = inputCostItem.ToInputCostItemResponseDto();
        return new SuccessResultDto<InputCostItemResponseDto>(result);
    }

    public async Task<ResultDto<InputCostItemResponseDto>> Create(CreateInputCostItemRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Invalid org");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newInputCostItem = requestDto.ToEntity(orgUid);
        if (newInputCostItem is null)
        {
            logger.Error("Invalid input cost item");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INPUTCOSTITEM_CREATE_FAILED);
        }

        var createdInputCostItem = await inputCostItemRepository.Create(newInputCostItem);
        if (createdInputCostItem is null)
        {
            logger.Error("Input cost create failed");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INPUTCOST_CREATE_FAILED);
        }
        var result = createdInputCostItem.ToInputCostItemResponseDto();
        return new SuccessResultDto<InputCostItemResponseDto>(result);
    }

    public async Task<ResultDto<InputCostItemResponseDto>> Update(Guid inputCostItemId, UpdateInputCostItemRequestDto requestDto)
    {
        var existInputCostItem = await inputCostItemRepository.GetById(inputCostItemId, new InputCostItemQueryableOptions
        {
            IncludedItem = true,
            IncludedVendor = true,
        });
        if (existInputCostItem is null)
        {
            logger.Error("Input cost item not found");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INPUTCOSTITEM_NOT_EXIST);
        }
        existInputCostItem.UpdateFromDto(requestDto);

        var updatedInputCostItem = await inputCostItemRepository.Update(existInputCostItem);
        if (updatedInputCostItem is null)
        {
            logger.Error("Input cost item update failed");
            return new ErrorResultDto<InputCostItemResponseDto>(ResponseCodeConstant.INPUTCOSTITEM_UPDATE_FAILED);
        }
        var result = existInputCostItem.ToInputCostItemResponseDto();
        return new SuccessResultDto<InputCostItemResponseDto>(result);
    }

    public async Task<ResultDto<bool>> UpdateMultiple(UpdateMultipleInputCostItemsRequestDto requestDto)
    {
        var isCreated = false;
        var isUpdated = false;
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Invalid org");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newInputCostItems = requestDto.Items
            .Where(item => item.InputCostItemId is null)
            .ToEntities(orgUid);
        if (newInputCostItems is not null && newInputCostItems.Any())
        {
            isCreated = await inputCostItemRepository.CreateMultiple(newInputCostItems);
        }

        var existInputCostItemIds = requestDto.Items
            .Where(item => item.InputCostItemId is not null && item.InputCostItemId != Guid.Empty)
            .Select(item => item.InputCostItemId!.Value)
            .ToList();
        var existInputCostItems = await inputCostItemRepository.GetByIds(existInputCostItemIds, new InputCostItemQueryableOptions());
        existInputCostItems.UpdateFromDto(requestDto);
        isUpdated = await inputCostItemRepository.UpdateMultiple(existInputCostItems);

        if (isCreated == false && isUpdated == false)
        {
            logger.Error("Failed to update input cost items");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOSTITEM_UPDATE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> Delete(Guid inputCostItemId)
    {
        var existInputCostItem = await inputCostItemRepository.GetById(inputCostItemId, new InputCostItemQueryableOptions());
        if (existInputCostItem is null)
        {
            logger.Error("Input cost item not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOSTITEM_NOT_EXIST);
        }
        existInputCostItem.IsDeleted = true;
        var deletedInputCostItem = await inputCostItemRepository.Update(existInputCostItem);
        if (deletedInputCostItem is null)
        {
            logger.Error("Input cost item delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOSTITEM_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
