using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
namespace Kantoku.Persistence.Queryables;

public class DeviceTokenQueryableOptions : EntityQueryableOptions
{
}

public interface IDeviceTokenQueryable
{
    IQueryable<DeviceToken> GetDeviceTokenQuery(
        DeviceTokenQueryableOptions options
    );

    IQueryable<DeviceToken> GetDeviceTokenQueryIncluded(
        DeviceTokenQueryableOptions options,
        IQueryable<DeviceToken>? query = null
    );
}

public class DeviceTokenQueryable(ApplicationDbContext context) :
    BaseQueryable<DeviceToken>(context), IDeviceTokenQueryable
{
    public IQueryable<DeviceToken> GetDeviceTokenQuery(
        DeviceTokenQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<DeviceToken> GetDeviceTokenQueryIncluded(
        DeviceTokenQueryableOptions options,
        IQueryable<DeviceToken>? query = null
    )
    {
        query ??= GetDeviceTokenQuery(options);

        return query;
    }
}
