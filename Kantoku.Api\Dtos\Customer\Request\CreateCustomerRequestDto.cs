using System.ComponentModel.DataAnnotations;
using Kantoku.Domain.Models;

namespace Kantoku.Api.Dtos.Customer.Request;
public class CreateCustomerRequestDto
{
    /// <summary>
    /// Customer code (*)
    /// </summary>
    [Required]
    public string CustomerCode { get; set; } = null!;

    /// <summary>
    /// Customer name (*)
    /// </summary>
    [Required]
    public string CustomerName { get; set; } = null!;

    /// <summary>
    /// Customer sub name
    /// </summary>
    public string? CustomerSubName { get; set; }

    /// <summary>
    /// Customer type code
    /// </summary>
    public string? CustomerTypeCode { get; set; }

    /// <summary>
    /// Customer description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The corporate number of the customer
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the customer
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the customer
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the customer
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the customer
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The logo of the customer
    /// </summary>
    public IFormFile? Logo { get; set; }
}


