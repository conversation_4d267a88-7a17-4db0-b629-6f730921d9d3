using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class ItemPriceConfiguration(string schema) : IEntityTypeConfiguration<ItemPrice>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ItemPrice> builder)
    {
        builder.HasKey(e => e.ItemPriceUid).HasName("item_price_pkey");

        builder.ToTable("item_price", Schema, tb => tb.HasComment("Bảng giá các hạng mục trên thị trường, nhập từ catalogue"));

        builder.HasIndex(e => new { e.ItemUid, e.VendorUid, e.Unit }, "item_price_item_vendor_unit_idx").IsUnique();

        builder.Property(e => e.ItemPriceUid)
            .HasColumnName("item_price_uid");
        builder.Property(e => e.ItemUid)
            .HasColumnName("item_uid");
        builder.Property(e => e.Price)
            .HasColumnName("price");
        builder.Property(e => e.Unit)
            .HasColumnName("unit");
        builder.Property(e => e.ValidFrom)
            .HasColumnName("valid_from");
        builder.Property(e => e.ValidTo)
            .HasColumnName("valid_to");
        builder.Property(e => e.VendorUid)
            .HasColumnName("vendor_uid");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Item).WithMany(p => p.ItemPrices)
            .HasForeignKey(d => d.ItemUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_price_item_id_fkey");

        builder.HasOne(d => d.Vendor).WithMany(p => p.ItemPrices)
            .HasForeignKey(d => d.VendorUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_price_vendor_id_fkey");

        builder.HasOne(d => d.Org).WithMany(p => p.ItemPrices)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_price_org_id_fkey");
    }
}
