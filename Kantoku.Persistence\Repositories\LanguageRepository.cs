using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface ILanguageRepository
{
    Task<IEnumerable<Language>> GetAll();
    Task<Language?> GetByCode(string code);
}

public class LanguageRepository : BaseRepository<Language>, ILanguageRepository
{
    public LanguageRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<IEnumerable<Language>> GetAll()
    {
        try
        {
            return await context.Languages.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all languages");
            return [];
        }
    }

    public async Task<Language?> GetByCode(string code)
    {
        try
        {
            return await context.Languages
                .Where(l => l.LanguageCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting language by code {Code}", code);
            return null;
        }
    }
}