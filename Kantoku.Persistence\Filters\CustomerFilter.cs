namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for customer queries - contains only business logic, no HTTP concerns
/// </summary>
public class CustomerFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in customer name, code, or contact information
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by customer types
    /// </summary>
    public IEnumerable<string>? CustomerType { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Normalize customer types
        if (CustomerType != null)
        {
            CustomerType = CustomerType
                .Where(ct => !string.IsNullOrWhiteSpace(ct))
                .Select(ct => ct.ToUpperInvariant())
                .Distinct()
                .ToList();
        }
    }
}