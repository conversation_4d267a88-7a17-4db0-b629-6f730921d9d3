using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace Kantoku.Processor.Services;

public class FirebaseService : IFirebaseService
{
    private readonly FirebaseMessaging _firebaseMessaging;  
    private readonly ILogger<FirebaseService> _logger;

    public FirebaseService(ILogger<FirebaseService> logger)
    {
        _logger = logger;
        if (FirebaseApp.DefaultInstance == null)
        {
            var firebaseConfigPath = Path.Combine(AppContext.BaseDirectory, "firebase-service-account.json");
            if (!File.Exists(firebaseConfigPath))
            {
                throw new FileNotFoundException("Firebase service account file not found");
            }
            try
            {
                FirebaseApp.Create(new AppOptions
                {
                    Credential = GoogleCredential.FromFile(firebaseConfigPath)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Firebase app");
                throw;
            }
        }

        _firebaseMessaging = FirebaseMessaging.DefaultInstance;
    }

    public async Task SendToDevice(string deviceToken, FirebaseMessageDto notification)
    {
        var message = new Message
        {
            Token = deviceToken,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            await _firebaseMessaging.SendAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to device {DeviceToken}", deviceToken);
            throw;
        }
    }

    public async Task SendToDevice(string deviceTokens, IEnumerable<FirebaseMessageDto> notifications)
    {
        try
        {
            var messages = notifications.Select(notification => new Message
            {
                Token = deviceTokens,
                Notification = new Notification
                {
                    Title = notification.Title,
                    Body = notification.Body
                },
                Data = notification.Data
            });

            var responses = new List<BatchResponse>();
            foreach (var message in messages.Chunk(500))
            {
                await _firebaseMessaging.SendEachAsync(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task SendToDevices(IEnumerable<string> deviceTokens, FirebaseMessageDto notification)
    {
        var message = new MulticastMessage
        {
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            var responses = new List<BatchResponse>();
            foreach (var token in deviceTokens.Chunk(500))
            {
                message.Tokens = [.. token];
                await _firebaseMessaging.SendEachForMulticastAsync(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task SendToDevices(IEnumerable<string> deviceTokens, IEnumerable<FirebaseMessageDto> notifications)
    {
        try
        {
            foreach (var notification in notifications.Chunk(500))
            {
                await SendToDevices(deviceTokens, notification);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task SendToTopic(string topic, FirebaseMessageDto notification)
    {
        var message = new Message
        {
            Topic = topic,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            await _firebaseMessaging.SendAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to topic {Topic}", topic);
            throw;
        }
    }

    public async Task SendToTopic(string topic, IEnumerable<FirebaseMessageDto> notifications)
    {
        var messages = notifications.Select(notification => new Message
        {
            Topic = topic,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        }).ToList();

        try
        {
            var responses = new List<BatchResponse>();
            foreach (var message in messages.Chunk(500))
            {
                var response = await _firebaseMessaging.SendEachAsync(message);
                responses.Add(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to topic {Topic}", topic);
            throw;
        }
    }
}