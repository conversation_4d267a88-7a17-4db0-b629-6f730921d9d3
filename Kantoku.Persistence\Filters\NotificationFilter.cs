namespace Kantoku.Persistence.Filters;

public class NotificationFilter : BaseFilter
{
    /// <summary>
    /// The keyword
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// The date from
    /// </summary>
    public string? DateFrom { get; set; }

    /// <summary>
    /// The date to
    /// </summary>
    public string? DateTo { get; set; }
}

public class EmployeeNotificationFilter : NotificationFilter
{
    /// <summary>
    /// The is read
    /// </summary>
    public bool? IsRead { get; set; }
}

public class OrgNotificationFilter : NotificationFilter
{
    /// <summary>
    /// The type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// The target type
    /// </summary>
    public string? TargetType { get; set; }

    /// <summary>
    /// The status
    /// </summary>
    public string? Status { get; set; }
}



