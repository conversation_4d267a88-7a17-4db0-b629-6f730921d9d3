using Kantoku.Persistence.Filters;
using ApiFilters = Kantoku.Api.Filters.Domains;
using DomainFilters = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Mappers;

/// <summary>
/// Maps API employee filter to domain employee filter
/// </summary>
public class EmployeeFilterMapper : FilterMapperBase<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeFilter apiFilter, DomainFilters.EmployeeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.EmployeeType = apiFilter.EmployeeType;
        domainFilter.StructureId = apiFilter.StructureId;
        domainFilter.PositionId = apiFilter.PositionId;
        domainFilter.RankId = apiFilter.RankId;
        domainFilter.WorkingStatus = apiFilter.WorkingStatus;
        domainFilter.HasApprovalAuthority = apiFilter.HasApprovalAuthority;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API employee invitation filter to domain employee invitation filter
/// </summary>
public class EmployeeInvitationFilterMapper : FilterMapperBase<ApiFilters.EmployeeInvitationFilter, DomainFilters.EmployeeInvitationFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeInvitationFilter apiFilter, DomainFilters.EmployeeInvitationFilter domainFilter)
    {
        domainFilter.InvitedEmail = apiFilter.InvitedEmail;
        domainFilter.IsAccepted = apiFilter.IsAccepted;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API audit log filter to domain audit log filter
/// </summary>
public class AuditLogFilterMapper : FilterMapperBase<ApiFilters.AuditLogFilter, DomainFilters.AuditLogFilter>
{
    protected override void MapSpecificProperties(ApiFilters.AuditLogFilter apiFilter, DomainFilters.AuditLogFilter domainFilter)
    {
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.Action = apiFilter.Action;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API category filter to domain category filter
/// </summary>
public class CategoryFilterMapper : FilterMapperBase<ApiFilters.CategoryFilter, DomainFilters.CategoryFilter>
{
    protected override void MapSpecificProperties(ApiFilters.CategoryFilter apiFilter, DomainFilters.CategoryFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.ParentId = apiFilter.ParentId;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API construction cost filter to domain construction cost filter
/// </summary>
public class ConstructionCostFilterMapper : FilterMapperBase<ApiFilters.ConstructionCostFilter, DomainFilters.ConstructionCostFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ConstructionCostFilter apiFilter, DomainFilters.ConstructionCostFilter domainFilter)
    {
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.IsPrimary = apiFilter.IsPrimary;
        domainFilter.ConstructionId = apiFilter.ConstructionId;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API construction filter to domain construction filter
/// </summary>
public class ConstructionFilterMapper : FilterMapperBase<ApiFilters.ConstructionFilter, DomainFilters.ConstructionFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ConstructionFilter apiFilter, DomainFilters.ConstructionFilter domainFilter)
    {
        domainFilter.IsPrimary = apiFilter.IsPrimary;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API contractor filter to domain contractor filter
/// </summary>
public class ContractorFilterMapper : FilterMapperBase<ApiFilters.ContractorFilter, DomainFilters.ContractorFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ContractorFilter apiFilter, DomainFilters.ContractorFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API customer filter to domain customer filter
/// </summary>
public class CustomerFilterMapper : FilterMapperBase<ApiFilters.CustomerFilter, DomainFilters.CustomerFilter>
{
    protected override void MapSpecificProperties(ApiFilters.CustomerFilter apiFilter, DomainFilters.CustomerFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.CustomerType = apiFilter.CustomerType;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API customer type filter to domain customer type filter
/// </summary>
public class CustomerTypeFilterMapper : FilterMapperBase<ApiFilters.CustomerTypeFilter, DomainFilters.CustomerTypeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.CustomerTypeFilter apiFilter, DomainFilters.CustomerTypeFilter domainFilter)
    {
        domainFilter.CustomerTypeCode = apiFilter.CustomerTypeCode;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API employee cost filter to domain employee cost filter
/// </summary>
public class EmployeeCostFilterMapper : FilterMapperBase<ApiFilters.EmployeeCostFilter, DomainFilters.EmployeeCostFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeCostFilter apiFilter, DomainFilters.EmployeeCostFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API employee leave filter to domain employee leave filter
/// </summary>
public class EmployeeLeaveFilterMapper : FilterMapperBase<ApiFilters.EmployeeLeaveFilter, DomainFilters.EmployeeLeaveFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeLeaveFilter apiFilter, DomainFilters.EmployeeLeaveFilter domainFilter)
    {
        domainFilter.QueryFrom = apiFilter.QueryFrom;
        domainFilter.QueryTo = apiFilter.QueryTo;
        domainFilter.UserInfo = apiFilter.UserInfo;
        domainFilter.BaseLeaveMin = apiFilter.BaseLeaveMin;
        domainFilter.BaseLeaveMax = apiFilter.BaseLeaveMax;
        domainFilter.BaseLeaveValidFrom = apiFilter.BaseLeaveValidFrom;
        domainFilter.BaseLeaveValidTo = apiFilter.BaseLeaveValidTo;
        domainFilter.LastLeaveRemainingMin = apiFilter.LastLeaveRemainingMin;
        domainFilter.LastLeaveRemainingMax = apiFilter.LastLeaveRemainingMax;
        domainFilter.LastLeaveRemainingValidFrom = apiFilter.LastLeaveRemainingValidFrom;
        domainFilter.LastLeaveRemainingValidTo = apiFilter.LastLeaveRemainingValidTo;
        domainFilter.UsedMin = apiFilter.UsedMin;
        domainFilter.UsedMax = apiFilter.UsedMax;
        domainFilter.PersonalUsedMin = apiFilter.PersonalUsedMin;
        domainFilter.PersonalUsedMax = apiFilter.PersonalUsedMax;
        domainFilter.OrgUsedMin = apiFilter.OrgUsedMin;
        domainFilter.OrgUsedMax = apiFilter.OrgUsedMax;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API employee shift filter to domain employee shift filter
/// </summary>
public class EmployeeShiftFilterMapper : FilterMapperBase<ApiFilters.EmployeeShiftFilter, DomainFilters.EmployeeShiftFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeShiftFilter apiFilter, DomainFilters.EmployeeShiftFilter domainFilter)
    {
        domainFilter.EmployeeId = apiFilter.EmployeeId;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.TimeFrom = apiFilter.TimeFrom;
        domainFilter.TimeTo = apiFilter.TimeTo;
        domainFilter.HasCheckedIn = apiFilter.HasCheckedIn;
        domainFilter.HasCheckedOut = apiFilter.HasCheckedOut;
        domainFilter.HasScheduledCheckIn = apiFilter.HasScheduledCheckIn;
        domainFilter.HasScheduledCheckOut = apiFilter.HasScheduledCheckOut;
        domainFilter.IsApproved = apiFilter.IsApproved;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API entry type filter to domain entry type filter
/// </summary>
public class EntryTypeFilterMapper : FilterMapperBase<ApiFilters.EntryTypeFilter, DomainFilters.EntryTypeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EntryTypeFilter apiFilter, DomainFilters.EntryTypeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API event calendar filter to domain event calendar filter
/// </summary>
public class EventCalendarFilterMapper : FilterMapperBase<ApiFilters.EventCalendarFilter, DomainFilters.EventCalendarFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EventCalendarFilter apiFilter, DomainFilters.EventCalendarFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.IsDayOff = apiFilter.IsDayOff;
        domainFilter.IsRecurring = apiFilter.IsRecurring;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API input cost filter to domain input cost filter
/// </summary>
public class InputCostFilterMapper : FilterMapperBase<ApiFilters.InputCostFilter, DomainFilters.InputCostFilter>
{
    protected override void MapSpecificProperties(ApiFilters.InputCostFilter apiFilter, DomainFilters.InputCostFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.ConstructionId = apiFilter.ConstructionId;
        domainFilter.VendorId = apiFilter.VendorId;
        domainFilter.Title = apiFilter.Title;
        domainFilter.PaymentTypeId = apiFilter.PaymentTypeId;
        domainFilter.EntryTypeId = apiFilter.EntryTypeId;
        domainFilter.IssueDateFrom = apiFilter.IssueDateFrom;
        domainFilter.IssueDateTo = apiFilter.IssueDateTo;
        domainFilter.PaymentDateFrom = apiFilter.PaymentDateFrom;
        domainFilter.PaymentDateTo = apiFilter.PaymentDateTo;
        domainFilter.TotalAmountMin = apiFilter.TotalAmountMin;
        domainFilter.TotalAmountMax = apiFilter.TotalAmountMax;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API input cost item filter to domain input cost item filter
/// </summary>
public class InputCostItemFilterMapper : FilterMapperBase<ApiFilters.InputCostItemFilter, DomainFilters.InputCostItemFilter>
{
    protected override void MapSpecificProperties(ApiFilters.InputCostItemFilter apiFilter, DomainFilters.InputCostItemFilter domainFilter)
    {
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.ConstructionId = apiFilter.ConstructionId;
        domainFilter.InputCostId = apiFilter.InputCostId;
        domainFilter.VendorId = apiFilter.VendorId;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API input cost item category filter to domain input cost item category filter
/// </summary>
public class InputCostItemCategoryFilterMapper : FilterMapperBase<ApiFilters.InputCostItemCategoryFilter, DomainFilters.InputCostItemCategoryFilter>
{
    protected override void MapSpecificProperties(ApiFilters.InputCostItemCategoryFilter apiFilter, DomainFilters.InputCostItemCategoryFilter domainFilter)
    {
        domainFilter.CategoryId = apiFilter.CategoryId;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.ConstructionId = apiFilter.ConstructionId;
        domainFilter.InputCostId = apiFilter.InputCostId;
        domainFilter.VendorId = apiFilter.VendorId;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API item filter to domain item filter
/// </summary>
public class ItemFilterMapper : FilterMapperBase<ApiFilters.ItemFilter, DomainFilters.ItemFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ItemFilter apiFilter, DomainFilters.ItemFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.CategoryId = apiFilter.CategoryId;
        domainFilter.ManufacturerId = apiFilter.ManufacturerId;
        domainFilter.ManufacturerIds = apiFilter.ManufacturerIds;
        domainFilter.Size = apiFilter.Size;
        domainFilter.SerialNumber = apiFilter.SerialNumber;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API item price filter to domain item price filter
/// </summary>
public class ItemPriceFilterMapper : FilterMapperBase<ApiFilters.ItemPriceFilter, DomainFilters.ItemPriceFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ItemPriceFilter apiFilter, DomainFilters.ItemPriceFilter domainFilter)
    {
        domainFilter.VendorKeyword = apiFilter.VendorKeyword;
        domainFilter.FromDate = apiFilter.FromDate;
        domainFilter.ToDate = apiFilter.ToDate;
        domainFilter.PriceMin = apiFilter.PriceMin;
        domainFilter.PriceMax = apiFilter.PriceMax;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API manufacturer filter to domain manufacturer filter
/// </summary>
public class ManufacturerFilterMapper : FilterMapperBase<ApiFilters.ManufacturerFilter, DomainFilters.ManufacturerFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ManufacturerFilter apiFilter, DomainFilters.ManufacturerFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API monthly report filter to domain monthly report filter
/// Note: Custom implementation due to required properties in domain filter
/// </summary>
public class MonthlyReportFilterMapper : IFilterMapper<ApiFilters.MonthlyReportFilter, DomainFilters.MonthlyReportFilter>
{
    public DomainFilters.MonthlyReportFilter MapToDomain(ApiFilters.MonthlyReportFilter apiFilter)
    {
        var domainFilter = new DomainFilters.MonthlyReportFilter
        {
            ReportFrom = apiFilter.ReportFrom ?? string.Empty,
            ReportTo = apiFilter.ReportTo ?? string.Empty,
            Keyword = apiFilter.Keyword,
            WorkingStatus = apiFilter.WorkingStatus,
            PageNum = apiFilter.PageNum,
            PageSize = apiFilter.PageSize
        };

        domainFilter.Validate();
        return domainFilter;
    }
}

/// <summary>
/// Maps API notification filter to domain notification filter
/// </summary>
public class NotificationFilterMapper : FilterMapperBase<ApiFilters.NotificationFilter, DomainFilters.NotificationFilter>
{
    protected override void MapSpecificProperties(ApiFilters.NotificationFilter apiFilter, DomainFilters.NotificationFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API employee notification filter to domain employee notification filter
/// </summary>
public class EmployeeNotificationFilterMapper : FilterMapperBase<ApiFilters.EmployeeNotificationFilter, DomainFilters.EmployeeNotificationFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeNotificationFilter apiFilter, DomainFilters.EmployeeNotificationFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.IsRead = apiFilter.IsRead;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API organization notification filter to domain organization notification filter    
/// </summary>
public class OrgNotificationFilterMapper : FilterMapperBase<ApiFilters.OrgNotificationFilter, DomainFilters.OrgNotificationFilter>
{
    protected override void MapSpecificProperties(ApiFilters.OrgNotificationFilter apiFilter, DomainFilters.OrgNotificationFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.Type = apiFilter.Type;
        domainFilter.TargetType = apiFilter.TargetType;
        domainFilter.Status = apiFilter.Status;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API outsource filter to domain outsource filter
/// </summary>
public class OutSourceFilterMapper : FilterMapperBase<ApiFilters.OutSourceFilter, DomainFilters.OutSourceFilter>
{
    protected override void MapSpecificProperties(ApiFilters.OutSourceFilter apiFilter, DomainFilters.OutSourceFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.Expertise = apiFilter.Expertise;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API outsource price filter to domain outsource price filter
/// </summary>
public class OutSourcePriceFilterMapper : FilterMapperBase<ApiFilters.OutSourcePriceFilter, DomainFilters.OutSourcePriceFilter>
{
    protected override void MapSpecificProperties(ApiFilters.OutSourcePriceFilter apiFilter, DomainFilters.OutSourcePriceFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.StartDate = apiFilter.StartDate;
        domainFilter.EndDate = apiFilter.EndDate;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API outsource shift filter to domain outsource shift filter
/// </summary>
public class OutSourceShiftFilterMapper : FilterMapperBase<ApiFilters.OutSourceShiftFilter, DomainFilters.OutSourceShiftFilter>
{
    protected override void MapSpecificProperties(ApiFilters.OutSourceShiftFilter apiFilter, DomainFilters.OutSourceShiftFilter domainFilter)
    {
        domainFilter.OutSourceId = apiFilter.OutSourceId;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.ProjectScheduleId = apiFilter.ProjectScheduleId;
        domainFilter.WorkingDate = apiFilter.WorkingDate;
        domainFilter.WorkingDateFrom = apiFilter.WorkingDateFrom;
        domainFilter.WorkingDateTo = apiFilter.WorkingDateTo;
        domainFilter.Role = apiFilter.Role;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API payment type filter to domain payment type filter
/// </summary>
public class PaymentTypeFilterMapper : FilterMapperBase<ApiFilters.PaymentTypeFilter, DomainFilters.PaymentTypeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.PaymentTypeFilter apiFilter, DomainFilters.PaymentTypeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API position filter to domain position filter
/// </summary>
public class PositionFilterMapper : FilterMapperBase<ApiFilters.PositionFilter, DomainFilters.PositionFilter>
{
    protected override void MapSpecificProperties(ApiFilters.PositionFilter apiFilter, DomainFilters.PositionFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API project daily report filter to domain project daily report filter
/// </summary>
public class ProjectDailyReportFilterMapper : FilterMapperBase<ApiFilters.ProjectDailyReportFilter, DomainFilters.ProjectDailyReportFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ProjectDailyReportFilter apiFilter, DomainFilters.ProjectDailyReportFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.ConstructionId = apiFilter.ConstructionId;
        domainFilter.ReportFrom = apiFilter.ReportFrom;
        domainFilter.ReportTo = apiFilter.ReportTo;
        domainFilter.ReportDate = apiFilter.ReportDate;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API project filter to domain project filter
/// </summary>
public class ProjectFilterMapper : FilterMapperBase<ApiFilters.ProjectFilter, DomainFilters.ProjectFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ProjectFilter apiFilter, DomainFilters.ProjectFilter domainFilter)
    {
        domainFilter.TypeId = apiFilter.TypeId;
        domainFilter.StatusCode = apiFilter.StatusCode;
        domainFilter.ProjectCode = apiFilter.ProjectCode;
        domainFilter.ProjectName = apiFilter.ProjectName;
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.ExStartDate = apiFilter.ExStartDate;
        domainFilter.ExEndDate = apiFilter.ExEndDate;
        domainFilter.ActStartDate = apiFilter.ActStartDate;
        domainFilter.ActEndDate = apiFilter.ActEndDate;
        domainFilter.BudgetMin = apiFilter.BudgetMin;
        domainFilter.BudgetMax = apiFilter.BudgetMax;
        domainFilter.CostMin = apiFilter.CostMin;
        domainFilter.CostMax = apiFilter.CostMax;
        domainFilter.ContractorUid = apiFilter.ContractorUid;
        domainFilter.CustomerUid = apiFilter.CustomerUid;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API project schedule filter to domain project schedule filter
/// </summary>
public class ProjectScheduleFilterMapper : FilterMapperBase<ApiFilters.ProjectScheduleFilter, DomainFilters.ProjectScheduleFilter>
{
    protected override void MapSpecificProperties(ApiFilters.ProjectScheduleFilter apiFilter, DomainFilters.ProjectScheduleFilter domainFilter)
    {
        domainFilter.SearchKeyword = apiFilter.SearchKeyword;
        domainFilter.FromDate = apiFilter.FromDate;
        domainFilter.ToDate = apiFilter.ToDate;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API ranking filter to domain ranking filter
/// </summary>
public class RankingFilterMapper : FilterMapperBase<ApiFilters.RankingFilter, DomainFilters.RankingFilter>
{
    protected override void MapSpecificProperties(ApiFilters.RankingFilter apiFilter, DomainFilters.RankingFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.MinValue = apiFilter.MinValue;
        domainFilter.MaxValue = apiFilter.MaxValue;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API request filter to domain request filter
/// </summary>
public class RequestFilterMapper : FilterMapperBase<ApiFilters.RequestFilter, DomainFilters.RequestFilter>
{
    protected override void MapSpecificProperties(ApiFilters.RequestFilter apiFilter, DomainFilters.RequestFilter domainFilter)
    {
        domainFilter.FromDate = apiFilter.FromDate;
        domainFilter.ToDate = apiFilter.ToDate;
        domainFilter.StatusCode = apiFilter.StatusCode;
        domainFilter.RequestTypeCode = apiFilter.RequestTypeCode;
        domainFilter.StatusCodes = apiFilter.StatusCodes;
        domainFilter.RequestTypeCodes = apiFilter.RequestTypeCodes;
        domainFilter.IsUserRequestedLeave = apiFilter.IsUserRequestedLeave;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API role filter to domain role filter
/// </summary>
public class RoleFilterMapper : FilterMapperBase<ApiFilters.RoleFilter, DomainFilters.RoleFilter>
{
    protected override void MapSpecificProperties(ApiFilters.RoleFilter apiFilter, DomainFilters.RoleFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.StructureId = apiFilter.StructureId;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API structure filter to domain structure filter
/// </summary>
public class StructureFilterMapper : FilterMapperBase<ApiFilters.StructureFilter, DomainFilters.StructureFilter>
{
    protected override void MapSpecificProperties(ApiFilters.StructureFilter apiFilter, DomainFilters.StructureFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API vendor filter to domain vendor filter
/// </summary>
public class VendorFilterMapper : FilterMapperBase<ApiFilters.VendorFilter, DomainFilters.VendorFilter>
{
    protected override void MapSpecificProperties(ApiFilters.VendorFilter apiFilter, DomainFilters.VendorFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API work shift filter to domain work shift filter
/// </summary>
public class WorkshiftFilterMapper : FilterMapperBase<ApiFilters.WorkShiftFilter, DomainFilters.WorkShiftFilter>
{
    protected override void MapSpecificProperties(ApiFilters.WorkShiftFilter apiFilter, DomainFilters.WorkShiftFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.ProjectId = apiFilter.ProjectId;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Extension methods for easy filter mapping
/// </summary>
public static class FilterMappingExtensions
{
    private static readonly EmployeeFilterMapper _employeeMapper = new();
    private static readonly AuditLogFilterMapper _auditLogMapper = new();
    private static readonly EmployeeInvitationFilterMapper _employeeInvitationMapper = new();

    /// <summary>
    /// Maps API employee filter to domain filter
    /// </summary>
    public static DomainFilters.EmployeeFilter ToDomain(this ApiFilters.EmployeeFilter apiFilter)
    {
        return _employeeMapper.MapToDomain(apiFilter);
    }

    /// <summary>
    /// Maps API audit log filter to domain filter
    /// </summary>
    public static DomainFilters.AuditLogFilter ToDomain(this ApiFilters.AuditLogFilter apiFilter)
    {
        return _auditLogMapper.MapToDomain(apiFilter);
    }

    /// <summary>
    /// Maps API employee invitation filter to domain filter
    /// </summary>
    public static DomainFilters.EmployeeInvitationFilter ToDomain(this ApiFilters.EmployeeInvitationFilter apiFilter)
    {
        return _employeeInvitationMapper.MapToDomain(apiFilter);
    }
}

/// <summary>
/// Service for registering all filter mappers
/// </summary>
public static class FilterMapperRegistration
{
    public static IServiceCollection AddFilterMappers(this IServiceCollection services)
    {
        // Register all filter mappers
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>, EmployeeFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.AuditLogFilter, DomainFilters.AuditLogFilter>, AuditLogFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeInvitationFilter, DomainFilters.EmployeeInvitationFilter>, EmployeeInvitationFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.CategoryFilter, DomainFilters.CategoryFilter>, CategoryFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ConstructionCostFilter, DomainFilters.ConstructionCostFilter>, ConstructionCostFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ConstructionFilter, DomainFilters.ConstructionFilter>, ConstructionFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ContractorFilter, DomainFilters.ContractorFilter>, ContractorFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.CustomerFilter, DomainFilters.CustomerFilter>, CustomerFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.CustomerTypeFilter, DomainFilters.CustomerTypeFilter>, CustomerTypeFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeCostFilter, DomainFilters.EmployeeCostFilter>, EmployeeCostFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeLeaveFilter, DomainFilters.EmployeeLeaveFilter>, EmployeeLeaveFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeShiftFilter, DomainFilters.EmployeeShiftFilter>, EmployeeShiftFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EntryTypeFilter, DomainFilters.EntryTypeFilter>, EntryTypeFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EventCalendarFilter, DomainFilters.EventCalendarFilter>, EventCalendarFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.InputCostFilter, DomainFilters.InputCostFilter>, InputCostFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.InputCostItemFilter, DomainFilters.InputCostItemFilter>, InputCostItemFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.InputCostItemCategoryFilter, DomainFilters.InputCostItemCategoryFilter>, InputCostItemCategoryFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ItemFilter, DomainFilters.ItemFilter>, ItemFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ItemPriceFilter, DomainFilters.ItemPriceFilter>, ItemPriceFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ManufacturerFilter, DomainFilters.ManufacturerFilter>, ManufacturerFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.MonthlyReportFilter, DomainFilters.MonthlyReportFilter>, MonthlyReportFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.NotificationFilter, DomainFilters.NotificationFilter>, NotificationFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeNotificationFilter, DomainFilters.EmployeeNotificationFilter>, EmployeeNotificationFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.OrgNotificationFilter, DomainFilters.OrgNotificationFilter>, OrgNotificationFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.OutSourceFilter, DomainFilters.OutSourceFilter>, OutSourceFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.OutSourcePriceFilter, DomainFilters.OutSourcePriceFilter>, OutSourcePriceFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.OutSourceShiftFilter, DomainFilters.OutSourceShiftFilter>, OutSourceShiftFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.PaymentTypeFilter, DomainFilters.PaymentTypeFilter>, PaymentTypeFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.PositionFilter, DomainFilters.PositionFilter>, PositionFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ProjectDailyReportFilter, DomainFilters.ProjectDailyReportFilter>, ProjectDailyReportFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ProjectFilter, DomainFilters.ProjectFilter>, ProjectFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.ProjectScheduleFilter, DomainFilters.ProjectScheduleFilter>, ProjectScheduleFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.RankingFilter, DomainFilters.RankingFilter>, RankingFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.RequestFilter, DomainFilters.RequestFilter>, RequestFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.RoleFilter, DomainFilters.RoleFilter>, RoleFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.StructureFilter, DomainFilters.StructureFilter>, StructureFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.VendorFilter, DomainFilters.VendorFilter>, VendorFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.WorkShiftFilter, DomainFilters.WorkShiftFilter>, WorkshiftFilterMapper>();

        return services;
    }
}
