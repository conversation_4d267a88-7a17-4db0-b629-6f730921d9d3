using Kantoku.Api.Dtos.Orgz.Request;
using Kantoku.Api.Dtos.Orgz.Response;
using Kantoku.SharedKernel.Helpers;
using Orgz = Kantoku.Domain.Models.Org;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class OrgMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an Org entity to an OrgResponseDto
    /// </summary>
    public static OrgResponseDto ToOrgResponseDto(this Orgz org)
    {
        if (org == null)
            return new OrgResponseDto();

        return new OrgResponseDto
        {
            OrgId = org.OrgUid.ToString(),
            OrgCode = org.OrgCode,
            OrgName = org.OrgName,
            OrgSubName = org.OrgSubName,
            PostalCode = org.PostalCode,
            Address = org.Address,
            PhoneNumber = org.PhoneNumber,
            Email = org.Email,
            Fax = org.Fax,
            Website = org.Website,
            RegistrationNumber = org.RegistrationNumber,
            RegistrationDate = org.RegistrationDate?.ToString("yyyy-MM-dd"),
            RegistrationLicenseType = org.RegistrationLicenseType,
            LegalOrgNumber = org.LegalOrgNumber,
            LegalTaxNumber = org.LegalTaxNumber,
            LegalRepresentative = org.LegalRepresentative,
            Description = org.Description,
            LogoUrl = org.LogoUrl
        };
    }

    /// <summary>
    /// Maps an Org entity to a SimpleOrgInfoResponseDto
    /// </summary>
    public static SimpleOrgInfoResponseDto ToSimpleOrgInfoResponseDto(this Orgz org)
    {
        if (org == null)
            return new SimpleOrgInfoResponseDto();

        return new SimpleOrgInfoResponseDto
        {
            OrgId = org.OrgUid.ToString(),
            OrgCode = org.OrgCode,
            OrgName = org.OrgName
        };
    }

    /// <summary>
    /// Maps a collection of Org entities to an OrgsResponseDto
    /// </summary>
    public static OrgsResponseDto ToOrgsResponseDto(
        this IEnumerable<Orgz> orgs,
        int pageNum = 1,
        int pageSize = 10,
        int totalRecords = 0)
    {
        if (orgs == null)
            return new OrgsResponseDto();

        return new OrgsResponseDto
        {
            Items = orgs.Select(o => o.ToSimpleOrgInfoResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords > 0 ? totalRecords : orgs.Count()
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateOrgRequestDto to an Org entity
    /// </summary>
    public static Orgz? ToEntity(this CreateOrgRequestDto dto)
    {
        if (dto == null)
            return null;

        var entity = new Orgz
        {
            OrgUid = GuidHelper.GenerateUUIDv7(),
            OrgCode = dto.OrgCode,
            OrgName = dto.OrgName,
            OrgSubName = dto.OrgSubName,
            PostalCode = dto.PostalCode,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            Fax = dto.Fax,
            Website = dto.Website,
            RegistrationNumber = dto.RegistrationNumber,
            RegistrationDate = !string.IsNullOrEmpty(dto.RegistrationDate)
                ? DateOnly.Parse(dto.RegistrationDate)
                : null,
            RegistrationLicenseType = dto.RegistrationLicenseType,
            LegalOrgNumber = dto.LegalOrgNumber,
            LegalTaxNumber = dto.LegalTaxNumber,
            LegalRepresentative = dto.LegalRepresentative,
            Description = dto.Description,
            IsDeleted = false
            // LogoUrl is typically handled separately during file upload in the service
        };

        return entity;
    }

    /// <summary>
    /// Updates an Org entity from an UpdateOrgRequestDto
    /// </summary>
    public static void UpdateFromDto(this Orgz entity, UpdateOrgRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.OrgCode != null)
            entity.OrgCode = dto.OrgCode;

        if (dto.OrgName != null)
            entity.OrgName = dto.OrgName;

        if (dto.OrgSubName != null)
            entity.OrgSubName = dto.OrgSubName;

        if (dto.PostalCode != null)
            entity.PostalCode = dto.PostalCode;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.Fax != null)
            entity.Fax = dto.Fax;

        if (dto.Website != null)
            entity.Website = dto.Website;

        if (dto.RegistrationNumber != null)
            entity.RegistrationNumber = dto.RegistrationNumber;

        if (dto.RegistrationDate != null)
            entity.RegistrationDate = DateOnly.Parse(dto.RegistrationDate);

        if (dto.RegistrationLicenseType != null)
            entity.RegistrationLicenseType = dto.RegistrationLicenseType;

        if (dto.LegalOrgNumber != null)
            entity.LegalOrgNumber = dto.LegalOrgNumber;

        if (dto.LegalTaxNumber != null)
            entity.LegalTaxNumber = dto.LegalTaxNumber;

        if (dto.LegalRepresentative != null)
            entity.LegalRepresentative = dto.LegalRepresentative;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
}