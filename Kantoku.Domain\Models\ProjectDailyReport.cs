using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class ProjectDailyReport : AuditableEntity
{
    public Guid ProjectDailyReportUid { get; set; }
    public Guid ProjectUid { get; set; }
    public Guid OrgUid { get; set; }

    public DateOnly ReportDate { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    [AuditProperty]
    public IEnumerable<EmployeeWorkload>? EmployeeWorkload { get; set; }

    [AuditProperty]
    public IEnumerable<OutSourceWorkload>? OutSourceWorkload { get; set; }

    public bool? IsApproved { get; set; }
    public DateTime? ApprovedTime { get; set; }
    public Guid? ApproverUid { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Project Project { get; set; } = null!;

    public virtual Employee Approver { get; set; } = null!;
}

public class EmployeeWorkload
{
    public Guid EmployeeUid { get; set; }
    public float MainConsWorkload { get; set; }
    public float SubConsWorkload { get; set; }
}

public class OutSourceWorkload
{
    public Guid OutSourceUid { get; set; }
    public float MainConsWorkload { get; set; }
    public float SubConsWorkload { get; set; }
}
