namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for employee cost queries - contains only business logic, no HTTP concerns
/// </summary>
public class EmployeeCostFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search for employee (name or code)
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter employee costs from this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateFrom { get; set; }

    /// <summary>
    /// Filter employee costs to this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateTo { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Validate date format (basic validation)
        if (!string.IsNullOrWhiteSpace(DateFrom) && !DateTime.TryParse(DateFrom, out _))
        {
            DateFrom = null; // Invalid date, ignore
        }

        if (!string.IsNullOrWhiteSpace(DateTo) && !DateTime.TryParse(DateTo, out _))
        {
            DateTo = null; // Invalid date, ignore
        }
    }
}

