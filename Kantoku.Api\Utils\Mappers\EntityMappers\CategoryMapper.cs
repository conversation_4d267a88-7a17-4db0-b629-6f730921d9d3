using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Category.Request;
using Kantoku.Api.Dtos.Category.Response;
using Kantoku.SharedKernel.Helpers;


namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class CategoryMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Category entity to a CategoryResponseDto
    /// </summary>
    /// <param name="category">The Category entity to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped CategoryResponseDto</returns>
    public static CategoryResponseDto ToCategoryResponseDto(this Category category, string languageCode)
    {
        if (category == null)
            return new CategoryResponseDto();

        var categoryName = !category.IsDefault
            ? category.CategoryName
            : category.TranslatedCategory?
                .Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                .FirstOrDefault()?.CategoryName;

        return new CategoryResponseDto
        {
            CategoryId = category.CategoryUid.ToString(),
            CategoryCode = category.CategoryCode,
            CategoryName = categoryName,
            Description = category.Description,
            NumberOfItems = category.Items?.Count ?? 0,
            IsSystemCategory = category.IsDefault == true,
            ParentId = category.ParentUid?.ToString(),
            ParentCode = category.ParentCategory?.CategoryCode,
            ParentName = category.ParentCategory?.CategoryName,
            HasChildren = category.Children?.Count > 0,
            CreateTime = category.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = category.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
        };
    }

    /// <summary>
    /// Maps a list of Category entities to a CategoriesResponseDto
    /// </summary>
    /// <param name="categories">The list of Category entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped CategoriesResponseDto</returns>
    public static CategoriesResponseDto ToCategoriesResponseDto(
        this IEnumerable<Category> categories,
        int pageNum,
        int pageSize,
        int totalRecords,
        string languageCode
    )
    {
        return new CategoriesResponseDto
        {
            Items = categories.Select(c => c.ToCategoryResponseDto(languageCode)),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateCategoryRequestDto to a Category entity
    /// </summary>
    /// <param name="dto">The CreateCategoryRequestDto to map</param>
    /// <param name="orgUid">The organization UID</param>
    /// <param name="rootCategoryCode">The root category code</param>
    /// <returns>The mapped Category entity</returns>
    public static Category? ToEntity(this CreateCategoryRequestDto dto, Guid? orgUid, string rootCategoryCode)
    {
        if (dto == null)
            return null;


        var entity = new Category
        {
            CategoryUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            CategoryCode = dto.CategoryCode,
            CategoryName = dto.CategoryName,
            Description = dto.Description,
            ParentUid = dto.ParentId,
            RootCategoryCode = rootCategoryCode,
            IsDefault = false,
            IsDeleted = false,
        };

        return entity;
    }

    /// <summary>
    /// Updates a Category entity from an UpdateCategoryRequestDto
    /// </summary>
    /// <param name="entity">The Category entity to update</param>
    /// <param name="dto">The UpdateCategoryRequestDto to update the entity with</param>
    public static void UpdateFromDto(this Category entity, UpdateCategoryRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.CategoryCode != null)
            entity.CategoryCode = dto.CategoryCode;

        if (dto.CategoryName != null)
            entity.CategoryName = dto.CategoryName;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.ParentId != null)
        {
            entity.ParentUid = dto.ParentId;
        }
        else if (dto.ParentId == null)
        {
            entity.ParentUid = null;
        }
    }

    #endregion
}