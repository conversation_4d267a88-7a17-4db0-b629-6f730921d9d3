using Kantoku.Api.Dtos.Auth;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.SharedKernel.Helpers;
using Kantoku.Api.Dtos.Auth.Request;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.Base;
using Kantoku.Persistence.Queryables;
using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Device.Request;

namespace Kantoku.Api.Services;

public interface IAuthenticationService
{
    Task<ResultDto<TokenDto>> SignIn(SignInRequestDto dto, DeviceRequestDto? deviceDto = null);
    Task<ResultDto<TokenDto>> SignOn(SignOnRequestDto dto, DeviceRequestDto? deviceDto = null);
    Task<ResultDto<bool>> LockAccount(Guid accountUid);
    Task<ResultDto<bool>> UnlockAccount(Guid accountUid);
    Task<ResultDto<bool>> DeleteAccount();
    Task<ResultDto<bool>> ChangePassword(ChangePasswordRequestDto dto);
    Task<ResultDto<bool>> ResetPassword(ResetPasswordRequestDto dto);
    Task<ResultDto<bool>> RecoverPassword(RecoverPasswordRequestDto dto);

    Task<ResultDto<bool>> GetOTP(string email);
}

[Service(ServiceLifetime.Scoped)]
public class AuthenticationService : BaseService<AuthenticationService>, IAuthenticationService
{
    private readonly IAccountRepository accountRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IRedisCacheService redisCacheService;
    private readonly IDeviceTokenRepository deviceTokenRepository;
    private readonly ITokenService tokenService;
    private readonly IEmailService emailService;
    public AuthenticationService(
        IAccountRepository accountRepository,
        IEmployeeRepository employeeRepository,
        IRedisCacheService redisCacheService,
        ITokenService tokenService,
        IEmailService emailService,
        IDeviceTokenRepository deviceTokenRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.accountRepository = accountRepository;
        this.employeeRepository = employeeRepository;
        this.redisCacheService = redisCacheService;
        this.tokenService = tokenService;
        this.emailService = emailService;
        this.deviceTokenRepository = deviceTokenRepository;
    }

    public async Task<ResultDto<bool>> GetOTP(string email)
    {

        var cacheKey = RedisKeyConst.OTP_CODE(email);
        var cachedOTP = await redisCacheService.GetAsync<string>(cacheKey);
        if (cachedOTP is not null)
        {
            return new SuccessResultDto<bool>(true);
        }
        var otp = StringHelper.GenerateOTP();
        var otpExp = TimeSpan.FromMinutes(15);
        await redisCacheService.SetAsync(cacheKey, otp, otpExp);
        var template = await emailService.GetTemplate(EmailTemplateConstant.OTP_TEMPLATE);
        if (template is null || template.Equals(string.Empty))
        {
            logger.Error("Template not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INTERNAL_SERVER_ERROR, false);
        }
        template = template.Replace("{{OTPCode}}", otp);
        template = template.Replace("{{OTPExpiredTime}}", otpExp.TotalMinutes.ToString());

        await emailService.SendAsync(email, template);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<TokenDto>> SignIn(SignInRequestDto dto, DeviceRequestDto? deviceDto = null)
    {
        var account = await accountRepository.GetByIdentity(dto.LoginId, new AccountQueryableOptions());
        if (account is null)
        {
            logger.Warning("Account {LoginId} not found", dto.LoginId);
            return new ErrorResultDto<TokenDto>(ResponseCodeConstant.ACCOUNT_NOT_EXIST);
        }
        var isPasswordCorrect = StringHelper.VerifyPassword(dto.Password, account.Password);
        if (!isPasswordCorrect)
        {
            logger.Warning("Password for user {LoginId} is incorrect", dto.LoginId);
            return new ErrorResultDto<TokenDto>(ResponseCodeConstant.PASSWORD_INCORRECT);
        }

        var accessToken = tokenService.GenerateAccountAccessToken(account);
        var refreshToken = tokenService.GenerateAccountRefreshToken(account);

        if (accessToken is null || refreshToken is null)
        {
            logger.Error("Failed to generate access token for account {LoginId}", dto.LoginId);
            return new ErrorResultDto<TokenDto>(ResponseCodeConstant.TOKEN_GENERATE_FAILED);
        }
        var result = new TokenDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
        };
        return new SuccessResultDto<TokenDto>(result);
    }

    public async Task<ResultDto<TokenDto>> SignOn(SignOnRequestDto dto, DeviceRequestDto? deviceDto = null)
    {
        var orgId = Guid.Parse(dto.OrgId);
        var employee = await employeeRepository.GetByAccount(GetCurrentAccountUid(), orgId, new EmployeeQueryableOptions());
        if (employee is null)
        {
            logger.Warning("Employee not found for account {AccountUid} and org {OrgId}", GetCurrentAccountUid(), dto.OrgId);
            return new ErrorResultDto<TokenDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        if (deviceDto is not null)
        {
            var device = await deviceTokenRepository.GetByFirebaseToken(deviceDto.FirebaseToken);
            if (device is null)
            {
                var newDevice = new DeviceToken
                {
                    DeviceTokenUid = GuidHelper.GenerateUUIDv7(),
                    EmployeeUid = employee.EmployeeUid,
                    Platform = deviceDto.Platform,
                    OsVersion = deviceDto.OsVersion,
                    AppVersion = deviceDto.AppVersion,
                    DeviceId = deviceDto.DeviceId,
                    LastActive = DateTime.Now,
                    FirebaseToken = deviceDto.FirebaseToken,
                };
                var createdDevice = await deviceTokenRepository.Create(newDevice);
                if (createdDevice is null)
                {
                    logger.Warning("Failed to create device for employee {EmployeeId}", employee.EmployeeUid);
                }
            }
            else
            {
                device.AppVersion = deviceDto.AppVersion;
                device.OsVersion = deviceDto.OsVersion;
                device.Platform = deviceDto.Platform;
                device.DeviceId = deviceDto.DeviceId;
                device.LastActive = DateTime.Now;
                var updatedDevice = await deviceTokenRepository.Update(device);
                if (!updatedDevice)
                {
                    logger.Warning("Failed to update device for employee {EmployeeId}", employee.EmployeeUid);
                }
            }
        }


        var accessToken = tokenService.GenerateOrgAccessToken(employee);
        var refreshToken = tokenService.GenerateOrgRefreshToken(employee);

        if (accessToken is null || refreshToken is null)
        {
            logger.Error("Failed to generate access token for employee {EmployeeId}", employee.EmployeeUid);
            return new ErrorResultDto<TokenDto>(ResponseCodeConstant.TOKEN_GENERATE_FAILED);
        }

        var result = new TokenDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
        };
        return new SuccessResultDto<TokenDto>(result);
    }

    public async Task<ResultDto<bool>> LockAccount(Guid accountUid)
    {
        var account = await accountRepository.GetById(accountUid, new AccountQueryableOptions());
        if (account is null)
        {
            logger.Warning("Account {AccountUid} not found", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }
        var isLocked = await accountRepository.Lock(account);
        if (!isLocked)
        {
            logger.Warning("Failed to lock account {AccountUid}", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_LOCK_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> UnlockAccount(Guid accountUid)
    {
        var account = await accountRepository.GetById(accountUid, new AccountQueryableOptions());
        if (account is null)
        {
            logger.Warning("Account {AccountUid} not found", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }
        var isUnlocked = await accountRepository.Unlock(account);
        if (!isUnlocked)
        {
            logger.Warning("Failed to unlock account {AccountUid}", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_UNLOCK_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteAccount()
    {
        var accountUid = GetCurrentAccountUid();
        var account = await accountRepository.GetById(accountUid, new AccountQueryableOptions());
        if (account is null)
        {
            logger.Warning("Account {AccountUid} not found", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }
        logger.Information("Deleting account {AccountUid}", accountUid);
        account.IsDeleted = true;
        var isDeleted = await accountRepository.Update(account);
        if (!isDeleted)
        {
            logger.Warning("Failed to delete account {AccountUid}", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> ChangePassword(ChangePasswordRequestDto dto)
    {
        var accountUid = GetCurrentAccountUid();

        var account = await accountRepository.GetById(accountUid, new AccountQueryableOptions());
        if (account is null)
        {
            logger.Warning("Account {AccountUid} not found", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }

        if (!StringHelper.VerifyPassword(dto.OldPassword, account.Password))
        {
            logger.Warning("Old password for account {AccountUid} is incorrect", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.PASSWORD_INCORRECT, false);
        }

        if (!dto.NewPassword.Equals(dto.ConfirmNewPassword))
        {
            logger.Warning("New password and confirm new password do not match for account {AccountUid}", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONFIRM_PASSWORD_MISMATCH, false);
        }

        account.Password = StringHelper.HashPassword(dto.NewPassword);
        var updatedAccount = await accountRepository.Update(account);
        if (!updatedAccount)
        {
            logger.Warning("Failed to update account {AccountUid}", accountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_UPDATE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> ResetPassword(ResetPasswordRequestDto dto)
    {

        var account = await accountRepository.GetByEmployeeId(dto.EmployeeId, new AccountQueryableOptions
        {
            IncludedUserInfo = true,
        });
        if (account is null)
        {
            logger.Warning("Employee {EmployeeId} not found", dto.EmployeeId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }

        var newPassword = StringHelper.PasswordGenerate();

        account.Password = StringHelper.HashPassword(newPassword);

        var updatedAccount = await accountRepository.Update(account);
        if (!updatedAccount)
        {
            logger.Warning("Failed to update password for employee {EmployeeId}", dto.EmployeeId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_UPDATE_FAILED, false);
        }
        account.Password = newPassword;
        var template = await emailService.GetTemplate(EmailTemplateConstant.PASSWORD_RESET_TEMPLATE);
        if (template is null || template.Equals(string.Empty))
        {
            logger.Error("Template not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INTERNAL_SERVER_ERROR, false);
        }
        template = template.Replace("{{UserName}}", account.UserInfo.Name);
        template = template.Replace("{{UserLoginID}}", account.LoginId);
        template = template.Replace("{{UserPassword}}", account.Password);
        await emailService.SendAsync(account.Email, template);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> RecoverPassword(RecoverPasswordRequestDto dto)
    {
        var account = await accountRepository.GetByIdentity(dto.AccountLoginInfo, new AccountQueryableOptions
        {
            IncludedUserInfo = true,
        });
        if (account is null)
        {
            logger.Warning("Account {AccountLoginInfo} not found", dto.AccountLoginInfo);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_NOT_EXIST, false);
        }

        var newPassword = StringHelper.PasswordGenerate();

        account.Password = StringHelper.HashPassword(newPassword);

        var updatedAccount = await accountRepository.Update(account);
        if (!updatedAccount)
        {
            logger.Warning("Failed to update account {AccountUid}", account.AccountUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ACCOUNT_UPDATE_FAILED, false);
        }

        account.Password = newPassword;
        var template = await emailService.GetTemplate(EmailTemplateConstant.PASSWORD_RECOVER_TEMPLATE);
        if (template is null || template.Equals(string.Empty))
        {
            logger.Error("Template not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INTERNAL_SERVER_ERROR, false);
        }
        template = template.Replace("{{UserName}}", account.UserInfo.Name);
        template = template.Replace("{{UserLoginID}}", account.LoginId);
        template = template.Replace("{{UserPassword}}", account.Password);
        await emailService.SendAsync(account.Email, template);
        return new SuccessResultDto<bool>(true);
    }
}
