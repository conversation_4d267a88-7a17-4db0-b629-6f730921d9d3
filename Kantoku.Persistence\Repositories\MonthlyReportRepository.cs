using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IMonthlyReportRepository
{
    Task<(IEnumerable<MonthlyReport>, int)> GetByFilter(DomainFilter.MonthlyReportFilter filter, MonthlyReportQueryableOptions options);
    Task<(IEnumerable<MonthlyReport>, int)> GetByEmployee(Guid employeeId, DomainFilter.MonthlyReportFilter filter, MonthlyReportQueryableOptions options);
    Task<MonthlyReport?> GetByEmployeeAndDate(Guid employeeId, DateOnly reportFrom, DateOnly reportTo, MonthlyReportQueryableOptions options);
    Task<MonthlyReport?> GetCurrentByEmployeeId(Guid employeeId, MonthlyReportQueryableOptions options);
    Task<MonthlyReport?> GetById(Guid monthlyReportId, MonthlyReportQueryableOptions options);
    Task<MonthlyReport?> Create(MonthlyReport monthlyReport);
    Task<IEnumerable<MonthlyReport>> Create(IEnumerable<MonthlyReport> monthlyReports);
    Task<MonthlyReport?> Update(MonthlyReport monthlyReport);
}

public class MonthlyReportRepository : BaseRepository<MonthlyReport>, IMonthlyReportRepository
{
    private readonly IMonthlyReportQueryable monthlyReportQueryable;
    private readonly IEmployeeQueryable employeeQueryable;
    public MonthlyReportRepository(
        ApplicationDbContext context,
        IMonthlyReportQueryable monthlyReportQueryable,
        IEmployeeQueryable employeeQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.monthlyReportQueryable = monthlyReportQueryable;
        this.employeeQueryable = employeeQueryable;
    }

    public async Task<(IEnumerable<MonthlyReport>, int)> GetByFilter(DomainFilter.MonthlyReportFilter reportFilter, MonthlyReportQueryableOptions options)
    {
        try
        {
            var employeeFilter = new DomainFilter.EmployeeFilter
            {
                Keyword = reportFilter.Keyword,
                WorkingStatus = reportFilter.WorkingStatus
            };
            employeeFilter = ValidateFilter(employeeFilter);
            var employeeQuery = await employeeQueryable.GetEmployeesQueryFiltered(employeeFilter, new EmployeeQueryableOptions
            {
                IncludedMonthlyReports = true,
            })
            .Where(e => !e.IsHidden)
            .ToListAsync();

            var unsettleReportEmployees = employeeQuery
                .Where(e => !e.MonthlyReportsAuthor.Any(mr =>
                    mr.ReportFrom == DateOnly.Parse(reportFilter.ReportFrom) &&
                    mr.ReportTo == DateOnly.Parse(reportFilter.ReportTo)
                ))
                .Select(e => e.EmployeeUid)
                .ToList();
            if (unsettleReportEmployees.Count > 0)
            {
                var newReport = unsettleReportEmployees.Select(uid => new MonthlyReport
                {
                    MonthlyReportUid = Guid.NewGuid(),
                    EmployeeUid = uid,
                    ReportFrom = DateOnly.Parse(reportFilter.ReportFrom),
                    ReportTo = DateOnly.Parse(reportFilter.ReportTo),
                });
                await context.MonthlyReports.AddRangeAsync(newReport);
                await context.SaveChangesAsync();
            }

            reportFilter = ValidateFilter(reportFilter);
            var reportQuery = monthlyReportQueryable.GetMonthlyReportQueryFiltered(reportFilter, options);

            var reports = await reportQuery
                .OrderBy(mr => mr.Employee.EmployeeCode)
                .Skip((reportFilter.PageNum - 1) * reportFilter.PageSize)
                .Take(reportFilter.PageSize)
                .ToListAsync();

            var total = await reportQuery.CountAsync();

            return (reports, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting monthly report");
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<MonthlyReport>, int)> GetByEmployee(Guid employeeId, DomainFilter.MonthlyReportFilter filter, MonthlyReportQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = monthlyReportQueryable.GetMonthlyReportQueryFiltered(filter, options)
                .Where(mr => mr.EmployeeUid == employeeId);

            var reports = await query
                .OrderBy(mr => mr.Employee.EmployeeCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (reports, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting monthly report");
            return ([], 0);
        }
    }

    public async Task<MonthlyReport?> GetByEmployeeAndDate(Guid employeeId, DateOnly reportFrom, DateOnly reportTo, MonthlyReportQueryableOptions options)
    {
        try
        {
            var query = monthlyReportQueryable.GetMonthlyReportQueryIncluded(options)
                .Where(mr => mr.EmployeeUid == employeeId)
                .Where(mr => mr.ReportFrom == reportFrom && mr.ReportTo == reportTo);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting monthly report");
            return null;
        }
    }

    public async Task<MonthlyReport?> GetCurrentByEmployeeId(Guid employeeId, MonthlyReportQueryableOptions options)
    {
        try
        {
            var dateFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1);
            var dateTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                .AddMonths(1)
                .AddDays(-1);

            var query = monthlyReportQueryable.GetMonthlyReportQueryIncluded(options)
                .Where(mr => dateFrom == mr.ReportFrom && dateTo == mr.ReportTo)
                .Where(mr => mr.EmployeeUid == employeeId);

            var report = await query.FirstOrDefaultAsync();
            return report;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting current monthly report for employee: {EmployeeId}", employeeId);
            return null;
        }
    }

    public async Task<MonthlyReport?> GetById(Guid monthlyReportId, MonthlyReportQueryableOptions options)
    {
        try
        {
            var query = monthlyReportQueryable.GetMonthlyReportQueryIncluded(options)
                .Where(mr => mr.MonthlyReportUid == monthlyReportId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting monthly report by id: {MonthlyReportId}", monthlyReportId);
            return null;
        }
    }

    private async Task<IEnumerable<MonthlyReport>> GetByIds(
        IEnumerable<Guid> monthlyReportIds, MonthlyReportQueryableOptions options)
    {
        try
        {
            var query = monthlyReportQueryable.GetMonthlyReportQueryIncluded(options)
                .Where(mr => monthlyReportIds.Contains(mr.MonthlyReportUid));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting monthly report by ids: {MonthlyReportIds}", monthlyReportIds);
            return [];
        }
    }

    public async Task<MonthlyReport?> Create(MonthlyReport monthlyReport)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.MonthlyReports.Add(monthlyReport);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(monthlyReport.MonthlyReportUid, new MonthlyReportQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating monthly report: {MonthlyReport}", monthlyReport);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<MonthlyReport>> Create(IEnumerable<MonthlyReport> monthlyReports)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.MonthlyReports.AddRange(monthlyReports);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetByIds(
                    monthlyReports.Select(mr => mr.MonthlyReportUid),
                    new MonthlyReportQueryableOptions
                    {
                        IncludedEmployee = true,
                    }
                );
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating monthly reports");
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<MonthlyReport?> Update(MonthlyReport monthlyReport)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.MonthlyReports.Update(monthlyReport);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(monthlyReport.MonthlyReportUid, new MonthlyReportQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating monthly report: {MonthlyReport}", monthlyReport);
            await transaction.RollbackAsync();
            return null;
        }
    }
}