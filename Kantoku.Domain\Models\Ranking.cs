﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class Ranking : AuditableEntity
{
    public Guid RankingUid { get; set; }

    [AuditProperty]
    public string RankingName { get; set; } = null!;

    [AuditProperty]
    public int? MaxValue { get; set; }

    [AuditProperty]
    public int? MinValue { get; set; }

    [AuditProperty]
    public int? AverageValue { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;
    public virtual ICollection<ProjectRankingCost> ProjectRankingCosts { get; set; } = [];
    public virtual ICollection<Employee> Employees { get; set; } = [];
}
