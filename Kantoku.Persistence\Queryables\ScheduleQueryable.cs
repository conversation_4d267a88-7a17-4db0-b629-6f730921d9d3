using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class ScheduleQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedEmployeeShifts { get; set; } = false;
    public bool IncludedOutSourceShifts { get; set; } = false;

    public ScheduleQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

    public ScheduleQueryableOptions SplitOptions()
    {
        IsSplitQuery = true;
        return this;
    }
}

public interface IScheduleQueryable
{
    IQueryable<ProjectSchedule> GetSchedulesQuery(
        ScheduleQueryableOptions options
    );

    IQueryable<ProjectSchedule> GetSchedulesQueryIncluded(
        ScheduleQueryableOptions options,
        IQueryable<ProjectSchedule>? query = null
    );

    IQueryable<ProjectSchedule> GetSchedulesQueryByFilters(
        ProjectScheduleFilter filter,
        ScheduleQueryableOptions options,
        IQueryable<ProjectSchedule>? query = null
    );
}

public class ScheduleQueryable(ApplicationDbContext context) :
    BaseQueryable<ProjectSchedule>(context), IScheduleQueryable
{
    public IQueryable<ProjectSchedule> GetSchedulesQuery(
        ScheduleQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(s => s.IsDeleted == false);
        return query;
    }

    public IQueryable<ProjectSchedule> GetSchedulesQueryIncluded(
        ScheduleQueryableOptions options,
        IQueryable<ProjectSchedule>? query = null
    )
    {
        query ??= GetSchedulesQuery(options);

        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(ps => ps.Project)
                .ThenInclude(p => p.ProjectWorkShifts)
                .ThenInclude(pws => pws.WorkShift);
        }
        if (options.IncludedEmployeeShifts || options.IncludedAll)
        {
            query = query.Include(ps => ps.EmployeeShifts)
                .ThenInclude(es => es.Employee);
        }
        if (options.IncludedOutSourceShifts || options.IncludedAll)
        {
            query = query.Include(ps => ps.OutSourceShifts)
                .ThenInclude(oss => oss.OutSource);
        }

        return query;
    }

    public IQueryable<ProjectSchedule> GetSchedulesQueryByFilters(
        ProjectScheduleFilter filter,
        ScheduleQueryableOptions options,
        IQueryable<ProjectSchedule>? query = null
    )
    {
        query ??= GetSchedulesQueryIncluded(options);
        query = query.Where(ps => ps.WorkingDate >= filter.FromDate && ps.WorkingDate <= filter.ToDate);
        if (filter.SearchKeyword != null)
        {
            query = query.Where(ps => (ps.Project.ProjectName.Contains(filter.SearchKeyword)
            || ps.Project.ProjectCode.Contains(filter.SearchKeyword))
            && ps.Project.StatusCode == ProjectStatus.STARTED && ps.Project.IsDeleted == false);
        }
        return query;
    }
}
