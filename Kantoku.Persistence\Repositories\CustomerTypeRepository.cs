using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Seeders.CustomerType;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface ICustomerTypeRepository
{   
    Task<(IEnumerable<CustomerType>, int)> GetByFilter(DomainFilter.CustomerTypeFilter filter);
}

public class CustomerTypeRepository : BaseRepository<CustomerType>, ICustomerTypeRepository
{
    private readonly ICustomerTypeSeeder customerTypeSeeder;

    public CustomerTypeRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        ICustomerTypeSeeder customerTypeSeeder
    ) : base(context, logger, tenantContext)
    {
        this.customerTypeSeeder = customerTypeSeeder;
    }

    public async Task<(IEnumerable<CustomerType>, int)> GetByFilter(DomainFilter.CustomerTypeFilter filter)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = context.CustomerTypes.AsQueryable();
            int v = await query.CountAsync();
            if (v == 0)
            {
                await customerTypeSeeder.Seed();
            }
            if (!string.IsNullOrEmpty(filter.CustomerTypeCode))
            {
                query = query.Where(c => c.CustomerTypeCode.ToUpper().Equals(filter.CustomerTypeCode.ToUpper()));
            }

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.CustomerTypeCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all Customers");
            return ([], 0);
        }
    }
}
