namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for outsource shift queries - contains only business logic, no HTTP concerns
/// </summary>
public class OutSourceShiftFilter : BaseFilter
{
    /// <summary>
    /// Filter by outsource ID
    /// </summary>
    public Guid? OutSourceId { get; set; }

    /// <summary>
    /// Filter by project ID
    /// </summary>
    public Guid? ProjectId { get; set; }

    /// <summary>
    /// Filter by project schedule ID
    /// </summary>
    public Guid? ProjectScheduleId { get; set; }

    /// <summary>
    /// Filter by specific working date (yyyy-MM-dd format)
    /// </summary>
    public string? WorkingDate { get; set; }

    /// <summary>
    /// Filter shifts from this working date (yyyy-MM-dd format)
    /// </summary>
    public string? WorkingDateFrom { get; set; }

    /// <summary>
    /// Filter shifts to this working date (yyyy-MM-dd format)
    /// </summary>
    public string? WorkingDateTo { get; set; }

    /// <summary>
    /// Filter by role
    /// </summary>
    public string? Role { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize role
        if (!string.IsNullOrWhiteSpace(Role))
        {
            Role = Role.Trim();
        }

        // Validate date formats
        WorkingDate = ValidateDate(WorkingDate);
        WorkingDateFrom = ValidateDate(WorkingDateFrom);
        WorkingDateTo = ValidateDate(WorkingDateTo);
    }

    private static string? ValidateDate(string? dateString)
    {
        if (!string.IsNullOrWhiteSpace(dateString) && !DateTime.TryParse(dateString, out _))
        {
            return null; // Invalid date, ignore
        }
        return dateString;
    }
}