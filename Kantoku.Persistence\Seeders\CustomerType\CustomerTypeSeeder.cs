using System.Text.Json;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Seeders.CustomerType;

public interface ICustomerTypeSeeder
{
    Task Seed();
}

public class CustomerTypeSeeder : ICustomerTypeSeeder
{
    private readonly ApplicationDbContext context;
    private readonly Serilog.ILogger logger;

    public CustomerTypeSeeder(ApplicationDbContext context, Serilog.ILogger logger)
    {
        this.context = context;
        this.logger = logger.ForContext<CustomerTypeSeeder>();
    }

    public async Task Seed()
    {
        try
        {
            var jsonPath = Path.Combine("Databases", "Seeders", "CustomerType", "predefined-customer-types.json");
            var jsonContent = await System.IO.File.ReadAllTextAsync(jsonPath);
            var jsonData = JsonSerializer.Deserialize<JsonDocument>(jsonContent);
            var customerTypes = jsonData?.RootElement.GetProperty("customerTypes");

            foreach (var customerType in customerTypes?.EnumerateArray() ?? Enumerable.Empty<JsonElement>())
            {
                if (customerType.ValueKind == JsonValueKind.Null)
                {
                    continue;
                }

                var systemCustomerType = new Domain.Models.CustomerType
                {
                    CustomerTypeCode = customerType.GetProperty("code").GetString() ?? string.Empty,
                    TranslatedCustomerType = customerType.GetProperty("translations").EnumerateObject()
                        .Select(t => new TranslatedCustomerType
                        {
                            LanguageCode = t.Name,
                            CustomerTypeName = t.Value.GetProperty("name").GetString() ?? string.Empty,
                            CustomerTypeDescription = t.Value.GetProperty("description").GetString() ?? string.Empty
                        }).ToList(),
                };

                var isExist = await context.CustomerTypes
                    .Where(c => c.CustomerTypeCode.Equals(systemCustomerType.CustomerTypeCode))
                    .AnyAsync();

                if (!isExist)
                {
                    await context.CustomerTypes.AddAsync(systemCustomerType);
                    logger.Information($"Added customer type: {systemCustomerType.CustomerTypeCode}");
                }
            }

            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.Error($"Error in CustomerTypeSeeder: {ex.Message}");
        }
    }
}