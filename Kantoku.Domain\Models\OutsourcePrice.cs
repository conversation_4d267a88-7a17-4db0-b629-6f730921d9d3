using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class OutSourcePrice : AuditableEntity
{
    public Guid OutSourcePriceUid { get; set; }
    public Guid OutSourceUid { get; set; }

    public DateOnly? EffectiveDate { get; set; }

    [AuditProperty]
    public int? PricePerDay { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual OutSource OutSource { get; set; } = null!;
}

