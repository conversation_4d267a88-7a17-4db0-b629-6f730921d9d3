using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class NotificationMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Notification entity to a NotificationResponseDto
    /// </summary>
    public static NotificationResponseDto ToNotificationResponseDto(this Notification notification)
    {
        if (notification == null)
            return new NotificationResponseDto();

        return new NotificationResponseDto
        {
            NotificationId = notification.NotificationUid.ToString(),
            Title = notification.Title,
            Body = notification.Body,
            NotificationType = notification.NotificationType,
            Targets = notification.NotificationTargets
                .OrderByDescending(t => t.PublishAt)
                .Select(t => new NotificationTargetResponseDto
                {
                    NotificationTargetId = t.NotificationTargetUid.ToString(),
                    TargetType = t.TargetType,
                    TargetIds = t.TargetIds,
                    PublishStatus = t.PublishStatus,
                    PublishAt = t.PublishAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                }).ToList(),
            CreatedTime = notification.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
        };
    }

    /// <summary>
    /// Maps a collection of Notification entities to a NotificationsResponseDto
    /// </summary>
    public static NotificationsResponseDto ToNotificationsResponseDto(
        this IEnumerable<Notification> notifications,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (notifications == null)
            return new NotificationsResponseDto();

        return new NotificationsResponseDto
        {
            Items = notifications
                .OrderByDescending(n => n.CreatedTime)
                .Select(n => n.ToNotificationResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a EmployeeNotification entity to a EmployeeNotificationResponseDto
    /// </summary>
    public static EmployeeNotificationResponseDto ToEmployeeNotificationResponseDto(this EmployeeNotification employeeNotification)
    {
        if (employeeNotification == null)
            return new EmployeeNotificationResponseDto();

        return new EmployeeNotificationResponseDto
        {
            EmployeeNotificationId = employeeNotification.EmployeeNotificationUid.ToString(),
            NotificationId = employeeNotification.NotificationUid.ToString(),
            Title = employeeNotification.Notification.Title,
            Body = employeeNotification.Notification.Body,
            IsRead = employeeNotification.IsRead,
            CreatedTime = employeeNotification.Notification.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
        };
    }

    /// <summary>
    /// Maps a collection of EmployeeNotification entities to a EmployeeNotificationsResponseDto
    /// </summary>
    public static EmployeeNotificationsResponseDto ToEmployeeNotificationsResponseDto(
        this IEnumerable<EmployeeNotification> employeeNotifications,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (employeeNotifications == null)
            return new EmployeeNotificationsResponseDto();

        return new EmployeeNotificationsResponseDto
        {
            Items = employeeNotifications
                .OrderByDescending(n => n.Notification.CreatedTime)
                .Select(n => n.ToEmployeeNotificationResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateNotificationRequestDto to a Notification entity
    /// </summary>
    public static Notification? ToEntity(this CreateNotificationRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Notification
        {
            NotificationUid = GuidHelper.GenerateUUIDv7(),
            Title = dto.Title,
            Body = dto.Body,
            NotificationType = dto.NotificationType,
            OrgUid = orgUid,
            IsDeleted = false,
        };
        entity.NotificationTargets = dto.Targets.Select(t => new NotificationTarget
        {
            NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
            NotificationUid = entity.NotificationUid,
            TargetType = t.TargetType,
            TargetIds = t.TargetIds,
            PublishStatus = NotificationStatusConstant.PENDING,
            IsDeleted = false
        }).ToList();

        return entity;
    }

    /// <summary>
    /// Updates a Notification entity from an UpdateNotificationRequestDto
    /// </summary>
    public static void UpdateFromDto(this Notification entity, UpdateNotificationRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (!string.IsNullOrWhiteSpace(dto.Title))
            entity.Title = dto.Title;

        if (!string.IsNullOrWhiteSpace(dto.Body))
            entity.Body = dto.Body;

        if (!string.IsNullOrWhiteSpace(dto.NotificationType))
            entity.NotificationType = dto.NotificationType;
    }

    /// <summary>
    /// Updates notification targets with proper handling of add, update, and delete operations
    /// </summary>
    public static void UpdateFromDto(this NotificationTarget entity, UpdateNotificationTargetRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.IsDeleted == true)
        {
            entity.IsDeleted = true;
            return;
        }

        if (!string.IsNullOrWhiteSpace(dto.TargetType))
            entity.TargetType = dto.TargetType;

        if (dto.TargetIds != null)
            entity.TargetIds = dto.TargetIds;
    }

    #endregion
}