using System.Text.Json.Serialization;
using Kantoku.Api.Dtos.Construction.Response;
using Kantoku.Api.Dtos.Employee.Response;

namespace Kantoku.Api.Dtos.Project.Response;

public class SimpleProjectsResponseDto
{
    public IEnumerable<SimpleProjectResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class SimpleProjectResponseDto
{
    [JsonPropertyName("id")]
    public string? ProjectId { get; set; }

    [JsonPropertyName("code")]
    public string? ProjectCode { get; set; }

    [JsonPropertyName("name")]
    public string? ProjectName { get; set; }

    [JsonPropertyName("address")]
    public string? Address { get; set; }

    [JsonPropertyName("latitude")]
    public double? Latitude { get; set; }

    [JsonPropertyName("longitude")]
    public double? Longitude { get; set; }

    [JsonPropertyName("isDefault")]
    public bool? IsDefault { get; set; }

    [JsonPropertyName("isOffice")]
    public bool? IsOffice { get; set; }

    [JsonPropertyName("constructions")]
    public IEnumerable<ConstructionBaseResponseDto>? Constructions { get; set; }

    [JsonPropertyName("projectManagers")]
    public IEnumerable<BaseEmployeeResponseDto>? ProjectManagers { get; set; }
}
