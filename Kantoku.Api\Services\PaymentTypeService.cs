﻿using Kantoku.Domain.Models;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.SharedKernel.Helpers;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.PaymentType.Response;
using Kantoku.Api.Dtos.PaymentType.Request;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.Base;
using Kantoku.Persistence.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IPaymentTypeService
{
    Task<ResultDto<PaymentTypeResponseDto>> GetById(Guid paymentTypeId);
    Task<ResultDto<PaymentTypesResponseDto>> GetByFilter(ApiFilter.PaymentTypeFilter filter);
    Task<ResultDto<PaymentTypeResponseDto>> Create(CreatePaymentTypeRequestDto requestDto);
    Task<ResultDto<PaymentTypeResponseDto>> Update(Guid paymentTypeId, UpdatePaymentTypeRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid paymentTypeId);
}

[Service(ServiceLifetime.Scoped)]
public class PaymentTypeService : BaseService<PaymentTypeService>, IPaymentTypeService
{
    private readonly IPaymentTypeRepository paymentTypeRepository;
    private readonly IFilterMapper<ApiFilter.PaymentTypeFilter, DomainFilter.PaymentTypeFilter> filterMapper;
    public PaymentTypeService(IPaymentTypeRepository paymentTypeRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IFilterMapper<ApiFilter.PaymentTypeFilter, DomainFilter.PaymentTypeFilter> filterMapper)
    : base(logger, httpContextAccessor)
    {
        this.paymentTypeRepository = paymentTypeRepository;
        this.filterMapper = filterMapper;
    }

    public async Task<ResultDto<PaymentTypesResponseDto>> GetByFilter(ApiFilter.PaymentTypeFilter filter)
    {
        var domainFilter = filterMapper.MapToDomain(filter);
        var (total, paymentTypes) = await paymentTypeRepository.GetByFilter(domainFilter, new PaymentTypeQueryableOptions());
        if (paymentTypes is null || !paymentTypes.Any() || total == 0)
        {
            logger.Error("Payment types not found");
            return new ErrorResultDto<PaymentTypesResponseDto>(ResponseCodeConstant.PAYMENTTYPE_NOT_EXIST);
        }
        var res = paymentTypes.ToPaymentTypesResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<PaymentTypesResponseDto>(res);
    }

    public async Task<ResultDto<PaymentTypeResponseDto>> GetById(Guid paymentTypeId)
    {
        var paymentType = await paymentTypeRepository.GetById(paymentTypeId, new PaymentTypeQueryableOptions());
        if (paymentType is null)
        {
            logger.Error("Payment type not found");
            return new ErrorResultDto<PaymentTypeResponseDto>(ResponseCodeConstant.PAYMENTTYPE_NOT_EXIST);
        }
        var res = paymentType.ToPaymentTypeResponseDto();
        return new SuccessResultDto<PaymentTypeResponseDto>(res);
    }

    public async Task<ResultDto<PaymentTypeResponseDto>> Create(CreatePaymentTypeRequestDto requestDto)
    {
        var newPaymentType = new PaymentType
        {
            PaymentTypeName = requestDto.PaymentTypeName,
            Description = requestDto.Description
        };
        var createdPaymentType = await paymentTypeRepository.Create(newPaymentType);
        if (createdPaymentType is null)
        {
            logger.Error("Payment type create failed");
            return new ErrorResultDto<PaymentTypeResponseDto>(ResponseCodeConstant.PAYMENTTYPE_CREATE_FAILED);
        }
        var res = createdPaymentType.ToPaymentTypeResponseDto();
        return new SuccessResultDto<PaymentTypeResponseDto>(res);
    }

    public async Task<ResultDto<PaymentTypeResponseDto>> Update(Guid paymentTypeId, UpdatePaymentTypeRequestDto requestDto)
    {
        var existPaymentType = await paymentTypeRepository.GetById(paymentTypeId, new PaymentTypeQueryableOptions());
        if (existPaymentType is null)
        {
            logger.Error("Payment type not found");
            return new ErrorResultDto<PaymentTypeResponseDto>(ResponseCodeConstant.PAYMENTTYPE_NOT_EXIST);
        }
        try
        {
            ObjectHelper.UpdateEntityFromDto(requestDto, existPaymentType);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error updating payment type");
            return new ErrorResultDto<PaymentTypeResponseDto>(ResponseCodeConstant.PAYMENTTYPE_UPDATE_FAILED);
        }

        var updatedPaymentType = await paymentTypeRepository.Update(existPaymentType);
        if (updatedPaymentType is null)
        {
            logger.Error("Payment type update failed");
            return new ErrorResultDto<PaymentTypeResponseDto>(ResponseCodeConstant.PAYMENTTYPE_UPDATE_FAILED);
        }
        var res = updatedPaymentType.ToPaymentTypeResponseDto();
        return new SuccessResultDto<PaymentTypeResponseDto>(res);
    }

    public async Task<ResultDto<bool>> Delete(Guid paymentTypeId)
    {
        var existPaymentType = await paymentTypeRepository.GetById(paymentTypeId, new PaymentTypeQueryableOptions());
        if (existPaymentType is null)
        {
            logger.Error("Payment type not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.PAYMENTTYPE_NOT_EXIST);
        }
        existPaymentType.IsDeleted = true;
        var deletedPaymentType = await paymentTypeRepository.Update(existPaymentType);
        if (deletedPaymentType is null)
        {
            logger.Error("Payment type delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.PAYMENTTYPE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}

