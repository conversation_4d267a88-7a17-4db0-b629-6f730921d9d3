using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Newtonsoft.Json;

namespace Kantoku.Persistence.EntityConfigurations;

public class OutSourceConfiguration(string schema) : IEntityTypeConfiguration<OutSource>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<OutSource> builder)
    {
        builder.HasKey(e => e.OutSourceUid)
            .HasName("outsource_pkey");

        builder.ToTable("outsource", Schema);

        builder.Property(e => e.OutSourceUid)
            .HasColumnName("outsource_uid");
        builder.Property(e => e.OutSourceCode)
            .HasColumnName("outsource_code");
        builder.Property(e => e.OutSourceName)
            .HasColumnName("outsource_name");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.CorporateNumber)
            .HasColumnName("corporate_number");
        builder.Property(e => e.ContactPerson)
            .HasColumnName("contact_person")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ContactPerson>(v)
            );
        builder.Property(e => e.Expertise)
            .HasColumnName("expertise")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ICollection<string>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<string>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<string>()).SequenceEqual(c2 ?? Enumerable.Empty<string>()),
                    c => c.Aggregate(0, (a, v) => a ^ v.GetHashCode()),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.OutSources)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("outsource_org_id_fkey");
    }
}