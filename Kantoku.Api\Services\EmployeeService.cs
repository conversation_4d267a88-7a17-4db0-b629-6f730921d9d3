using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Employee.Request;
using Kantoku.Api.Dtos.Employee.Response;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;


namespace Kantoku.Api.Services;

public interface IEmployeeService
{
    Task<ResultDto<EmployeesResponseDto>> GetEmployeesByFilter(ApiFilter.EmployeeFilter filter);
    Task<ResultDto<EmployeeResponseDto>> GetEmployeeInfo(Guid? employeeUid = null);
    Task<ResultDto<EmployeeRolesResponseDto>> GetEmployeeRole(Guid? employeeUid = null);
    Task<ResultDto<bool>> IsProjectManager(Guid? employeeUid = null);
    Task<ResultDto<SimpleEmployeesResponseDto>> GetSimpleEmployees();
    Task<ResultDto<BaseEmployeesResponseDto>> GetHasApprovalAuthorityEmployees();
    Task<ResultDto<BaseEmployeesResponseDto>> GetProjectManagers(Guid? projectId);
    Task<ResultDto<EmployeesAvatarResponseDto>> GetEmployeeAvatar();

    Task<ResultDto<EmployeeResponseDto>> UpdateEmployee(Guid employeeUid, UpdateEmployeeRequestDto requestDto);
    Task<ResultDto<bool>> DeleteEmployee(Guid employeeUid);

    Task<ResultDto<bool>> InviteEmployee(EmployeeInvitationRequestDto requestDto);
    Task<ResultDto<InvitedEmployeesResponseDto>> GetInvitedEmployees(ApiFilter.EmployeeInvitationFilter filter);
    Task<ResultDto<bool>> DeleteInvitedEmployee(Guid employeeInvitationUid);
}

[Service(ServiceLifetime.Scoped)]
public class EmployeeService : BaseService<EmployeeService>, IEmployeeService
{
    private readonly IEmployeeRepository employeeRepository;
    private readonly IEmployeeInvitationRepository employeeInvitationRepository;
    private readonly IAccountRepository accountRepository;
    private readonly IOrgRepository orgRepository;
    private readonly IRankingRepository rankingRepository;
    private readonly IFileService fileService;
    private readonly IEmailService emailService;
    private readonly IFilterMapper<ApiFilter.EmployeeFilter, DomainFilter.EmployeeFilter> filterMapper;
    private readonly IFilterMapper<ApiFilter.EmployeeInvitationFilter, DomainFilter.EmployeeInvitationFilter> employeeInvitationFilterMapper;
    private readonly EmployeeQueryableOptions options = new()
    {
        IncludedPosition = true,
        IncludedStructure = true,
        IncludedUserInfo = true,
        IncludedEmployeeRoles = true,
        IncludedEmployeeRanks = true,
    };
    public EmployeeService(
        IEmployeeRepository employeeRepository,
        IEmployeeInvitationRepository employeeInvitationRepository,
        IAccountRepository accountRepository,
        IRankingRepository rankingRepository,
        IOrgRepository orgRepository,
        IFileService fileService,
        IEmailService emailService,
        IFilterMapper<ApiFilter.EmployeeFilter, DomainFilter.EmployeeFilter> filterMapper,
        IFilterMapper<ApiFilter.EmployeeInvitationFilter, DomainFilter.EmployeeInvitationFilter> employeeInvitationFilterMapper,
        IHttpContextAccessor httpContextAccessor,
        Serilog.ILogger logger) : base(logger, httpContextAccessor)
    {
        this.employeeRepository = employeeRepository;
        this.employeeInvitationRepository = employeeInvitationRepository;
        this.accountRepository = accountRepository;
        this.rankingRepository = rankingRepository;
        this.fileService = fileService;
        this.orgRepository = orgRepository;
        this.emailService = emailService;
        this.filterMapper = filterMapper;
        this.employeeInvitationFilterMapper = employeeInvitationFilterMapper;
    }

    public async Task<ResultDto<EmployeesResponseDto>> GetEmployeesByFilter(ApiFilter.EmployeeFilter filter)
    {
        var domainFilter = filterMapper.MapToDomain(filter);
        var (employees, totalRecords) = await employeeRepository.GetByFilter(domainFilter, options);
        if (employees is null || !employees.Any() || totalRecords == 0)
        {
            logger.Error("No employees found with filter: {Filter}", filter);
            return new ErrorResultDto<EmployeesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }

        var result = employees.ToEmployeeInfosResponseDto(filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<EmployeesResponseDto>(result);
    }

    public async Task<ResultDto<EmployeeResponseDto>> GetEmployeeInfo(Guid? employeeUid = null)
    {
        employeeUid ??= GetCurrentEmployeeUid();
        var employee = await employeeRepository.GetById(employeeUid.Value, options);
        if (employee is null)
        {
            logger.Error("Employee not found with employeeUid: {EmployeeUid}", employeeUid);
            return new ErrorResultDto<EmployeeResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var result = employee.ToEmployeeInfoResponseDto();
        return new SuccessResultDto<EmployeeResponseDto>(result);
    }

    public async Task<ResultDto<SimpleEmployeesResponseDto>> GetSimpleEmployees()
    {
        var filter = new ApiFilter.EmployeeFilter
        {
            PageNum = 1,
            PageSize = int.MaxValue,
            OrgId = GetCurrentOrgUid()
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (employees, totalRecords) = await employeeRepository.GetByFilter(domainFilter, new EmployeeQueryableOptions());
        if (employees is null || !employees.Any() || totalRecords == 0)
        {
            logger.Error("No employees found with filter: {Filter}", filter);
            return new ErrorResultDto<SimpleEmployeesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var (ranks, _) = await rankingRepository.GetByFilter(new DomainFilter.RankingFilter
        {
            PageNum = 1,
            PageSize = int.MaxValue,
        }, new RankingQueryableOptions());
        var result = employees.ToSimpleEmployeesResponseDto(ranks);
        return new SuccessResultDto<SimpleEmployeesResponseDto>(result);
    }

    public async Task<ResultDto<BaseEmployeesResponseDto>> GetHasApprovalAuthorityEmployees()
    {
        var filter = new ApiFilter.EmployeeFilter
        {
            PageNum = 1,
            PageSize = int.MaxValue,
            HasApprovalAuthority = true,
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (employees, totalRecords) = await employeeRepository.GetByFilter(domainFilter, new EmployeeQueryableOptions
        {
            IncludedEmployeeRoles = true,
        });
        if (employees is null || !employees.Any() || totalRecords == 0)
        {
            logger.Error("No employees found with filter: {Filter}", filter);
            return new ErrorResultDto<BaseEmployeesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var result = employees.ToBaseEmployeesResponseDto();
        return new SuccessResultDto<BaseEmployeesResponseDto>(result);
    }

    public async Task<ResultDto<BaseEmployeesResponseDto>> GetProjectManagers(Guid? projectId)
    {
        var employees = await employeeRepository.GetProjectManagers(projectId, new EmployeeQueryableOptions());
        if (employees is null || !employees.Any())
        {
            logger.Error("No employees found with projectId: {ProjectId}", projectId);
            return new ErrorResultDto<BaseEmployeesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var result = employees.ToBaseEmployeesResponseDto();
        return new SuccessResultDto<BaseEmployeesResponseDto>(result);
    }

    public async Task<ResultDto<EmployeeRolesResponseDto>> GetEmployeeRole(Guid? employeeUid = null)
    {
        employeeUid ??= GetCurrentEmployeeUid();
        var employee = await employeeRepository.GetById(employeeUid.Value, new EmployeeQueryableOptions
        {
            IncludedEmployeeRoles = true,
        });
        if (employee is null)
        {
            logger.Error("Employee not found with employeeUid: {EmployeeUid}", employeeUid);
            return new ErrorResultDto<EmployeeRolesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var result = employee.ToEmployeeRoleResponseDto();
        return new SuccessResultDto<EmployeeRolesResponseDto>(result);
    }

    public async Task<ResultDto<bool>> IsProjectManager(Guid? employeeUid = null)
    {
        employeeUid ??= GetCurrentEmployeeUid();
        var isProjectManager = await employeeRepository.IsProjectManager(employeeUid.Value);
        return new SuccessResultDto<bool>(isProjectManager);
    }

    public async Task<ResultDto<EmployeesAvatarResponseDto>> GetEmployeeAvatar()
    {
        var employeeAvatarUrls = await employeeRepository.GetEmployeeAvatarUrl();
        if (employeeAvatarUrls is null || !employeeAvatarUrls.Any())
        {
            logger.Error("No employees found with avatar url");
            return new ErrorResultDto<EmployeesAvatarResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var tasks = employeeAvatarUrls.Select(async employee => new EmployeeAvatarResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            Avatar = await GetEmployeeAvatar(employee.AvatarUrl)
        });
        try
        {
            var items = await Task.WhenAll(tasks);
            var result = new EmployeesAvatarResponseDto
            {
                Items = items
            };
            return new SuccessResultDto<EmployeesAvatarResponseDto>(result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error when getting employee avatar");
            return new ErrorResultDto<EmployeesAvatarResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
    }

    public async Task<ResultDto<bool>> InviteEmployee(EmployeeInvitationRequestDto requestDto)
    {
        var isExistInvitation = await employeeInvitationRepository.IsExist(requestDto.Email);
        if (isExistInvitation)
        {
            logger.Error("Employee invitation already exist with email: {Email}", requestDto.Email);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_INVITATION_EXIST, false);
        }

        var invitationExpiredTime = TimeSpan.FromDays(7);

        var newEmployeeInvitation = new EmployeeInvitation
        {
            Email = requestDto.Email,
            OrgUid = GetCurrentOrgUid(),
            PreassignedEmployeeCode = requestDto.EmployeeCode,
            PreassignedRoleUid = requestDto.RoleIds
                .Where(roleId => Guid.TryParse(roleId, out var _))
                .Select(Guid.Parse)
                .ToList(),
            InvitationDescription = requestDto.InvitationDescription,
            ExpiredTime = DateTime.Now.Add(invitationExpiredTime),
        };
        var createdEmployeeInvitation = await employeeInvitationRepository.Create(newEmployeeInvitation);
        if (createdEmployeeInvitation is null)
        {
            logger.Error("Failed to create pending employee with email: {Email}", requestDto.Email);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_INVITATION_FAILED, false);
        }

        var notifyTasks = new List<Task>();
        var invitor = await accountRepository.GetById(GetCurrentAccountUid(), new AccountQueryableOptions());
        if (invitor is not null)
        {
            notifyTasks.Add(NotifyInvitedEmployee(createdEmployeeInvitation, invitor, invitationExpiredTime));
            notifyTasks.Add(NotifyInvitor(createdEmployeeInvitation, invitor));
            try
            {
                await Task.WhenAll(notifyTasks);
                return new SuccessResultDto<bool>(true);
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error when inviting employee with email: {Email}", requestDto.Email);
            }
        }

        return new SuccessResultDto<bool>(true);
    }

    private async Task NotifyInvitedEmployee(EmployeeInvitation employeeInvitation, Account invitor, TimeSpan invitationExpiredTime)
    {
        try
        {
            var org = await orgRepository.GetById(GetCurrentOrgUid());
            if (org is null)
            {
                logger.Error("Org not found with orgId: {OrgId}", GetCurrentOrgUid());
                return;
            }
            var employeeInvitationTemplate = await emailService.GetTemplate(EmailTemplateConstant.EMPLOYEE_INVITATION_TEMPLATE);
            if (employeeInvitationTemplate is null || string.IsNullOrEmpty(employeeInvitationTemplate))
            {
                logger.Error("Employee invitation template not found");
                return;
            }
            employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{OrgName}}", org.OrgName);
            employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{InviterName}}", invitor.UserInfo.Name);
            employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{InvitationExpiredTime}}", invitationExpiredTime.TotalDays.ToString());
            await emailService.SendAsync(employeeInvitation.Email, employeeInvitationTemplate);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error notifying invited employee");
        }
    }

    private async Task NotifyInvitor(EmployeeInvitation employeeInvitation, Account invitor)
    {
        try
        {
            var template = await emailService.GetTemplate(EmailTemplateConstant.EMPLOYEE_INVITATION_SENT_NOTI_TEMPLATE);
            if (template is null || string.IsNullOrEmpty(template))
            {
                logger.Error("Employee invitation sent template not found");
                return;
            }
            template = template.Replace("{{InviterName}}", invitor.UserInfo.Name);
            template = template.Replace("{{InvitedUserEmail}}", employeeInvitation.Email);
            template = template.Replace("{{EmployeeId}}", employeeInvitation.PreassignedEmployeeCode);
            await emailService.SendAsync(invitor.Email, template);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error notifying invitor");
        }
    }

    public async Task<ResultDto<InvitedEmployeesResponseDto>> GetInvitedEmployees(ApiFilter.EmployeeInvitationFilter filter)
    {
        var domainFilter = employeeInvitationFilterMapper.MapToDomain(filter);
        var (employees, totalRecords) = await employeeInvitationRepository.GetByFilter(domainFilter, new EmployeeInvitationQueryableOptions
        {
            IncludedAll = true,
        });
        if (employees is null || !employees.Any() || totalRecords == 0)
        {
            logger.Information("No employee invitations found with filter: {Filter}", filter);
            return new ErrorResultDto<InvitedEmployeesResponseDto>(ResponseCodeConstant.EMPLOYEE_INVITATION_NOT_EXIST);
        }
        var result = new InvitedEmployeesResponseDto
        {
            Items = employees.Select(e => new InvitedEmployeeResponseDto
            {
                InvitationId = e.EmployeeInvitationUid.ToString(),
                InvitedEmail = e.Email,
                InvitationDescription = e.InvitationDescription,
                IsAccepted = e.IsAccepted,
                AcceptedTime = e.AcceptedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                ExpiredTime = e.ExpiredTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            }),
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords
        };
        return new SuccessResultDto<InvitedEmployeesResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteInvitedEmployee(Guid employeeInvitationUid)
    {
        var employeeInvitation = await employeeInvitationRepository.GetById(employeeInvitationUid, new EmployeeInvitationQueryableOptions());
        if (employeeInvitation is null)
        {
            logger.Error("Employee invitation not found with employeeInvitationUid: {EmployeeInvitationUid}", employeeInvitationUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_INVITATION_NOT_EXIST, false);
        }
        employeeInvitation.IsDeleted = true;
        var updatedEmployeeInvitation = await employeeInvitationRepository.Update(employeeInvitation);
        if (updatedEmployeeInvitation is null)
        {
            logger.Error("Failed to delete employee invitation with employeeInvitationUid: {EmployeeInvitationUid}", employeeInvitationUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_INVITATION_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<EmployeeResponseDto>> UpdateEmployee(Guid employeeUid, UpdateEmployeeRequestDto requestDto)
    {
        var option = options.TrackingOptions();
        var existEmployee = await employeeRepository.GetById(employeeUid, option);
        if (existEmployee is null)
        {
            logger.Error("Employee not found with employeeId: {EmployeeId}", employeeUid);
            return new ErrorResultDto<EmployeeResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }

        existEmployee.UpdateFromDto(requestDto);
        if (requestDto.SalaryInMonth.HasValue)
        {
            var (ranks, _) = await rankingRepository.GetByFilter(new DomainFilter.RankingFilter
            {
                PageNum = 1,
                PageSize = int.MaxValue,
            }, new RankingQueryableOptions());
            var rank = ranks.Where(r => r.MinValue <= requestDto.SalaryInMonth.Value && r.MaxValue >= requestDto.SalaryInMonth.Value)
                .OrderByDescending(r => r.MaxValue)
                .ThenByDescending(r => r.MinValue)
                .FirstOrDefault();
            existEmployee.RankingUid = rank?.RankingUid ?? existEmployee.RankingUid;
        }
        var updatedEmployee = await employeeRepository.Update(existEmployee, options);
        if (updatedEmployee is null)
        {
            logger.Error("Failed to update employee with employeeId: {EmployeeId}", existEmployee.EmployeeUid);
            return new ErrorResultDto<EmployeeResponseDto>(ResponseCodeConstant.EMPLOYEE_UPDATE_FAILED);
        }
        if (requestDto.SalaryInMonth.HasValue)
        {
            var (employeesOnRank, _) = await employeeRepository.GetByFilter(new DomainFilter.EmployeeFilter
            {
                PageNum = 1,
                PageSize = int.MaxValue,
                RankId = updatedEmployee.RankingUid,
            }, new EmployeeQueryableOptions());
            if (employeesOnRank is not null && employeesOnRank.Any())
            {
                var rank = updatedEmployee.Ranking;
                if (rank is not null)
                {
                    rank.AverageValue = (int)Math.Round(employeesOnRank.Average(e => e.SalaryInMonth));
                    await rankingRepository.Update(rank);
                }
            }
        }

        
        var result = updatedEmployee.ToEmployeeInfoResponseDto();
        return new SuccessResultDto<EmployeeResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteEmployee(Guid employeeUid)
    {
        var existEmployee = await employeeRepository.GetById(employeeUid, new EmployeeQueryableOptions());
        if (existEmployee is null)
        {
            logger.Error("Employee not found with employeeId: {EmployeeId}", employeeUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST, false);
        }
        existEmployee.IsDeleted = true;
        var isUpdated = await employeeRepository.Update(existEmployee);
        if (!isUpdated)
        {
            logger.Error("Failed to delete employee with employeeId: {EmployeeId}", employeeUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<AvatarResponseDto> GetEmployeeAvatar(string? avatarUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(avatarUrl))
            {
                return new AvatarResponseDto();
            }
            var fileResponse = await fileService.DownloadFile(avatarUrl);
            if (fileResponse is null || fileResponse.Data is null)
            {
                return new AvatarResponseDto();
            }
            return new AvatarResponseDto
            {
                AvatarUrl = fileResponse.Data.DataAsBase64,
                AvatarBase64 = fileResponse.Data.DataAsBase64,
                AvatarByteArr = fileResponse.Data.DataAsBytes,
                Metadata = fileResponse.Data.Metadata,
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error when getting employee avatar with avatarUrl: {AvatarUrl}", avatarUrl);
            return new AvatarResponseDto();
        }
    }
}

