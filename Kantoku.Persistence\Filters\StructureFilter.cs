namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for organizational structure queries - contains only business logic, no HTTP concerns
/// </summary>
public class StructureFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in structure name or code
    /// </summary>
    public string? Keyword { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}
