using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class RequestTypeConfiguration(string schema) : IEntityTypeConfiguration<RequestType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<RequestType> builder)
    {
        builder.ToTable("request_type", Schema);

        builder.HasKey(e => e.RequestTypeCode).HasName("request_type_pkey");

        builder.Property(e => e.RequestTypeCode)
            .HasColumnName("request_type_code");
        builder.Property(e => e.TranslatedRequestType)
            .HasColumnName("translated_request_type")
            .HasColumnType("jsonb")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<TranslatedRequestType>>(v) ?? Enumerable.Empty<TranslatedRequestType>()
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<TranslatedRequestType>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<TranslatedRequestType>()).SequenceEqual(c2 ?? Enumerable.Empty<TranslatedRequestType>()),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.RequiredLevel1Approval)
            .HasDefaultValue(false)
            .HasColumnName("required_level1_approval");
        builder.Property(e => e.RequiredLevel2Approval)
            .HasDefaultValue(false)
            .HasColumnName("required_level2_approval");
    }
} 