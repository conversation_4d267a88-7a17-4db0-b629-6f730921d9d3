using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class ItemQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedManufacturer { get; set; } = false;
    public bool IncludedCategory { get; set; } = false;
    public bool IncludedInputCostItems { get; set; } = false;
    public bool IncludedItemPrices { get; set; } = false;
}

public interface IItemQueryable
{
    IQueryable<Item> GetItemsQuery(
        ItemQueryableOptions options
    );
    IQueryable<Item> GetItemsQueryIncluded(
        ItemQueryableOptions options,
        IQueryable<Item>? query = null
    );
    IQueryable<Item> GetItemsQueryFiltered(
        ItemFilter filter,
        ItemQueryableOptions options,
        IQueryable<Item>? query = null
    );
}

public class ItemQueryable(ApplicationDbContext context) :
    BaseQueryable<Item>(context), IItemQueryable
{
    public IQueryable<Item> GetItemsQuery(
        ItemQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Item> GetItemsQueryIncluded(
        ItemQueryableOptions options,
        IQueryable<Item>? query = null
    )
    {
        query ??= GetItemsQuery(options);
        if (options.IncludedCategory || options.IncludedAll)
        {
            query = query.Include(p => p.Category);
        }
        if (options.IncludedInputCostItems || options.IncludedAll)
        {
            query = query.Include(p => p.InputCostItems);
        }
        if (options.IncludedItemPrices || options.IncludedAll)
        {
            query = query.Include(p => p.ItemPrices);
        }
        if (options.IncludedManufacturer || options.IncludedAll)
        {
            query = query.Include(p => p.Manufacturer);
        }

        return query;
    }

    public IQueryable<Item> GetItemsQueryFiltered(
        ItemFilter filter,
        ItemQueryableOptions options,
        IQueryable<Item>? query = null
    )
    {
        query ??= GetItemsQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => p.ItemCode.Contains(filter.Keyword)
            || p.ItemName.Contains(filter.Keyword));
        }
        if (filter.CategoryId is not null && filter.CategoryId != Guid.Empty)
        {
            query = query.Where(p => p.CategoryUid == filter.CategoryId);
        }
        if (filter.ManufacturerId is not null && filter.ManufacturerId != Guid.Empty)
        {
            query = query.Where(p => p.ManufacturerUid == filter.ManufacturerId);
        }
        if (filter.ManufacturerIds != null && filter.ManufacturerIds.Any())
        {
            query = query.Where(p => p.ManufacturerUid != null && filter.ManufacturerIds.Contains(p.ManufacturerUid.Value));
        }
        if (string.IsNullOrEmpty(filter.Size) == false)
        {
            query = query.Where(p => p.Size != null && p.Size.Contains(filter.Size));
        }
        if (string.IsNullOrEmpty(filter.SerialNumber) == false)
        {
            query = query.Where(p => p.SerialNumber != null && p.SerialNumber.Contains(filter.SerialNumber));
        }
        return query;
    }
}

