using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class EmployeeLeaveQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedEmployee { get; set; } = false;
}

public interface IEmployeeLeaveQueryable
{
    IQueryable<EmployeeLeave> GetEmployeeLeaveQuery(
        EmployeeLeaveQueryableOptions options
    );

    IQueryable<EmployeeLeave> GetEmployeeLeaveQueryIncluded(
        EmployeeLeaveQueryableOptions options,
        IQueryable<EmployeeLeave>? query = null
    );

    IQueryable<EmployeeLeave> GetEmployeeLeaveQueryFiltered(
        EmployeeLeaveFilter filter,
        EmployeeLeaveQueryableOptions options,
        IQueryable<EmployeeLeave>? query = null
    );
}

public class EmployeeLeaveQueryable(ApplicationDbContext context) :
    BaseQueryable<EmployeeLeave>(context), IEmployeeLeaveQueryable
{
    public IQueryable<EmployeeLeave> GetEmployeeLeaveQuery(
        EmployeeLeaveQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(e => e.IsDeleted == false);
        return query;
    }

    public IQueryable<EmployeeLeave> GetEmployeeLeaveQueryIncluded(
        EmployeeLeaveQueryableOptions options,
        IQueryable<EmployeeLeave>? query = null
    )
    {
        query ??= GetEmployeeLeaveQuery(options);
        if (options.IncludedEmployee || options.IncludedAll)
        {
            query = query.Include(e => e.Employee);
        }
        return query;
    }

    public IQueryable<EmployeeLeave> GetEmployeeLeaveQueryFiltered(
        EmployeeLeaveFilter filter,
        EmployeeLeaveQueryableOptions options,
        IQueryable<EmployeeLeave>? query = null
    )
    {
        query ??= GetEmployeeLeaveQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(e => e.Employee.OrgUid == filter.OrgId);
        }
        if (!string.IsNullOrEmpty(filter.UserInfo))
        {
            query = query
                .Where(e => e.Employee.EmployeeName.Contains(filter.UserInfo) || e.Employee.EmployeeCode.Contains(filter.UserInfo));
        }
        if (DateOnly.TryParse(filter.QueryFrom, out var fromDate))
        {
            query = query
                .Where(el => el.BaseLeaveExpire >= fromDate);
        }
        if (DateOnly.TryParse(filter.QueryTo, out var toDate))
        {
            query = query
                .Where(el => el.BaseLeaveExpire <= toDate);
        }
        if (filter.BaseLeaveMin.HasValue)
        {
            query = query.Where(e => e.BaseLeave >= filter.BaseLeaveMin.Value);
        }
        if (filter.BaseLeaveMax.HasValue)
        {
            query = query.Where(e => e.BaseLeave <= filter.BaseLeaveMax.Value);
        }
        if (!string.IsNullOrEmpty(filter.BaseLeaveValidFrom))
        {
            if (DateOnly.TryParse(filter.BaseLeaveValidFrom, out var baseLeaveValidFromDate))
            {
                query = query.Where(e => e.BaseLeaveExpire >= baseLeaveValidFromDate);
            }
        }
        if (!string.IsNullOrEmpty(filter.BaseLeaveValidTo))
        {
            if (DateOnly.TryParse(filter.BaseLeaveValidTo, out var baseLeaveValidToDate))
            {
                query = query.Where(e => e.BaseLeaveExpire <= baseLeaveValidToDate);
            }
        }
        if (filter.LastLeaveRemainingMin.HasValue)
        {
            query = query.Where(e => e.LastRemainLeave >= filter.LastLeaveRemainingMin.Value);
        }
        if (filter.LastLeaveRemainingMax.HasValue)
        {
            query = query.Where(e => e.LastRemainLeave <= filter.LastLeaveRemainingMax.Value);
        }
        if (!string.IsNullOrEmpty(filter.LastLeaveRemainingValidFrom))
        {
            if (DateOnly.TryParse(filter.LastLeaveRemainingValidFrom, out var lastLeaveRemainingValidFromDate))
            {
                query = query.Where(e => e.LastRemainLeaveExpire >= lastLeaveRemainingValidFromDate);
            }
        }
        if (!string.IsNullOrEmpty(filter.LastLeaveRemainingValidTo))
        {
            if (DateOnly.TryParse(filter.LastLeaveRemainingValidTo, out var lastLeaveRemainingValidToDate))
            {
                query = query.Where(e => e.LastRemainLeaveExpire <= lastLeaveRemainingValidToDate);
            }
        }
        if (filter.UsedMin.HasValue)
        {
            query = query.Where(e => e.SelfTakenLeave + e.OrgTakenLeave >= filter.UsedMin.Value);
        }
        if (filter.UsedMax.HasValue)
        {
            query = query.Where(e => e.SelfTakenLeave + e.OrgTakenLeave <= filter.UsedMax.Value);
        }
        if (filter.PersonalUsedMin.HasValue)
        {
            query = query.Where(e => e.SelfTakenLeave >= filter.PersonalUsedMin.Value);
        }
        if (filter.PersonalUsedMax.HasValue)
        {
            query = query.Where(e => e.SelfTakenLeave <= filter.PersonalUsedMax.Value);
        }
        if (filter.OrgUsedMin.HasValue)
        {
            query = query.Where(e => e.OrgTakenLeave >= filter.OrgUsedMin.Value);
        }
        if (filter.OrgUsedMax.HasValue)
        {
            query = query.Where(e => e.OrgTakenLeave <= filter.OrgUsedMax.Value);
        }
        return query;
    }
}
