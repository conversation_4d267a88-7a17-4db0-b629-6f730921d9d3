using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class InputCostItemConfiguration(string schema) : IEntityTypeConfiguration<InputCostItem>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<InputCostItem> builder)
    {
        builder.HasKey(e => e.InputCostItemUid).HasName("input_cost_item_pkey");

        builder.ToTable("input_cost_item", Schema, tb => tb.HasComment("Các hạng mục phát sinh chi phí"));

        builder.Property(e => e.InputCostItemUid)
            .HasColumnName("input_cost_item_uid");
        builder.Property(e => e.TransactionDate)
            .HasColumnType("date")
            .HasColumnName("transaction_date");
        builder.Property(e => e.InputCostUid)
            .HasColumnName("input_cost_uid");
        builder.Property(e => e.ConstructionUid)
            .HasColumnName("construction_uid");
        builder.Property(e => e.ItemUid)
            .HasColumnName("item_uid");
        builder.Property(e => e.Unit)
            .HasColumnName("unit");
        builder.Property(e => e.VendorUid)
            .HasColumnName("vendor_uid");
        builder.Property(e => e.Quantity)
            .HasColumnName("quantity");
        builder.Property(e => e.Price)
            .HasColumnName("price");
        builder.Property(e => e.TaxRate)
            .HasColumnName("tax_rate");
        builder.Property(e => e.TotalNonTaxed)
            .HasColumnName("total_non_taxed");
        builder.Property(e => e.TotalTaxed)
            .HasColumnName("total_taxed");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org).WithMany(p => p.InputCostItems)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_item_org_id_fkey");

        builder.HasOne(d => d.InputCost).WithMany(p => p.InputCostItems)
            .HasForeignKey(d => d.InputCostUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_item_input_cost_id_fkey");

        builder.HasOne(d => d.Item).WithMany(p => p.InputCostItems)
            .HasForeignKey(d => d.ItemUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_item_item_id_fkey");

        builder.HasOne(d => d.Vendor).WithMany(p => p.InputCostItems)
            .HasForeignKey(d => d.VendorUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_item_vendor_id_fkey");

        builder.HasOne(d => d.Construction).WithMany(p => p.InputCostItems)
            .HasForeignKey(d => d.ConstructionUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_item_construction_id_fkey");
    }
}
