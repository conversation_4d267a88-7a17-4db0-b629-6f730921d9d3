namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for customer type queries - contains only business logic, no HTTP concerns
/// </summary>
public class CustomerTypeFilter : BaseFilter
{
    /// <summary>
    /// Filter by customer type code
    /// </summary>
    public string? CustomerTypeCode { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize customer type code
        if (!string.IsNullOrWhiteSpace(CustomerTypeCode))
        {
            CustomerTypeCode = CustomerTypeCode.ToUpperInvariant().Trim();
        }
    }
}
