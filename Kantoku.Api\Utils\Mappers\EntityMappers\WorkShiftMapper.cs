using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.WorkShift.Request;
using Kantoku.Api.Dtos.WorkShift.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class WorkShiftMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a WorkShift entity to a WorkShiftResponseDto
    /// </summary>
    /// <param name="workShift"></param>
    /// <returns> WorkShiftResponseDto </returns>
    public static WorkShiftResponseDto ToWorkShiftResponseDto(this WorkShift workShift)
    {
        if (workShift == null)
            return new WorkShiftResponseDto();

        return new WorkShiftResponseDto
        {
            WorkShiftId = workShift.WorkShiftUid.ToString(),
            WorkShiftCode = workShift.WorkShiftCode,
            WorkShiftName = workShift.WorkShiftName,
            CheckInTime = workShift.CheckInTime.ToString("HH:mm"),
            CheckOutTime = workShift.CheckOutTime.ToString("HH:mm"),
            TotalRequiredTime = workShift.TotalRequiredTime,
            Description = workShift.Description,
            WorkShiftBreaks = workShift.WorkShiftBreakTimes?.Select(bt => new WorkShiftBreakResponseDto
            {
                BreakInTime = bt.BreakInTime.ToString("HH:mm"),
                BreakOutTime = bt.BreakOutTime.ToString("HH:mm")
            }),
        };
    }

    /// <summary>
    /// Maps a collection of WorkShift entities to a WorkShiftsResponseDto
    /// </summary>  
    /// <param name="workShifts"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> WorkShiftsResponseDto </returns>
    public static WorkShiftsResponseDto ToWorkShiftsResponseDto(this IEnumerable<WorkShift> workShifts, int pageNum, int pageSize, int totalRecords)
    {
        if (workShifts == null)
            return new WorkShiftsResponseDto();

        return new WorkShiftsResponseDto
        {
            Items = workShifts.Select(ws => ws.ToWorkShiftResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Map CreateWorkShiftRequestDto to WorkShift entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="orgUid"></param>
    /// <returns> WorkShift entity </returns>
    public static WorkShift? ToEntity(this CreateWorkShiftRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        if (!ValidateWorkShiftDto(dto.CheckInTime, dto.CheckOutTime, dto.WorkShiftBreaks))
            return null;

        var entity = new WorkShift
        {
            WorkShiftUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            WorkShiftCode = dto.WorkShiftCode,
            WorkShiftName = dto.WorkShiftName,
            CheckInTime = TimeOnly.Parse(dto.CheckInTime),
            CheckOutTime = TimeOnly.Parse(dto.CheckOutTime),
            WorkShiftBreakTimes = dto.WorkShiftBreaks.Select(bt => new WorkShiftBreakTime
            {
                BreakInTime = TimeOnly.Parse(bt.BreakInTime),
                BreakOutTime = TimeOnly.Parse(bt.BreakOutTime)
            }),
            Description = dto.Description,
            TotalRequiredTime = CalculateTotalRequiredTime(
                TimeOnly.Parse(dto.CheckInTime),
                TimeOnly.Parse(dto.CheckOutTime),
                dto.WorkShiftBreaks.Select(bt => (TimeOnly.Parse(bt.BreakInTime), TimeOnly.Parse(bt.BreakOutTime)))
            ),
            IsDeleted = false
        };

        return entity;
    }

    /// <summary>
    /// Map UpdateWorkShiftRequestDto to WorkShift entity
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dto"></param>
    public static void UpdateFromDto(this WorkShift entity, UpdateWorkShiftRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.WorkShiftCode != null)
            entity.WorkShiftCode = dto.WorkShiftCode;

        if (dto.WorkShiftName != null)
            entity.WorkShiftName = dto.WorkShiftName;

        if (dto.CheckInTime != null)
            entity.CheckInTime = TimeOnly.Parse(dto.CheckInTime);

        if (dto.CheckOutTime != null)
            entity.CheckOutTime = TimeOnly.Parse(dto.CheckOutTime);

        if (dto.WorkShiftBreaks != null)
        {
            entity.WorkShiftBreakTimes = dto.WorkShiftBreaks.Select(bt => new WorkShiftBreakTime
            {
                BreakInTime = TimeOnly.Parse(bt.BreakInTime),
                BreakOutTime = TimeOnly.Parse(bt.BreakOutTime)
            });
        }

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.CheckInTime != null || dto.CheckOutTime != null || dto.WorkShiftBreaks != null)
        {
            entity.TotalRequiredTime = CalculateTotalRequiredTime(
                entity.CheckInTime,
                entity.CheckOutTime,
                entity.WorkShiftBreakTimes.Select(bt => (bt.BreakInTime, bt.BreakOutTime))
            );
        }
    }

    #endregion

    #region Helper methods

    /// <summary>
    /// Validate WorkShiftDto entity
    /// </summary>
    /// <param name="checkInTime"></param>
    /// <param name="checkOutTime"></param>
    /// <param name="workShiftBreaks"></param>
    /// <returns></returns>
    private static bool ValidateWorkShiftDto(string checkInTime, string checkOutTime, IEnumerable<WorkShiftBreakRequestDto> workShiftBreaks)
    {
        if (!TimeOnly.TryParse(checkInTime, out var checkInTimeOnly) ||
            !TimeOnly.TryParse(checkOutTime, out var checkOutTimeOnly))
        {
            return false;
        }

        if (checkInTimeOnly >= checkOutTimeOnly)
        {
            return false;
        }

        var breakTimeDto = workShiftBreaks
            .Where(br =>
            {
                if (!TimeOnly.TryParse(br.BreakInTime, out var breakInTimeOnly) ||
                    !TimeOnly.TryParse(br.BreakOutTime, out var breakOutTimeOnly))
                    return false;

                return breakInTimeOnly >= checkInTimeOnly &&
                       breakOutTimeOnly <= checkOutTimeOnly &&
                       breakInTimeOnly < breakOutTimeOnly;
            })
            .Select(b => new WorkShiftBreakTime
            {
                BreakInTime = TimeOnly.Parse(b.BreakInTime),
                BreakOutTime = TimeOnly.Parse(b.BreakOutTime)
            });

        var totalRequiredTime = (float)((checkOutTimeOnly - checkInTimeOnly).TotalSeconds - breakTimeDto.Sum(b => (b.BreakOutTime - b.BreakInTime).TotalSeconds)) / 3600;
        totalRequiredTime = (float)(Math.Round(totalRequiredTime * 4) / 4);

        if (totalRequiredTime <= 0)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Validate WorkShift entity
    /// </summary>
    /// <param name="checkInTime"></param>
    /// <param name="checkOutTime"></param>
    /// <param name="workShiftBreaks"></param>
    /// <returns></returns>
    private static bool ValidateWorkShift(TimeOnly checkInTime, TimeOnly checkOutTime, IEnumerable<WorkShiftBreakTime> workShiftBreaks)
    {
        if (checkInTime >= checkOutTime)
        {
            return false;
        }

        var breakTimeDto = workShiftBreaks
            .Where(br =>
            {
                if (br.BreakInTime >= br.BreakOutTime)
                    return false;

                return br.BreakInTime >= checkInTime &&
                       br.BreakOutTime <= checkOutTime &&
                       br.BreakInTime < br.BreakOutTime;
            })
            .Select(b => new WorkShiftBreakTime
            {
                BreakInTime = b.BreakInTime,
                BreakOutTime = b.BreakOutTime
            });

        var totalRequiredTime = (float)((checkOutTime - checkInTime).TotalSeconds - breakTimeDto.Sum(b => (b.BreakOutTime - b.BreakInTime).TotalSeconds)) / 3600;
        totalRequiredTime = (float)(Math.Round(totalRequiredTime * 4) / 4);

        if (totalRequiredTime <= 0)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Calculate total required working time based on check-in/out times and break times
    /// </summary>
    /// <param name="checkInTime"></param>
    /// <param name="checkOutTime"></param>
    /// <param name="breakTimes"></param>
    /// <returns> Total required working time </returns>
    private static float CalculateTotalRequiredTime(TimeOnly checkInTime, TimeOnly checkOutTime, IEnumerable<(TimeOnly breakIn, TimeOnly breakOut)> breakTimes)
    {
        // Calculate total hours between check-in and check-out
        float totalHours = (float)(checkOutTime - checkInTime).TotalHours;

        // Subtract break times
        if (breakTimes != null)
        {
            foreach (var (breakIn, breakOut) in breakTimes)
            {
                totalHours -= (float)(breakOut - breakIn).TotalHours;
            }
        }

        return totalHours > 0 ? totalHours : 0;
    }

    #endregion
}