using System.Globalization;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Utils.Mappers;

public static class CultureInfoMapper
{
    private static readonly Dictionary<string, string> _cultureToLanguage = new(StringComparer.OrdinalIgnoreCase)
    {
        { "ja", LanguageConstant.JA },
        { "ja-JP", LanguageConstant.JA },
        { "en", LanguageConstant.EN },
        { "en-US", LanguageConstant.EN },
        { "en-GB", LanguageConstant.EN },
        { "vi", LanguageConstant.VI },
        { "vi-VN", LanguageConstant.VI }
    };

    public static string ToLanguage(this CultureInfo? cultureInfo)
    {
        if (cultureInfo is null)
            return LanguageConstant.JA; // Default fallback

        return _cultureToLanguage.TryGetValue(cultureInfo.Name, out var language)
            ? language.ToString()
            : LanguageConstant.JA; // Fallback to English if culture not found
    }

    public static CultureInfo ToCultureInfo(this string language)
    {
        return language switch
        {
            LanguageConstant.JA => new CultureInfo("ja-JP"),
            LanguageConstant.VI => new CultureInfo("vi-VN"),
            LanguageConstant.EN => new CultureInfo("en-US"),
            _ => new CultureInfo("en-US") // Default fallback
        };
    }
}