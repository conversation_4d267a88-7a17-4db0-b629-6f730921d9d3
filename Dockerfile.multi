FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the solution file
COPY ["KantokuService.sln", "./"]

# Copy all project files
COPY ["Kantoku.SharedKernel/Kantoku.SharedKernel.csproj", "Kantoku.SharedKernel/"]
COPY ["Kantoku.Domain/Kantoku.Domain.csproj", "Kantoku.Domain/"]
COPY ["Kantoku.Application/Kantoku.Application.csproj", "Kantoku.Application/"]
COPY ["Kantoku.Infrastructure/Kantoku.Infrastructure.csproj", "Kantoku.Infrastructure/"]
COPY ["EventBus/EventBus.csproj", "EventBus/"]
COPY ["EventBus.Kafka/EventBus.Kafka.csproj", "EventBus.Kafka/"]
COPY ["Kantoku.Persistence/Kantoku.Persistence.csproj", "Kantoku.Persistence/"]
COPY ["Kantoku.Processor/Kantoku.Processor.csproj", "Kantoku.Processor/"]
COPY ["Kantoku.Api/Kantoku.Api.csproj", "Kantoku.Api/"]

# Restore packages for all projects
RUN dotnet restore

# Copy the rest of the source code
COPY . .

# Build and publish the API project
ARG configuration=Release
RUN dotnet publish "Kantoku.Api/Kantoku.Api.csproj" -c $configuration -o /app/publish/api /p:UseAppHost=false
RUN dotnet publish "Kantoku.Processor/Kantoku.Processor.csproj" -c $configuration -o /app/publish/processor /p:UseAppHost=false

# API image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS api
WORKDIR /app
COPY --from=build /app/publish/api .
EXPOSE 4869
ENV ASPNETCORE_URLS=http://+:4869
USER app
ENTRYPOINT ["dotnet", "Kantoku.Api.dll"]

# Processor image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS processor
WORKDIR /app
COPY --from=build /app/publish/processor .
USER app
ENTRYPOINT ["dotnet", "Kantoku.Processor.dll"]