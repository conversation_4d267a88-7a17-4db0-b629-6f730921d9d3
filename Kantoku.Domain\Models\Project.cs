﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class Project : AuditableEntity
{
    public Guid ProjectUid { get; set; }
    public Guid OrgUid { get; set; }

    [AuditProperty]
    public string ProjectCode { get; set; } = null!;

    [AuditProperty]
    public string ProjectName { get; set; } = null!;

    [AuditProperty]
    public Guid? ProjectTypeUid { get; set; }

    [AuditProperty]
    public Guid? CustomerUid { get; set; }

    [AuditProperty]
    public Guid? ContractorUid { get; set; }

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public double? Latitude { get; set; }

    [AuditProperty]
    public double? Longitude { get; set; }

    [AuditProperty]
    public string StatusCode { get; set; } = null!;

    [AuditProperty]
    public int MonthlyReportDate { get; set; }

    public bool IsDeleted { get; set; } = false;
    public bool IsOffice { get; set; } = false;
    public bool IsDefault { get; set; } = false;

    [AuditProperty]
    public DateOnly? ExpectedStartDate { get; set; }

    [AuditProperty]
    public DateOnly? ExpectedEndDate { get; set; }

    [AuditProperty]
    public DateOnly? ActualStartDate { get; set; }

    [AuditProperty]
    public DateOnly? ActualEndDate { get; set; }

    [AuditProperty]
    public float? InitialBudget { get; set; }

    [AuditProperty]
    public float? ActualBudget { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ProjectType? ProjectType { get; set; }

    public virtual Contractor? Contractor { get; set; }

    public virtual Customer? Customer { get; set; }

    public virtual Status Status { get; set; } = null!;

    public virtual ICollection<Request> Requests { get; set; } = [];

    public virtual ICollection<ProjectSchedule> ProjectSchedules { get; set; } = [];

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];

    public virtual ICollection<ProjectManager> Managers { get; set; } = [];

    public virtual ICollection<ProjectWorkShift> ProjectWorkShifts { get; set; } = [];

    public virtual ICollection<Construction> Constructions { get; set; } = [];

    public virtual ICollection<ProjectDailyReport> ProjectDailyReports { get; set; } = [];

    public virtual ICollection<ProjectRankingCost> ProjectRankingCosts { get; set; } = [];

    public virtual ICollection<EmployeeShiftDetail> EmployeeShiftDetails { get; set; } = [];
}

public static class ProjectStatus
{
    public const string PLANNED = nameof(PLANNED);
    public const string STARTED = nameof(STARTED);
    public const string ENDED = nameof(ENDED);
}

