using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class AuditLogConfiguration(string schema) : IEntityTypeConfiguration<AuditLog>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.HasKey(e => e.AuditLogUid).HasName("audit_log_pkey");

        builder.ToTable("audit_log", Schema);

        builder.Property(e => e.AuditLogUid)
            .HasColumnName("audit_log_uid");
        builder.Property(e => e.EntityName)
            .HasColumnName("entity_name");
        builder.Property(e => e.EntityId)
            .HasColumnName("entity_id");
        builder.Property(e => e.Action)
            .HasColumnName("action");
        builder.Property(e => e.AccountUid)
            .HasColumnName("account_uid");
        builder.Property(e => e.Timestamp)
            .HasColumnName("timestamp");
        builder.Property(e => e.OldValues)
            .HasColumnType("jsonb")
            .HasColumnName("old_values");
        builder.Property(e => e.NewValues)
            .HasColumnType("jsonb")
            .HasColumnName("new_values");

        builder.HasOne(d => d.Account)
            .WithMany(p => p.AuditLogs)
            .HasForeignKey(d => d.AccountUid)
            .HasConstraintName("audit_log_account_uid_fkey");
    }
}
