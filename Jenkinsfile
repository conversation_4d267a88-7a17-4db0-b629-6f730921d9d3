pipeline {
    agent any
    
    parameters {
        booleanParam(name: 'DEPLOY_API', defaultValue: true, description: 'Deploy Kantoku.Api service?')
        booleanParam(name: 'DEPLOY_PROCESS', defaultValue: true, description: 'Deploy Kantoku.Processor service?')
        // Add more services as needed
    }
  
    environment {
        REGISTRY = "neoxasia/kantoku"
        REGISTRY_CREDENTIALS = "docker-hub"
        GIT_TAG = "${GIT_BRANCH.tokenize('/').pop()}-${GIT_COMMIT.substring(0,7)}-${env.BUILD_ID}"
        APP_ENV = "Development"

        // Service-specific configurations
        API_IMAGE = "${REGISTRY}-api"
        API_CONTAINER = "${JOB_NAME.replaceAll('/','-')}-api"
        API_PORT = "84:4869"

        PROCESS_IMAGE = "${REGISTRY}-processor"
        PROCESS_CONTAINER = "${JOB_NAME.replaceAll('/','-')}-processor"
    }

    stages {
        stage('Build and Deploy API') {
            when { expression { return params.DEPLOY_API } }
            stages {
                stage('Build API Docker Image') {
                    steps {
                        script {
                            echo "Building API image ${API_IMAGE}"
                            sh "docker build --target api -t ${API_IMAGE}:${GIT_TAG} -f Dockerfile.multi ."
                            sh "docker tag ${API_IMAGE}:${GIT_TAG} ${API_IMAGE}:latest"
                            
                            // withCredentials([usernamePassword(credentialsId: REGISTRY_CREDENTIALS, usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                            //     sh 'echo $DOCKER_PASSWORD | docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD'
                            //     sh "docker push ${API_IMAGE}:${GIT_TAG}"
                            //     sh "docker push ${API_IMAGE}:latest"
                            // }
                        }
                    }
                }

                stage('Deploy API') {
                    steps {
                        script {
                            // Stop and remove existing container
                            sh "docker stop ${API_CONTAINER} || true"
                            sh "docker rm ${API_CONTAINER} || true"

                            // Deploy new container
                            sh """
                                docker run -d \
                                --name ${API_CONTAINER} \
                                -p ${API_PORT} \
                                -e "ASPNETCORE_ENVIRONMENT=${APP_ENV}" \
                                -e "TZ=Asia/Tokyo" \
                                -v ./Logs:/app/Logs:rw \
                                ${API_IMAGE}:${GIT_TAG}
                            """
                        }
                    }
                }
            }
        }

        stage('Build and Deploy Processor') {
            when { expression { return params.DEPLOY_PROCESS } }
            stages {
                stage('Build Processor Docker Image') {
                    steps {
                        script {
                            echo "Building Processor image ${PROCESS_IMAGE}"
                            sh "docker build --target processor -t ${PROCESS_IMAGE}:${GIT_TAG} -f Dockerfile.multi ."
                            sh "docker tag ${PROCESS_IMAGE}:${GIT_TAG} ${PROCESS_IMAGE}:latest"
                            
                            // withCredentials([usernamePassword(credentialsId: REGISTRY_CREDENTIALS, usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                            //     sh 'echo $DOCKER_PASSWORD | docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD'
                            //     sh "docker push ${PROCESS_IMAGE}:${GIT_TAG}"
                            //     sh "docker push ${PROCESS_IMAGE}:latest"
                            // }
                        }
                    }
                }

                stage('Deploy Processor') {
                    steps {
                        script {
                            // Stop and remove existing container
                            sh "docker stop ${PROCESS_CONTAINER} || true"
                            sh "docker rm ${PROCESS_CONTAINER} || true"

                            // Deploy new container
                            sh """
                                docker run -d \
                                --name ${PROCESS_CONTAINER} \
                                -e "ASPNETCORE_ENVIRONMENT=${APP_ENV}" \
                                -e "TZ=Asia/Tokyo" \
                                -v ./Logs:/app/Logs:rw \
                                ${PROCESS_IMAGE}:${GIT_TAG}
                            """
                        }
                    }
                }
            }
        }

        stage('Cleanup') {
            steps {
                sh "docker image prune -f"
            }
        }
    }

    post {
        success {
            echo "========pipeline executed successfully ========"
        }
        failure {
            echo "========pipeline execution failed========"
            script {
                if (params.DEPLOY_API) {
                    sh "docker stop ${API_CONTAINER} || true"
                    sh "docker rm ${API_CONTAINER} || true"
                }
                if (params.DEPLOY_PROCESS) {
                    sh "docker stop ${PROCESS_CONTAINER} || true"
                    sh "docker rm ${PROCESS_CONTAINER} || true"
                }
            }
        }
        always {
            cleanWs()
        }
    }
}