using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class OutSourceShiftMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an OutSourceShift entity to a ScheduledOutSourceShiftResponseDto
    /// </summary>
    /// <param name="outSourceShift">The OutSourceShift entity to map</param>
    /// <returns>A ScheduledOutSourceShiftResponseDto containing the mapped outsource shift</returns>
    /// <exception cref="NullReferenceException">If the outsource shift is null</exception>
    public static ScheduledOutSourceShiftResponseDto ToScheduledOutSourceShiftResponseDto(this OutSourceShift outSourceShift)
    {
        if (outSourceShift == null)
            return new ScheduledOutSourceShiftResponseDto();

        return new ScheduledOutSourceShiftResponseDto
        {
            OutSourceShiftId = outSourceShift.OutSourceShiftUid.ToString(),
            ProjectScheduleId = outSourceShift.ProjectScheduleUid.ToString(),
            OutSourceName = outSourceShift.OutSource?.OutSourceName,
            OutSourceCode = outSourceShift.OutSource?.OutSourceCode,
            WorkingDate = outSourceShift.ProjectSchedule?.WorkingDate.ToString("yyyy-MM-dd"),
            ScheduledStartTime = outSourceShift.ScheduledStartTime.ToString("HH:mm:ss"),
            ScheduledEndTime = outSourceShift.ScheduledEndTime.ToString("HH:mm:ss"),
            AssignedWorkload = outSourceShift.AssignedWorkload,
            WorkingRole = outSourceShift.Role,
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateScheduledOutSourceShiftRequestDto to an OutSourceShift entity
    /// </summary>
    /// <param name="dto">The CreateScheduledOutSourceShiftRequestDto to map</param>
    /// <param name="projectSchedule">The project schedule</param>
    /// <returns>The mapped OutSourceShift entity</returns>
    public static OutSourceShift? ToEntity(this CreateScheduledOutSourceShiftRequestDto dto, ProjectSchedule projectSchedule)
    {
        if (dto == null || projectSchedule == null)
            return null;

        var workingDate = projectSchedule.WorkingDate;

        var startWorkingTime = new DateTime(
            workingDate.Year, workingDate.Month, workingDate.Day,
            dto.StartTime.Hour, dto.StartTime.Minute, 0);

        var endWorkingTime = new DateTime(
            workingDate.Year, workingDate.Month, workingDate.Day,
            dto.EndTime.Hour, dto.EndTime.Minute, 0);

        var newOutSourceShift = new OutSourceShift
        {
            OutSourceShiftUid = GuidHelper.GenerateUUIDv7(),
            OutSourceUid = dto.OutSourceId,
            ProjectScheduleUid = projectSchedule.ProjectScheduleUid,
            ScheduledStartTime = startWorkingTime,
            ScheduledEndTime = endWorkingTime,
            AssignedWorkload = dto.AssignedWorkload,
            Role = dto.AssignedRole,
            IsDeleted = false,
        };

        return newOutSourceShift;
    }

    /// <summary>
    /// Clone an OutSourceShift entity
    /// </summary>
    /// <param name="shift">The OutSourceShift entity to clone</param>
    /// <param name="projectScheduleId">The project schedule id</param>
    /// <param name="date">The date to clone the shift to</param>
    /// <returns>The cloned OutSourceShift entity</returns>
    public static OutSourceShift? Clone(this OutSourceShift shift, Guid? projectScheduleId, DateOnly date)
    {
        if (projectScheduleId is null)
        {
            return null;
        }
        var newScheduledStartTime = new DateTime(
            date.Year,
            date.Month,
            date.Day,
            shift.ScheduledStartTime.Hour,
            shift.ScheduledStartTime.Minute,
            0);
        var newScheduledEndTime = new DateTime(
            date.Year,
            date.Month,
            date.Day,
            shift.ScheduledEndTime.Hour,
            shift.ScheduledEndTime.Minute,
            0);
        return new OutSourceShift
        {
            OutSourceShiftUid = GuidHelper.GenerateUUIDv7(),
            OutSourceUid = shift.OutSourceUid,
            ProjectScheduleUid = projectScheduleId.Value,
            ScheduledStartTime = newScheduledStartTime,
            ScheduledEndTime = newScheduledEndTime,
            AssignedWorkload = shift.AssignedWorkload,
            Role = shift.Role,
        };
    }

    #endregion
}