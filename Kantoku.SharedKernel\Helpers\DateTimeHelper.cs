namespace Kantoku.SharedKernel.Helpers;

public static class DateTimeHelper
{
    public static string? AdjustBeyondTime(DateTime? inTime, DateTime? outTime)
    {
        if (inTime == null || outTime == null) return null;

        if (TryParseToLocalTime(inTime, out var inTimeLocal))
        {
            inTime = inTimeLocal;
        }

        if (TryParseToLocalTime(outTime, out var outTimeLocal))
        {
            outTime = outTimeLocal;
        }

        if (outTime.Value.Date > inTime.Value.Date)
        {
            // Calculate hours past midnight
            int extraHours = 24;
            var adjustedHour = outTime.Value.Hour + extraHours;

            // Return only the time portion with adjusted hours
            return $"{adjustedHour:00}:{outTime.Value.Minute:00}:{outTime.Value.Second:00}";
        }

        return outTime.Value.ToString("HH:mm:ss");
    }

    public static string? AdjustBeyondTime(DateTimeOffset? inTime, DateTimeOffset? outTime)
    {
        if (inTime == null || outTime == null) return null;

        if (TryParseToLocalTime(inTime, out var inTimeLocal))
        {
            inTime = inTimeLocal;
        }

        if (TryParseToLocalTime(outTime, out var outTimeLocal))
        {
            outTime = outTimeLocal;
        }

        if (outTime.Value.Date > inTime.Value.Date)
        {
            // Calculate hours past midnight
            int extraHours = 24;
            var adjustedHour = outTime.Value.Hour + extraHours;

            // Return only the time portion with adjusted hours
            return $"{adjustedHour:00}:{outTime.Value.Minute:00}:{outTime.Value.Second:00}";
        }

        return outTime.Value.ToString("HH:mm:ss");
    }

    public static DateTime? ParseToLocalTime(DateTime? dateTime)
    {
        if (dateTime == null)
        {
            return null;
        }

        if (dateTime.Value.Kind == DateTimeKind.Utc)
        {
            try
            {
                var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
                var jstDateTime = TimeZoneInfo.ConvertTimeFromUtc(dateTime.Value, jstTimeZone);
                return jstDateTime;
            }
            catch (System.Exception)
            {
                return null;
            }
        }
        return dateTime.Value;
    }

    public static DateTime? ParseToLocalTime(DateTimeOffset? dateTime)
    {
        if (dateTime == null)
        {
            return null;
        }

        try
        {
            var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
            return TimeZoneInfo.ConvertTime(dateTime.Value, jstTimeZone).DateTime;
        }
        catch (Exception)
        {
            return null;
        }
    }

    public static bool TryParseToLocalTime(DateTime? dateTime, out DateTime result)
    {
        if (dateTime == null)
        {
            result = DateTime.MinValue;
            return false;
        }

        if (dateTime.Value.Kind == DateTimeKind.Utc)
        {
            try
            {
                var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
                var jstDateTime = TimeZoneInfo.ConvertTimeFromUtc(dateTime.Value, jstTimeZone);
                result = jstDateTime;
                return true;
            }
            catch (System.Exception)
            {
                result = DateTime.MinValue;
                return false;
            }
        }
        result = dateTime.Value;
        return true;
    }

    public static bool TryParseToLocalTime(DateTimeOffset? dateTime, out DateTimeOffset result)
    {
        if (dateTime == null)
        {
            result = DateTimeOffset.MinValue;
            return false;
        }

        try
        {
            var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
            result = TimeZoneInfo.ConvertTime(dateTime.Value, jstTimeZone);
            return true;
        }
        catch (System.Exception)
        {
            result = DateTimeOffset.MinValue;
            return false;
        }
    }

    public static bool TryParseToUtcTime(DateTime? dateTime, out DateTime? result)
    {
        if (dateTime == null)
        {
            result = null;
            return false;
        }

        var tokyoZone = TimeZoneInfo.TryFindSystemTimeZoneById("Asia/Tokyo", out var zone) ? zone : null;
        if (tokyoZone == null)
        {
            result = null;
            return false;
        }

        dateTime = DateTime.SpecifyKind(dateTime.Value, DateTimeKind.Unspecified);
        try
        {
            dateTime = TimeZoneInfo.ConvertTimeToUtc(dateTime.Value, tokyoZone);
            result = dateTime;
            return true;
        }
        catch (Exception)
        {
            result = null;
            return false;
        }
    }

    public static bool TryParseToUtcTime(string? dateTime, out DateTime? result)
    {
        if (dateTime == null)
        {
            result = null;
            return false;
        }

        var tokyoZone = TimeZoneInfo.TryFindSystemTimeZoneById("Asia/Tokyo", out var zone) ? zone : null;
        if (tokyoZone == null)
        {
            result = null;
            return false;
        }

        if (DateTime.TryParse(dateTime, out var dateTimeValue))
        {
            dateTimeValue = DateTime.SpecifyKind(dateTimeValue, DateTimeKind.Unspecified);
            try
            {
                dateTimeValue = TimeZoneInfo.ConvertTimeToUtc(dateTimeValue, tokyoZone);
                result = dateTimeValue;
                return true;
            }
            catch (Exception)
            {
                result = null;
                return false;
            }
        }
        result = null;
        return false;
    }
}