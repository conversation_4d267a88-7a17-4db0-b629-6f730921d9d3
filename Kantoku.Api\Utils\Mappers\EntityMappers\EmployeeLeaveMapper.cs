using Kantoku.Api.Dtos.Leave.Response;
using Kantoku.Api.Dtos.Leave.Request;
using Kantoku.Domain.Models;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EmployeeLeaveMapper
{
    #region Entity To Dto

    /// <summary>
    /// Parse employee leave to employee leave response dto
    /// </summary>
    /// <param name="employeeLeave"></param>
    /// <returns></returns>
    public static EmployeeLeaveResponseDto ToEmployeeLeaveResponseDto(this EmployeeLeave employeeLeave)
    {
        if (employeeLeave == null)
        {
            return new EmployeeLeaveResponseDto();
        }
        return new EmployeeLeaveResponseDto
        {
            EmployeeLeaveId = employeeLeave.EmployeeLeaveUid.ToString(),
            BaseLeave = employeeLeave.BaseLeave,
            BaseLeaveExpire = employeeLeave.BaseLeaveExpire.ToString("yyyy-MM-dd"),
            LastRemainLeave = employeeLeave.LastRemainLeave,
            LastRemainLeaveExpire = employeeLeave.LastRemainLeaveExpire?.ToString("yyyy-MM-dd"),
            TotalOrgUsedLeave = employeeLeave.OrgTakenLeave,
            TotalSelfUsedLeave = employeeLeave.SelfTakenLeave,
            TotalUsedLeave = employeeLeave.OrgTakenLeave + employeeLeave.SelfTakenLeave,
            CreateTime = DateTimeHelper.ParseToLocalTime(employeeLeave.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(employeeLeave.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Parse employee leaves to employee leaves response dto
    /// </summary>
    /// <param name="employeeLeaves"></param>
    /// <returns></returns>
    public static IEnumerable<EmployeeLeaveResponseDto> ToEmployeeLeaveResponseDtos(
        this IEnumerable<EmployeeLeave> employeeLeaves)
    {
        if (employeeLeaves == null || !employeeLeaves.Any())
        {
            return [];
        }
        return employeeLeaves.Select(ToEmployeeLeaveResponseDto);
    }

    /// <summary>
    /// Parse employee leaves to employee leaves response dto
    /// </summary>
    /// <param name="employee"></param>
    /// <param name="employeeLeaves"></param>
    /// <returns></returns>
    public static EmployeeLeavesResponseDto ToEmployeeLeavesResponseDto(
        this Employee employee,
        IEnumerable<EmployeeLeave> employeeLeaves)
    {
        if (employee == null)
        {
            return new EmployeeLeavesResponseDto();
        }
        return new EmployeeLeavesResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            EmployeeCode = employee.EmployeeCode,
            EmployeeName = employee.EmployeeName,
            Items = employeeLeaves.ToEmployeeLeaveResponseDtos()
        };
    }

    /// <summary>       
    /// Parse employee leaves to leaves response dto
    /// </summary>
    /// <param name="employeeLeaves"> Employee leaves </param>
    /// <param name="total"> Total records </param>
    /// <param name="pageNum"> Page number </param>
    /// <param name="pageSize"> Page size </param>
    /// <returns> Leaves response dto </returns>
    public static LeavesResponseDto LeavesResponseDtoParser(
        this IEnumerable<EmployeeLeave> employeeLeaves,
        int total,
        int pageNum,
        int pageSize)
    {
        if (employeeLeaves == null || !employeeLeaves.Any())
        {
            return new LeavesResponseDto();
        }
        return new LeavesResponseDto
        {
            Items = employeeLeaves.Select(ToLeaveResponseDto),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = total
        };
    }

    /// <summary>
    /// Parse employee leaves to leaves response dto
    /// </summary>
    /// <param name="employeeLeaves"> Employee leaves </param>
    /// <returns> Leaves response dto </returns>
    public static IEnumerable<LeaveResponseDto> LeavesResponseDtoParser(
        this IEnumerable<EmployeeLeave> employeeLeaves)
    {
        if (employeeLeaves == null || !employeeLeaves.Any())
        {
            return [];
        }
        return employeeLeaves.Select(ToLeaveResponseDto);
    }

    /// <summary>
    /// Parse employee leave to leave response dto
    /// </summary>
    /// <param name="employeeLeave"></param>
    /// <returns></returns>
    public static LeaveResponseDto ToLeaveResponseDto(this EmployeeLeave employeeLeave)
    {
        if (employeeLeave == null)
        {
            return new LeaveResponseDto();
        }
        return new LeaveResponseDto
        {
            EmployeeLeaveId = employeeLeave.EmployeeLeaveUid.ToString(),
            EmployeeId = employeeLeave.EmployeeUid.ToString(),
            EmployeeName = employeeLeave.Employee?.EmployeeName,
            EmployeeCode = employeeLeave.Employee?.EmployeeCode,
            BaseLeave = employeeLeave.BaseLeave,
            BaseLeaveExpire = employeeLeave.BaseLeaveExpire.ToString("yyyy-MM-dd"),
            LastRemainLeave = employeeLeave.LastRemainLeave,
            LastRemainLeaveExpire = employeeLeave.LastRemainLeaveExpire?.ToString("yyyy-MM-dd"),
            TotalOrgUsedLeave = employeeLeave.OrgTakenLeave,
            TotalSelfUsedLeave = employeeLeave.SelfTakenLeave,
            TotalUsedLeave = employeeLeave.OrgTakenLeave + employeeLeave.SelfTakenLeave,
            CreateTime = DateTimeHelper.ParseToLocalTime(employeeLeave.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(employeeLeave.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    #endregion

    #region Dto To Entity

    /// <summary>
    /// Parse create employee leave request dto to employee leave entity
    /// </summary>
    /// <param name="dto"> Create employee leave request dto </param>
    /// <param name="employeeLeave"> Employee leave entity </param>
    /// <returns> Employee leave entity </returns>
    public static void UpdateFromDto(this EmployeeLeave employeeLeave, UpdateEmployeeLeaveRequestDto dto)
    {
        if (employeeLeave == null)
        {
            return;
        }
        employeeLeave.BaseLeave = dto.BaseLeave ?? employeeLeave.BaseLeave;
        employeeLeave.BaseLeaveExpire = DateOnly.TryParse(dto.BaseLeaveExpire, out var date)
            ? date
            : employeeLeave.BaseLeaveExpire;
    }


    #endregion
}

