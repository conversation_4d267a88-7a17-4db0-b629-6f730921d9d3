using EventBus.Interfaces;
using EventBus.Kafka.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace EventBus.Kafka.Extensions;

/// <summary>
/// Service collection extensions for Kafka event bus
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Kafka event producer to the service collection (for API projects)
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddKafkaEventProducer(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure Kafka settings
        var kafkaSection = configuration.GetSection(KafkaConfig.SectionName);
        services.Configure<KafkaConfig>(kafkaSection);

        // Register producer
        services.AddSingleton<IEventProducer, KafkaEventProducer>();

        return services;
    }

    /// <summary>
    /// Adds Kafka event consumer to the service collection (for processor projects)
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddKafkaEventConsumer(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure Kafka settings
        var kafkaSection = configuration.GetSection(KafkaConfig.SectionName);
        services.Configure<KafkaConfig>(kafkaSection);

        // Register consumer
        services.AddSingleton<IEventConsumer, KafkaEventConsumer>();

        return services;
    }

    /// <summary>
    /// Adds Kafka event bus to the service collection (backward compatibility)
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddKafkaEventBus(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure Kafka settings
        var kafkaSection = configuration.GetSection(KafkaConfig.SectionName);
        services.Configure<KafkaConfig>(kafkaSection);

        // Register producer and consumer
        services.AddSingleton<IEventProducer, KafkaEventProducer>();
        services.AddSingleton<IEventConsumer, KafkaEventConsumer>();

        return services;
    }

    /// <summary>
    /// Adds event handler to the service collection
    /// </summary>
    /// <typeparam name="TEvent">Event type</typeparam>
    /// <typeparam name="THandler">Handler type</typeparam>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddEventHandler<TEvent, THandler>(this IServiceCollection services)
        where TEvent : class, IIntegrationEvent
        where THandler : class, IEventHandler<TEvent>
    {
        services.AddScoped<THandler>();
        return services;
    }
}