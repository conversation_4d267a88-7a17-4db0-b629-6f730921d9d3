using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Dtos.Contractor.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Contractor.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Domain.Models;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ContractorController : BaseController
{
    private readonly IContractorService contractorService;
    private readonly IAuditLogService auditLogService;


    public ContractorController(
        ITResponseFactory responseFactory,
        IContractorService contractorService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.contractorService = contractorService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated list of contractors
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>Paginated list of contractors</returns>
    [HttpGet]
    public async Task<GeneralResponse<ContractorsResponseDto>> GetAll([FromQuery] ContractorFilter filter)
    {
        try
        {
            var result = await contractorService.GetContractorByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ContractorsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get contractor details by ID
    /// </summary>
    /// <param name="id">Contractor ID to retrieve</param>
    /// <param name="keyword">Keyword to search for (project code/name)</param>
    /// <param name="pageNum">Page number of project list</param>
    /// <param name="pageSize">Page size of project list</param>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ContractorDetailResponseDto>> GetById(
        [FromRoute] Guid id,
        [FromQuery] string? keyword,
        [FromQuery] int pageNum = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ContractorDetailResponseDto>();

            var result = await contractorService.GetContractorById(id, keyword, pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ContractorDetailResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get contractor logo
    /// </summary>
    /// <param name="id">Contractor ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>Contractor logo</returns>
    [AllowAnonymous]
    [HttpGet("{id}/logo")]
    public async Task<dynamic> GetLogo([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await contractorService.GetLogo(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.CONTRACTOR_LOGO_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }
    /// <summary>
    /// Create a new contractor
    /// </summary>
    /// <param name="requestDto">Contractor creation data</param>
    /// <returns>Created contractor details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<ContractorResponseDto>> CreateContractor([FromForm] CreateContractorRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ContractorResponseDto>();

            var result = await contractorService.CreateContractor(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ContractorResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing contractor
    /// </summary>
    /// <param name="id">Contractor ID to update</param>
    /// <param name="requestDto">Updated contractor data</param>
    /// <returns>Updated contractor details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<ContractorResponseDto>> UpdateContractor([FromRoute] Guid id, [FromForm] UpdateContractorRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ContractorResponseDto>();

            var result = await contractorService.UpdateContractor(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ContractorResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a contractor
    /// </summary>
    /// <param name="id">Contractor ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteContractor([FromRoute] Guid id)
    {
        try
        {
            var result = await contractorService.DeleteContractor(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a contractor
    /// </summary>
    /// <param name="id">Contractor ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetContractorLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Contractor>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
