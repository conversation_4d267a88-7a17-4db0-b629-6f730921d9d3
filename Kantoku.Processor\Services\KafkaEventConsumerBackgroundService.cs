using EventBus.Interfaces;
using EventBus.Kafka.Events;
using Kantoku.Processor.Handlers;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Kantoku.Processor.Services;

/// <summary>
/// Independent background service for Kafka event consumption that works alongside Hangfire
/// This service runs continuously and is separate from Hangfire job scheduling
/// </summary>
public class KafkaEventConsumerBackgroundService : BackgroundService
{
    private readonly IEventConsumer _eventConsumer;
    private readonly ILogger<KafkaEventConsumerBackgroundService> _logger;

    public KafkaEventConsumerBackgroundService(
        IEventConsumer eventConsumer,
        ILogger<KafkaEventConsumerBackgroundService> logger)
    {
        _eventConsumer = eventConsumer;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting independent Kafka event consumer background service...");

        try
        {
            // Subscribe to events
            _eventConsumer.Subscribe<AttendanceEvent, AttendanceEventHandler>();
            _logger.LogInformation("Subscribed to AttendanceEvent");

            // Start the consumer to begin listening for messages
            await _eventConsumer.StartAsync(stoppingToken);
            _logger.LogInformation("Kafka event consumer started successfully");

            // Keep the service running until cancellation is requested
            try
            {
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Kafka event consumer background service is stopping...");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Kafka event consumer background service was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Kafka event consumer background service: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping independent Kafka event consumer background service...");

        try
        {
            await _eventConsumer.StopAsync(cancellationToken);
            _logger.LogInformation("Kafka event consumer stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Kafka event consumer: {ErrorMessage}", ex.Message);
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("Independent Kafka event consumer background service stopped");
    }
}
