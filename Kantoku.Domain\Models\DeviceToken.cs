using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class DeviceToken
{
    public Guid DeviceTokenUid { get; set; }
    public Guid EmployeeUid { get; set; }

    [AuditProperty]
    public string? Platform { get; set; }

    [AuditProperty]
    public string? OsVersion { get; set; }

    [AuditProperty]
    public string? DeviceId { get; set; }

    [AuditProperty]
    public DateTime? LastActive { get; set; }

    [AuditProperty]
    public string? AppVersion { get; set; }

    [AuditProperty]
    public string? FirebaseToken { get; set; }

    public virtual Employee Employee { get; set; } = null!;
}

public static class Platform
{
    public const string MACOS = nameof(MACOS);
    public const string WINDOWS = nameof(WINDOWS);
    public const string WEB = nameof(WEB);
    public const string IOS = nameof(IOS);
    public const string ANDROID = nameof(ANDROID);
}
