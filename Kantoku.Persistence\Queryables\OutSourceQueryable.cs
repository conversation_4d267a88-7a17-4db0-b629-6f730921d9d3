using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class OutSourceQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedOutSourceShifts { get; set; } = false;
    public bool IncludedOutSourcePrice { get; set; } = false;
}

public interface IOutSourceQueryable
{
    IQueryable<OutSource> GetOutSourceQuery(
        OutSourceQueryableOptions options
    );
    IQueryable<OutSource> GetOutSourceQueryIncluded(
        OutSourceQueryableOptions options,
        IQueryable<OutSource>? query = null
    );
    IQueryable<OutSource> GetOutSourceQueryFiltered(
        OutSourceFilter filter,
        OutSourceQueryableOptions options,
        IQueryable<OutSource>? query = null
    );
}

public class OutSourceQueryable(ApplicationDbContext context) :
    BaseQueryable<OutSource>(context), IOutSourceQueryable
{
    public IQueryable<OutSource> GetOutSourceQuery(
        OutSourceQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<OutSource> GetOutSourceQueryIncluded(
        OutSourceQueryableOptions options,
        IQueryable<OutSource>? query = null
    )
    {
        query ??= GetOutSourceQuery(options);

        if (options.IncludedOutSourceShifts || options.IncludedAll)
        {
            query = query.Include(s => s.OutSourceShifts);
        }
        if (options.IncludedOutSourcePrice)
        {
            query = query.Include(s => s.OutSourcePrices);
        }

        return query;
    }

    public IQueryable<OutSource> GetOutSourceQueryFiltered(
        OutSourceFilter filter,
        OutSourceQueryableOptions options,
        IQueryable<OutSource>? query = null
    )
    {
        query ??= GetOutSourceQueryIncluded(options);

        if (filter.Keyword is not null)
        {
            query = query.Where(s => s.OutSourceCode.Contains(filter.Keyword) || s.OutSourceName.Contains(filter.Keyword));
        }
        if (filter.Expertise is not null)
        {
            query = query.Where(s => s.Expertise != null && s.Expertise.Intersect(filter.Expertise).Any());
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(s => s.OrgUid == filter.OrgId);
        }


        return query;
    }
}

