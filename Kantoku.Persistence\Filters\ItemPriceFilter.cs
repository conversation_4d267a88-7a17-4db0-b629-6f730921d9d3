namespace Kantoku.Persistence.Filters;

public class ItemPriceFilter : BaseFilter
{
    /// <summary>
    /// The vendor keyword
    /// </summary>
    public string? VendorKeyword { get; set; }

    /// <summary>
    /// The from date
    /// </summary>
    public string? FromDate { get; set; }

    /// <summary>
    /// The to date
    /// </summary>
    public string? ToDate { get; set; }

    /// <summary>
    /// The price min
    /// </summary>
    public int? PriceMin { get; set; }

    /// <summary>
    /// The price max
    /// </summary>
    public int? PriceMax { get; set; }
}

