using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IProjectRankingCostRepository
{
    Task<IEnumerable<ProjectRankingCost>> GetByProjectId(Guid projectId);
    Task<ProjectRankingCost?> GetById(Guid projectId, Guid rankId);
}

public class ProjectRankingCostRepository : BaseRepository<ProjectRankingCost>, IProjectRankingCostRepository
{

    public ProjectRankingCostRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<IEnumerable<ProjectRankingCost>> GetByProjectId(Guid projectId)
    {
        try
        {
            var query = context.ProjectRankingCosts
                .Where(c => c.ProjectUid == projectId);

            return await query
                .OrderBy(c => c.MinValue)
                .ThenBy(c => c.MaxValue)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all project ranking costs");
            return [];
        }
    }

    public async Task<ProjectRankingCost?> GetById(Guid projectId, Guid rankingId)
    {
        try
        {
            return await context.ProjectRankingCosts
                .Where(c => c.ProjectUid == projectId && c.RankingUid == rankingId)
                .FirstOrDefaultAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting project ranking cost by id: {projectId} and {rankingId}", projectId, rankingId);
            return null;
        }
    }
}
