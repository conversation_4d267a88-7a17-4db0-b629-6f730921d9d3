using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class EmployeeRoleConfiguration(string schema) : IEntityTypeConfiguration<EmployeeRole>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeRole> builder)
    {
        builder.HasKey(e => new { e.EmployeeUid, e.RoleUid }).HasName("pk_employee_role");

        builder.ToTable("employee_role", Schema);

        builder.Property(e => e.EmployeeUid).HasColumnName("employee_uid");
        builder.Property(e => e.RoleUid).HasColumnName("role_uid");

        builder.HasOne(d => d.Employee)
            .WithMany(p => p.EmployeeRoles)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_role_employee_id_fkey");

        builder.HasOne(d => d.Role)
            .WithMany(p => p.EmployeeRoles)
            .HasForeignKey(d => d.RoleUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_role_role_id_fkey");
    }
}
