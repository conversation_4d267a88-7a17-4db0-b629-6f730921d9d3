using System.Text.Json;
using EventBus.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using EventBus.Kafka.Events;
using Kantoku.Processor.Services.Interfaces;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Contexts;
using Microsoft.Extensions.Logging;

namespace Kantoku.Processor.Handlers;

/// <summary>
/// <PERSON><PERSON> check-in events to find the nearest project
/// </summary>
public class AttendanceEventHandler : IEventHandler<AttendanceEvent>
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<AttendanceEventHandler> _logger;
    private readonly IDistributedCache _cache;
    private readonly IGeoCodeService _geoCodeService;

    public AttendanceEventHandler(
        ApplicationDbContext appDbContext,
        ILogger<AttendanceEventHandler> logger,
        IDistributedCache cache,
        IGeoCodeService geoCodeService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _cache = cache;
        _geoCodeService = geoCodeService;
    }

    public async Task HandleAsync(AttendanceEvent @event, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing check-in event for shiftId {ShiftId} at coordinates {Lat}, {Lng}",
                @event.ShiftId, @event.Coordinates.Latitude, @event.Coordinates.Longitude);

            // Validate GPS coordinates
            if (!@event.Coordinates.IsValid)
            {
                _logger.LogWarning("Invalid GPS coordinates provided: {Lat}, {Lng}",
                    @event.Coordinates.Latitude, @event.Coordinates.Longitude);
                return;
            }

            var currentEmployeeShift = await _appDbContext.Set<EmployeeShift>()
                .Where(e => e.EmployeeShiftUid == @event.ShiftId)
                .FirstOrDefaultAsync(cancellationToken);

            if (currentEmployeeShift is null)
            {
                _logger.LogWarning("No employee shift found for shiftId {ShiftId}", @event.ShiftId);
                return;
            }

            var gpsLocation = await _geoCodeService.GetAddressAsync(@event.Coordinates.Latitude!.Value, @event.Coordinates.Longitude!.Value, cancellationToken);
            if (!string.IsNullOrEmpty(gpsLocation))
            {
                _logger.LogInformation("GPS location found for shiftId {ShiftId}: {GpsLocation}", @event.ShiftId, gpsLocation);
                if (@event.IsCheckIn)
                {
                    currentEmployeeShift.CheckInLocation = gpsLocation;
                }
                else
                {
                    currentEmployeeShift.CheckOutLocation = gpsLocation;
                }
            }

            // Get all active projects for the organization
            var projects = await GetProjectListAsync(@event.OrgId, cancellationToken);
            if (!projects.Any())
            {
                _logger.LogWarning("No active projects found for organization {OrgId}", @event.OrgId);
                return;
            }

            // Find the nearest project
            var nearestProject = FindNearestProject(@event.Coordinates, projects);
            if (nearestProject != null)
            {
                currentEmployeeShift.ProjectUid = nearestProject.ProjectUid;
            }
            await _appDbContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing check-in event for shiftId {ShiftId}", @event.ShiftId);
        }
    }

    private async Task<IEnumerable<Project>> GetProjectListAsync(Guid orgId, CancellationToken cancellationToken)
    {
        string cacheKey = $"projectList:{orgId}";
        var cachedProjects = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (!string.IsNullOrEmpty(cachedProjects))
        {
            _logger.LogDebug("Project list for OrgId {OrgId} found in cache.", orgId);
            return JsonSerializer.Deserialize<List<Project>>(cachedProjects) ?? Enumerable.Empty<Project>();
        }

        _logger.LogDebug("Project list for OrgId {OrgId} not found in cache. Fetching from DB.", orgId);
        try
        {
            var projectsFromDb = await _appDbContext.Set<Project>()
                .Where(p => p.OrgUid == orgId)
                .Where(p => !p.IsDeleted)
                .Where(p => p.StatusCode == "STARTED") // Assuming this is the active status
                .Where(p => !string.IsNullOrEmpty(p.Address))
                .ToListAsync(cancellationToken);

            if (!projectsFromDb.Any())
            {
                _logger.LogInformation("No active projects with addresses found for organization {OrgId} in DB.", orgId);
                // Cache an empty list to avoid repeated DB calls for non-existent/empty data
                await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(Enumerable.Empty<Project>()),
                    new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(10)), cancellationToken);
                return [];
            }

            // Get coordinates for projects with addresses
            var projectsWithCoordinates = await GetProjectCoordinatesAsync(projectsFromDb, cancellationToken);

            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1)); // Cache for 1 hour
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(projectsWithCoordinates), cacheEntryOptions, cancellationToken);
            _logger.LogDebug("Project list for OrgId {OrgId} cached.", orgId);

            return projectsWithCoordinates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active projects for organization {OrgId}", orgId);
            return [];
        }
    }

    private Project? FindNearestProject(
        GpsCoordinates checkInCoordinates,
        IEnumerable<Project> projects,
        double threshold = 50.0)
    {
        if (!projects.Any())
            return null;

        var projectDistances = CalculateProjectDistanace(checkInCoordinates, projects, threshold);
        if (!projectDistances.Any())
            return null;

        var closest = projectDistances.OrderBy(d => d.Distance).FirstOrDefault();
        if (closest is null)
            return null;

        return projects.FirstOrDefault(p => p.ProjectUid == closest.ProjectUid);
    }

    private IEnumerable<ProjectDistance> CalculateProjectDistanace(
        GpsCoordinates checkInCoordinates,
        IEnumerable<Project> projects,
        double threshold = 0.01) // 100 meters
    {
        if (checkInCoordinates is null || checkInCoordinates.Latitude is null || checkInCoordinates.Longitude is null)
            yield break;
        if (projects is null || !projects.Any())
            yield break;

        foreach (var project in projects)
        {
            if (project.Latitude is null || project.Longitude is null)
                continue;
            var distance = CalculateDistance(
                checkInCoordinates.Latitude.Value, checkInCoordinates.Longitude.Value,
                project.Latitude.Value, project.Longitude.Value);
            if (distance <= threshold)
                yield return new ProjectDistance(project.ProjectUid, distance);
        }
    }

    private async Task<IEnumerable<Project>> GetProjectCoordinatesAsync(
        IEnumerable<Project> projects,
        CancellationToken cancellationToken)
    {
        var getCoordinateTasks = projects
            .Where(p => (p.Latitude is null || p.Longitude is null) && !string.IsNullOrEmpty(p.Address))
            .Select(async project =>
            {
                var coordinates = await _geoCodeService.GetCoordinatesAsync(project.Address, cancellationToken);
                return new Project
                {
                    ProjectUid = project.ProjectUid,
                    Address = project.Address,
                    Latitude = coordinates?.Latitude,
                    Longitude = coordinates?.Longitude
                };
            });
        return await Task.WhenAll(getCoordinateTasks);
    }

    private static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        // Haversine formula to calculate distance between two GPS coordinates
        const double earthRadiusKm = 6371.0;

        var dLat = DegreesToRadians(lat2 - lat1);
        var dLon = DegreesToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return earthRadiusKm * c;
    }

    private static double DegreesToRadians(double degrees)
    {
        return degrees * Math.PI / 180.0;
    }

    private record ProjectDistance(Guid ProjectUid, double Distance);
}