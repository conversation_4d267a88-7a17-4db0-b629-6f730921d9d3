using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Queryables;

public class PaymentTypeQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IPaymentTypeQueryable
{
    IQueryable<PaymentType> GetPaymentTypesQuery(
        PaymentTypeQueryableOptions options
    );
    IQueryable<PaymentType> GetPaymentTypesQueryIncluded(
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    );
    IQueryable<PaymentType> GetPaymentTypesQueryFiltered(
        PaymentTypeFilter filter,
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    );
}

public class PaymentTypeQueryable(ApplicationDbContext context) :
    BaseQueryable<PaymentType>(context), IPaymentTypeQueryable
{
    public IQueryable<PaymentType> GetPaymentTypesQuery(
        PaymentTypeQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<PaymentType> GetPaymentTypesQueryIncluded(
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    )
    {
        query ??= GetPaymentTypesQuery(options);
        return query;
    }

    public IQueryable<PaymentType> GetPaymentTypesQueryFiltered(
        PaymentTypeFilter filter,
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    )
    {
        query ??= GetPaymentTypesQueryIncluded(options);

        if (filter.Keyword is not null)
        {
            query = query.Where(p => p.PaymentTypeName.Contains(filter.Keyword));
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        return query;
    }
}

