using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Common;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface ICommonService
{
    Task<ResultDto<StatusesResponseDto>> GetStatusByGroup(string group);
    Task<ResultDto<RequestTypesResponseDto>> GetAllRequestTypes();
    Task<ResultDto<LeaveTypesResponseDto>> GetAllLeaveTypes();
    Task<ResultDto<ProjectTypesResponseDto>> GetAllProjectTypes();
    Task<ResultDto<CustomerTypesResponseDto>> GetAllCustomerTypes(ApiFilter.CustomerTypeFilter filter);
}

[Service(ServiceLifetime.Scoped)]
public class CommonService : BaseService<CommonService>, ICommonService
{
    private readonly IStatusRepository StatusRepository;
    private readonly IRequestTypeRepository requestTypeRepository;
    private readonly ILeaveTypeRepository leaveTypeRepository;
    private readonly IProjectTypeRepository projectTypeRepository;
    private readonly ICustomerTypeRepository customerTypeRepository;
    private readonly IFilterMapper<ApiFilter.CustomerTypeFilter, DomainFilter.CustomerTypeFilter> filterMapper;

    public CommonService(
        IStatusRepository StatusRepository,
        IRequestTypeRepository requestTypeRepository,
        ILeaveTypeRepository leaveTypeRepository,
        IProjectTypeRepository projectTypeRepository,
        ICustomerTypeRepository customerTypeRepository,
        IFilterMapper<ApiFilter.CustomerTypeFilter, DomainFilter.CustomerTypeFilter> filterMapper,
        IHttpContextAccessor httpContextAccessor,
        Serilog.ILogger logger) : base(logger, httpContextAccessor)
    {
        this.StatusRepository = StatusRepository;
        this.requestTypeRepository = requestTypeRepository;
        this.leaveTypeRepository = leaveTypeRepository;
        this.projectTypeRepository = projectTypeRepository;
        this.customerTypeRepository = customerTypeRepository;
        this.filterMapper = filterMapper;
    }

    public async Task<ResultDto<StatusesResponseDto>> GetStatusByGroup(string group)
    {
        var statuses = await StatusRepository.GetByGroup(group);
        if (statuses is null || !statuses.Any())
        {
            return new ErrorResultDto<StatusesResponseDto>(ResponseCodeConstant.STATUS_NOT_EXIST);
        }
        var languageCode = GetCurrentLanguageCode();
        var items = statuses
            .Select(s => new StatusResponseDto
            {
                StatusCode = s.StatusCode,
                StatusName = s.TranslatedStatus?.FirstOrDefault(t => t.LanguageCode.ToLower().Equals(languageCode.ToLower()))?.StatusName,
            });
        var result = new StatusesResponseDto
        {
            Items = items,
            PageIndex = 1,
            PageSize = items.Count()
        };
        return new SuccessResultDto<StatusesResponseDto>(result);
    }

    public async Task<ResultDto<RequestTypesResponseDto>> GetAllRequestTypes()
    {
        var languageCode = GetCurrentLanguageCode();
        var requestTypes = await requestTypeRepository.GetAll();
        if (requestTypes is null || !requestTypes.Any())
        {
            return new ErrorResultDto<RequestTypesResponseDto>(ResponseCodeConstant.REQUEST_TYPE_NOT_EXIST);
        }
        
        var result = requestTypes.ToRequestTypesResponseDto(languageCode);
        
        return new SuccessResultDto<RequestTypesResponseDto>(result);
    }

    public async Task<ResultDto<LeaveTypesResponseDto>> GetAllLeaveTypes()
    {
        var leaveTypes = await leaveTypeRepository.GetAll();
        if (leaveTypes is null || !leaveTypes.Any())
        {
            return new ErrorResultDto<LeaveTypesResponseDto>(ResponseCodeConstant.LEAVE_TYPE_NOT_EXIST);
        }
        
        var languageCode = GetCurrentLanguageCode();
        var result = leaveTypes.ToLeaveTypesResponseDto(languageCode);
        
        return new SuccessResultDto<LeaveTypesResponseDto>(result);
    }

    public async Task<ResultDto<ProjectTypesResponseDto>> GetAllProjectTypes()
    {
        var languageCode = GetCurrentLanguageCode();
        var projectTypes = await projectTypeRepository.GetAll(languageCode.ToString());
        if (projectTypes is null || !projectTypes.Any())
        {
            return new ErrorResultDto<ProjectTypesResponseDto>(ResponseCodeConstant.PROJECT_TYPE_NOT_EXIST);
        }
        
        var result = projectTypes.ToProjectTypesResponseDto(languageCode);
        
        return new SuccessResultDto<ProjectTypesResponseDto>(result);
    }

    public async Task<ResultDto<CustomerTypesResponseDto>> GetAllCustomerTypes(ApiFilter.CustomerTypeFilter filter)
    {
        var domainFilter = filterMapper.MapToDomain(filter);
        var (customerTypes, total) = await customerTypeRepository.GetByFilter(domainFilter);
        if (customerTypes is null || !customerTypes.Any())
        {
            return new ErrorResultDto<CustomerTypesResponseDto>(ResponseCodeConstant.CUSTOMER_TYPE_NOT_EXIST);
        }
        var languageCode = GetCurrentLanguageCode();
        var res = customerTypes.Select(c => new CustomerTypeResponseDto
        {
            CustomerTypeCode = c.CustomerTypeCode,
            CustomerTypeName = c.TranslatedCustomerType?.FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.CustomerTypeName,
            CustomerTypeDescription = c.TranslatedCustomerType?.FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.CustomerTypeDescription
        });

        var result = new CustomerTypesResponseDto
        {
            Items = res,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            TotalRecords = total
        };
        return new SuccessResultDto<CustomerTypesResponseDto>(result);
    }
}


