﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IManufacturerRepository
{
    Task<(IEnumerable<Manufacturer>, int)> GetByFilter(DomainFilter.ManufacturerFilter filter, ManufacturerQueryableOptions options);
    Task<Manufacturer?> GetById(Guid manufacturerId, ManufacturerQueryableOptions options);
    Task<Manufacturer?> Create(Manufacturer manufacturer);
    Task<Manufacturer?> Update(Manufacturer manufacturer);
    Task<bool> Delete(Manufacturer manufacturer);
}

public class ManufacturerRepository : BaseRepository<ManufacturerRepository>, IManufacturerRepository
{
    private readonly IManufacturerQueryable queryable;
    public ManufacturerRepository(
        ApplicationDbContext context,
        IManufacturerQueryable queryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(IEnumerable<Manufacturer>, int)> GetByFilter(DomainFilter.ManufacturerFilter filter, ManufacturerQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetManufacturersQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .OrderBy(m => m.ManufacturerCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (result, total);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting manufacturers list");
            return ([], 0);
        }
    }

    public async Task<Manufacturer?> GetById(Guid manufacturerId, ManufacturerQueryableOptions options)
    {
        try
        {
            var query = queryable.GetManufacturersQueryIncluded(options)
                .Where(m => m.ManufacturerUid == manufacturerId);

            var result = await query.FirstOrDefaultAsync();

            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting manufacturer by id");
            return null;
        }
    }

    public async Task<Manufacturer?> Create(Manufacturer manufacturer)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Manufacturers.AddAsync(manufacturer);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(manufacturer.ManufacturerUid, new ManufacturerQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating manufacturer");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Manufacturer?> Update(Manufacturer manufacturer)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Manufacturers.Update(manufacturer);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(manufacturer.ManufacturerUid, new ManufacturerQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating manufacturer");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Delete(Manufacturer manufacturer)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            manufacturer.IsDeleted = true;
            context.Entry(manufacturer).Property(m => m.IsDeleted).IsModified = true;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting manufacturer");
            await transaction.RollbackAsync();
            return false;
        }
    }
}
