using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class StructureQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedParent { get; set; } = false;
    public bool IncludedChildren { get; set; } = false;
    public bool IncludedRoles { get; set; } = false;
    public bool IncludedEmployees { get; set; } = false;

    public StructureQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

    public StructureQueryableOptions SplitQueryOptions()
    {
        IsSplitQuery = true;
        return this;
    }
}

public interface IStructureQueryable
{
    IQueryable<Structure> GetStructureQuery(
        StructureQueryableOptions options
    );

    IQueryable<Structure> GetStructuresQueryIncluded(
        StructureQueryableOptions options,
        IQueryable<Structure>? query = null
    );

    IQueryable<Structure> GetStructuresQueryFiltered(
        StructureFilter filter,
        StructureQueryableOptions options,
        IQueryable<Structure>? query = null
    );
}

public class StructureQueryable(ApplicationDbContext context) :
    BaseQueryable<Structure>(context), IStructureQueryable
{
    public IQueryable<Structure> GetStructureQuery(
        StructureQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(s => s.IsDeleted == false);
        return query;
    }

    public IQueryable<Structure> GetStructuresQueryIncluded(
        StructureQueryableOptions options,
        IQueryable<Structure>? query = null
    )
    {
        query ??= GetStructureQuery(options);
        if (options.IncludedEmployees || options.IncludedAll)
        {
            query = query.Include(s => s.Employees);
        }
        if (options.IncludedParent || options.IncludedAll)
        {
            query = query.Include(s => s.Parent);
        }
        if (options.IncludedChildren || options.IncludedAll)
        {
            query = query.Include(s => s.Children.Where(c => c.IsDeleted == false));
        }
        if (options.IncludedRoles || options.IncludedAll)
        {
            query = query.Include(s => s.Roles)
                .ThenInclude(r => r.RoleFunctions)
                    .ThenInclude(rf => rf.Function);
        }

        return query;
    }

    public IQueryable<Structure> GetStructuresQueryFiltered(
        StructureFilter filter,
        StructureQueryableOptions options,
        IQueryable<Structure>? query = null
    )
    {
        query ??= GetStructuresQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(s => s.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(s => s.StructureName.Contains(filter.Keyword)
            || s.StructureCode.Contains(filter.Keyword));
        }
        return query;
    }


}
