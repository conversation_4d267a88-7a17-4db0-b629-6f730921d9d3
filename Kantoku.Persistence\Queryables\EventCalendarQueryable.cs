using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Queryables;

public class EventCalendarQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IEventCalendarQueryable
{
    IQueryable<EventCalendar> GetEventCalendarQuery(
        EventCalendarQueryableOptions options
    );

    IQueryable<EventCalendar> GetEventCalendarQueryIncluded(
        EventCalendarQueryableOptions options,
        IQueryable<EventCalendar>? query = null
    );

    IQueryable<EventCalendar> GetEventCalendarQueryFiltered(
        EventCalendarFilter filter,
        EventCalendarQueryableOptions options,
        IQueryable<EventCalendar>? query = null
    );
}

public class EventCalendarQueryable(ApplicationDbContext context) :
    BaseQueryable<EventCalendar>(context), IEventCalendarQueryable
{
    public IQueryable<EventCalendar> GetEventCalendarQuery(
        EventCalendarQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<EventCalendar> GetEventCalendarQueryIncluded(
        EventCalendarQueryableOptions options,
        IQueryable<EventCalendar>? query = null
    )
    {
        query ??= GetEventCalendarQuery(options);
        return query;
    }

    public IQueryable<EventCalendar> GetEventCalendarQueryFiltered(
        EventCalendarFilter filter,
        EventCalendarQueryableOptions options,
        IQueryable<EventCalendar>? query = null
    )
    {
        query ??= GetEventCalendarQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => p.EventName.Contains(filter.Keyword));
        }
        if (filter.IsDayOff is not null)
        {
            query = query.Where(p => p.IsDayOff == filter.IsDayOff);
        }
        if (filter.IsRecurring is not null)
        {
            query = query.Where(p => p.IsRecurring == filter.IsRecurring);
        }
        return query;
    }
}
