using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Function.Request;
using Kantoku.Api.Dtos.Function.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class FunctionMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Function entity to a FunctionResponseDto
    /// </summary>
    public static FunctionResponseDto ToFunctionResponseDto(this Function function)
    {
        if (function == null)
            return new FunctionResponseDto();

        return new FunctionResponseDto
        {
            FunctionId = function.FunctionUid.ToString(),
            FunctionName = function.FunctionName,
            HideInMenu = function.HideInMenu,
            FunctionUrl = function.FunctionUrl,
            Component = function.Component,
            Redirect = function.Redirect,
            ParentId = function.ParentUid?.ToString(),
            HideInBreadcrumb = function.HideInBreadcrumb,
            HideChildrenInMenu = function.HideChildrenInMenu,
            Locale = function.Locale,
            DisplayOrder = function.DisplayOrder.HasValue ? (short)function.DisplayOrder.Value : null,
            Icon = function.Icon
        };
    }

    /// <summary>
    /// Maps a Function entity to a SimpleFunctionResponseDto
    /// </summary>
    public static SimpleFunctionResponseDto ToSimpleFunctionResponseDto(this Function function)
    {
        if (function == null)
            return new SimpleFunctionResponseDto();

        return new SimpleFunctionResponseDto
        {
            FunctionId = function.FunctionUid.ToString(),
            FunctionName = function.FunctionName,
            HideInMenu = function.HideInMenu
        };
    }

    /// <summary>
    /// Maps a collection of Function entities to a SimpleFunctionsResponseDto
    /// </summary>
    public static SimpleFunctionsResponseDto ToSimpleFunctionsResponseDto(
        this IEnumerable<Function> functions, 
        int pageNum = 1, 
        int pageSize = 10, 
        int totalRecords = 0)
    {
        if (functions == null)
            return new SimpleFunctionsResponseDto
            {
                Items = Enumerable.Empty<SimpleFunctionResponseDto>(),
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0
            };

        return new SimpleFunctionsResponseDto
        {
            Items = functions.Select(f => f.ToSimpleFunctionResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateFunctionRequestDto to a Function entity
    /// </summary>
    public static Function? ToEntity(this CreateFunctionRequestDto dto)
    {
        if (dto == null)
            return null;

        return new Function
        {
            FunctionUid = GuidHelper.GenerateUUIDv7(),
            FunctionName = dto.FunctionName,
            HideInMenu = dto.HideInMenu,
            FunctionUrl = string.Empty // Required field in entity
        };
    }

    #endregion
} 