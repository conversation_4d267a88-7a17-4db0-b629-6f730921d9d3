using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class ConstructionCost : AuditableEntity
{
    public Guid ConstructionCostUid { get; set; }
    public Guid OrgUid { get; set; }
    public Guid ConstructionUid { get; set; }

    [AuditProperty]
    public DateOnly StartDate { get; set; }

    [AuditProperty]
    public DateOnly EndDate { get; set; }

    [AuditProperty]
    public long TotalCostAmount { get; set; }

    [AuditProperty]
    public int RiskModifiedAmount { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual ConstructionPaymentRequest ConstructionPaymentRequest { get; set; } = null!;

    public virtual ICollection<CategorizedCost> CategorizedCosts { get; set; } = [];
}