using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.EmployeeShift.Request;
using Kantoku.Api.Dtos.EmployeeShift.Response;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EmployeeShiftMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an EmployeeShift entity to a ShiftResponseDto
    /// </summary>
    public static ShiftResponseDto ToShiftResponseDto(this EmployeeShift shift)
    {
        if (shift == null)
            return new ShiftResponseDto();

        var res = new ShiftResponseDto
        {
            EmployeeShiftId = shift.EmployeeShiftUid.ToString(),
            EmployeeId = shift.Employee.EmployeeUid.ToString(),
            EmployeeName = shift.Employee?.EmployeeName,
            ProjectId = shift.Project?.ProjectUid.ToString(),
            ProjectName = shift.Project?.ProjectName,
            WorkingLocation = shift.WorkingLocation,
            ScheduledStartTime = DateTimeHelper.ParseToLocalTime(shift.ScheduledStartTime)?.ToString("HH:mm:ss"),
            ScheduledEndTime = DateTimeHelper.AdjustBeyondTime(shift.ScheduledStartTime, shift.ScheduledEndTime),
            CheckInTime = DateTimeHelper.ParseToLocalTime(shift.CheckInTime)?.ToString("HH:mm:ss"),
            ModifiedCheckInTimeLastModifierType = GetLastModifier(shift.CheckInTimeLastModifier),
            CheckInLocation = shift.CheckInLocation,
            CheckOutTime = DateTimeHelper.AdjustBeyondTime(shift.CheckInTime, shift.CheckOutTime),
            ModifiedCheckOutTimeLastModifierType = GetLastModifier(shift.CheckOutTimeLastModifier),
            AutoCheckOutTime = DateTimeHelper.AdjustBeyondTime(shift.CheckInTime, shift.AutoCheckOutTime),
            CheckOutLocation = shift.CheckOutLocation,
            BreakList = shift.EmployeeShiftBreakTimes?.Select(br => new BreakItemResponseDto
            {
                BreakInTime = DateTimeHelper.ParseToLocalTime(br.BreakInTime)?.ToString("HH:mm:ss"),
                BreakOutTime = DateTimeHelper.AdjustBeyondTime(br.BreakInTime, br.BreakOutTime)
            }),
            ModifiedBreakListLastModifierType = GetLastModifier(shift.BreakTimeLastModifier),
            IsScheduled = shift.ProjectScheduleUid is not null,
            TotalScheduledWorkTime = (float)Math.Round(shift.TotalScheduledWorkTime, 2),
            TotalWorkTime = (float)Math.Round(shift.TotalWorkTime, 2),
            TotalBreakTime = (float)Math.Round(shift.TotalBreakTime, 2),
            TotalOverTime = (float)Math.Round(shift.TotalOverTime, 2),
            AssignedRole = shift.AssignedRole,
            Description = shift.Description,
            IsRequested = shift.IsRequested,
            IsApproved = shift.IsApproved,
            ApprovedBy = shift.Approver?.EmployeeName,
            ApprovedTime = DateTimeHelper.ParseToLocalTime(shift.ApprovedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            CreateTime = DateTimeHelper.ParseToLocalTime(shift.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(shift.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
        var workingDate = shift.ScheduledStartTime ?? shift.CheckInTime;
        res.WorkingDate = DateTimeHelper.ParseToLocalTime(workingDate)?.ToString("yyyy-MM-dd");

        return res;
    }

    /// <summary>
    /// Maps a collection of EmployeeShift entities to a ShiftsResponseDto
    /// </summary>
    public static ShiftsResponseDto ToShiftsResponseDto(
        this IEnumerable<EmployeeShift> shifts,
        IEnumerable<Request> leaveRequests)
    {
        if (shifts == null || !shifts.Any())
            return new ShiftsResponseDto
            {
                Items = [],
                TotalWorkingDays = 0,
                TotalAbsentDays = 0,
                TotalWorkTime = 0,
                TotalOverTime = 0,
                TotalPaidLeaveUsed = 0,
                TotalUnpaidLeaveUsed = 0
            };

        var totalWorkingDays = shifts
            .Where(i => i.CheckInTime is not null)
            .Select(i => DateOnly.FromDateTime(i.CheckInTime!.Value))
            .Distinct()
            .Count();

        var totalAbsentDays = shifts
            .Where(i => i.ScheduledStartTime is not null && i.CheckInTime is null)
            .Select(i => DateOnly.FromDateTime(i.ScheduledStartTime!.Value))
            .Distinct()
            .Count();

        var totalWorkHours = shifts.Sum(i => i.TotalWorkTime);
        var totalOvertime = shifts.Sum(i => i.TotalOverTime);

        var paidLeaveUsed = leaveRequests
            // .Where(l => l.LeaveType.IsPaid)
            .Count();
        // var unpaidLeaveUsed = leaveRequests
        //     .Where(l => l.LeaveType.IsSelf)
        //     .Count();
        var unpaidLeaveUsed = 0;

        var items = shifts.Select(s => s.ToShiftResponseDto())
            .ToList();

        return new ShiftsResponseDto
        {
            Items = items,
            TotalWorkingDays = totalWorkingDays,
            TotalAbsentDays = totalAbsentDays,
            TotalWorkTime = (float)Math.Round(totalWorkHours, 2),
            TotalOverTime = (float)Math.Round(totalOvertime, 2),
            TotalPaidLeaveUsed = paidLeaveUsed,
            TotalUnpaidLeaveUsed = unpaidLeaveUsed
        };
    }

    /// <summary>
    /// Maps a collection of EmployeeShifts to WorksiteShiftsResponseDto
    /// </summary>
    /// <param name="shifts">The collection of EmployeeShifts to map</param>
    /// <param name="project">The project to map</param>
    /// <param name="fromDate">The start date of the report</param>
    /// <returns>A collection of WorksiteShiftsResponseDto</returns>
    public static WorksiteShiftsResponseDto ToWorksiteShiftsResponseDto(
        this IEnumerable<EmployeeShift> shifts,
        Project project,
        DateOnly fromDate)
    {
        if (shifts == null || !shifts.Any())
            return new WorksiteShiftsResponseDto();

        var items = shifts.Select(s => s.ToShiftResponseDto());
        var hasReport = project.ProjectDailyReports?
            .Where(r => r.ReportDate == fromDate)
            .Any() ?? false;

        return new WorksiteShiftsResponseDto
        {
            ProjectId = project.ProjectUid.ToString(),
            HasReport = hasReport,
            Items = items
        };
    }

    /// <summary>
    /// Maps a collection of EmployeeShifts to WorksiteShiftsResponseDto
    /// </summary>
    public static WorksitesShiftsResponseDto ToWorksitesShiftsResponseDto(
        this IEnumerable<EmployeeShift> shifts,
        Project project,
        DateOnly fromDate,
        IEnumerable<Ranking>? rankings = null)
    {
        if (shifts == null || !shifts.Any())
            return new WorksitesShiftsResponseDto
            {
                Items = []
            };

        var hasReport = project.ProjectDailyReports?
            .Where(r => r.ReportDate == fromDate)
            .Any() ?? false;

        var shiftsByProject = shifts.GroupBy(s => s.ProjectUid).Select(g => new WorksiteShiftsResponseDto
        {
            ProjectId = g.Key.ToString(),
            HasReport = hasReport,
            Items = g.Select(s => s.ToShiftResponseDto())
        });

        return new WorksitesShiftsResponseDto
        {
            Items = shiftsByProject
        };
    }

    /// <summary>
    /// Maps an EmployeeShift entity to a SimpleShiftInfoResponseDto
    /// </summary>
    public static SimpleShiftInfoResponseDto ToSimpleShiftInfoResponseDto(this EmployeeShift shift)
    {
        if (shift == null)
            return new SimpleShiftInfoResponseDto();

        string? workingDate = null;
        if (shift.ScheduledStartTime.HasValue)
            workingDate = shift.ScheduledStartTime.Value.ToString("yyyy-MM-dd");
        else if (shift.CheckInTime.HasValue)
            workingDate = shift.CheckInTime.Value.ToString("yyyy-MM-dd");

        return new SimpleShiftInfoResponseDto
        {
            EmployeeShiftId = shift.EmployeeShiftUid.ToString(),
            ProjectId = shift.ProjectUid.ToString(),
            ProjectName = shift.Project?.ProjectName,
            WorkingLocation = shift.WorkingLocation,
            WorkingDate = workingDate,
            ScheduledStartTime = shift.ScheduledStartTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            CheckInTime = shift.CheckInTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            CheckInLocation = shift.CheckInLocation,
            ScheduledEndTime = shift.ScheduledEndTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            CheckOutTime = shift.CheckOutTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            CheckOutLocation = shift.CheckOutLocation,
            BreakList = MapBreakTimes(shift.EmployeeShiftBreakTimes),
            CreateTime = shift.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = shift.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    private static IEnumerable<BreakItemResponseDto> MapBreakTimes(IEnumerable<EmployeeShiftBreakTime>? breakTimes)
    {
        if (breakTimes == null || !breakTimes.Any())
            return [];

        return breakTimes.Select(bt => new BreakItemResponseDto
        {
            BreakInTime = bt.BreakInTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            BreakOutTime = bt.BreakOutTime?.ToString("yyyy-MM-dd HH:mm:ss")
        });
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateShiftRequestDto to an EmployeeShift entity
    /// Used for creating employee shift
    /// </summary>
    /// <param name="dto">The CreateShiftRequestDto to map</param>
    /// <param name="employeeId">The employee id</param>
    /// <param name="standardWorkingHours">The standard working hours</param>
    /// <param name="monthlyReport">The monthly report</param>
    /// <returns>The mapped EmployeeShift entity</returns>
    public static EmployeeShift? ToEntity(
        this CreateShiftRequestDto dto,
        Guid employeeId,
        float standardWorkingHours,
        MonthlyReport? monthlyReport = null)
    {
        if (dto == null || dto.ProjectId == Guid.Empty || employeeId == Guid.Empty)
            return null;

        var entity = new EmployeeShift
        {
            EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = employeeId,
            ProjectUid = dto.ProjectId,
            WorkingLocation = dto.WorkingLocation,
            CheckInTime = DateTime.TryParse(dto.CheckInTime, out var checkInTime)
                ? checkInTime
                : null,
            CheckInTimeLastModifier = (int)LastModifier.Author,
            CheckOutTime = DateTime.TryParse(dto.CheckOutTime, out var checkOutTime)
                ? checkOutTime
                : null,
            CheckOutTimeLastModifier = (int)LastModifier.Author,
            EmployeeShiftBreakTimes = dto.BreakList?.Select(b => new EmployeeShiftBreakTime
            {
                BreakInTime = DateTime.TryParse(b.BreakInTime, out var breakInTime)
                    ? breakInTime
                    : null,
                BreakOutTime = DateTime.TryParse(b.BreakOutTime, out var breakOutTime)
                    ? breakOutTime
                    : null,
            }).ToList() ?? [],
            BreakTimeLastModifier = (int)LastModifier.Author,
            Description = dto.Description,
            TotalScheduledWorkTime = standardWorkingHours,
            IsRequested = false,
            IsApproved = false,
            IsDeleted = false
        };
        if (monthlyReport is not null)
        {
            entity.MonthlyReportUid = monthlyReport.MonthlyReportUid;

        }
        else
        {
            monthlyReport = new MonthlyReport
            {
                MonthlyReportUid = GuidHelper.GenerateUUIDv7(),
                EmployeeUid = employeeId,
                ReportFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1),
                ReportTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                        .AddMonths(1)
                        .AddDays(-1),
            };
            entity.MonthlyReport = monthlyReport;
            entity.MonthlyReportUid = monthlyReport.MonthlyReportUid;
        }

        entity.CalculateDailyWorkTime();
        return entity;
    }

    /// <summary>
    /// Maps a UnscheduledCheckInRequestDto to an EmployeeShift entity
    /// Used for creating unscheduled check-in
    /// </summary>
    /// <param name="dto">The UnscheduledCheckInRequestDto to map</param>
    /// <param name="employee">The employee</param>
    /// <param name="monthlyReport">The monthly report</param>
    /// <returns>The mapped EmployeeShift entity</returns>
    public static EmployeeShift? ToEntity(this UnscheduledCheckInRequestDto dto, Employee employee, MonthlyReport? monthlyReport = null)
    {
        if (dto == null || employee == null)
            return null;

        var newShift = new EmployeeShift
        {
            EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = employee.EmployeeUid,
            ProjectUid = dto.ProjectId,
            WorkingLocation = dto.WorkingLocation,
            CheckInTime = DateTime.Now,
            TotalScheduledWorkTime = employee.StandardWorkingHours,
            IsRequested = false,
            IsApproved = false,
            IsDeleted = false
        };
        if (monthlyReport is null)
        {
            var newReport = new MonthlyReport
            {
                MonthlyReportUid = GuidHelper.GenerateUUIDv7(),
                EmployeeUid = employee.EmployeeUid,
                ReportFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1),
                ReportTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                        .AddMonths(1)
                        .AddDays(-1),
            };
            newShift.MonthlyReportUid = newReport.MonthlyReportUid;
            newShift.MonthlyReport = newReport;
            newShift.TotalScheduledWorkTime = employee.StandardWorkingHours;
        }
        return newShift;
    }

    /// <summary>
    /// Creates an EmployeeShift entity from a SynchronizeShiftRequestItem
    /// Used for synchronizing employee shift
    /// </summary>
    /// <param name="item">The SynchronizeShiftRequestItem to map</param>
    /// <param name="employeeUid">The employee uid</param>
    /// <returns>The mapped EmployeeShift entity</returns>
    public static EmployeeShift? ToEntity(this SynchronizeShiftRequestItem item, Guid employeeUid)
    {
        if (item == null)
            return null;

        if (item.ProjectId == Guid.Empty)
            return null;

        var entity = new EmployeeShift
        {
            EmployeeShiftUid = item.EmployeeShiftId is null
                ? GuidHelper.GenerateUUIDv7()
                : item.EmployeeShiftId.Value,
            EmployeeUid = employeeUid,
            ProjectUid = item.ProjectId,
            WorkingLocation = item.WorkingLocation,
            CheckInTime = DateTime.TryParse(item.CheckInTime, out var checkInTime)
                ? checkInTime
                : null,
            CheckInTimeLastModifier = (int)LastModifier.Author,
            CheckInLocation = item.CheckInCoordinate?.ToString(),
            CheckOutTime = DateTime.TryParse(item.CheckOutTime, out var checkOutTime)
                ? checkOutTime
                : null,
            CheckOutTimeLastModifier = (int)LastModifier.Author,
            CheckOutLocation = item.CheckOutCoordinate?.ToString(),
            EmployeeShiftBreakTimes = item.Breaks?.Select(b => new EmployeeShiftBreakTime
            {
                BreakInTime = DateTime.TryParse(b.BreakInTime, out var breakInTime)
                    ? breakInTime
                    : null,
                BreakOutTime = DateTime.TryParse(b.BreakOutTime, out var breakOutTime)
                    ? breakOutTime
                    : null,
            }).ToList() ?? [],
            BreakTimeLastModifier = (int)LastModifier.Author,
            Description = item.Description,
        };
        entity.CalculateDailyWorkTime();
        return entity;
    }

    /// <summary>
    /// Maps a CreateScheduledEmployeeShiftRequestDto to an EmployeeShift entity
    /// Used for creating scheduled employee shift
    /// </summary>
    /// <param name="dto">The CreateScheduledEmployeeShiftRequestDto to map</param>
    /// <param name="projectSchedule">The project schedule</param>
    /// <param name="monthlyReport">The monthly report</param>
    /// <returns>The mapped EmployeeShift entity</returns>
    public static EmployeeShift? ToEntity(
        this CreateScheduledEmployeeShiftRequestDto dto,
        ProjectSchedule projectSchedule,
        MonthlyReport? monthlyReport = null)
    {
        if (dto == null)
            return null;

        var scheduledStartTime = new DateTime(
            projectSchedule.WorkingDate.Year,
            projectSchedule.WorkingDate.Month,
            projectSchedule.WorkingDate.Day,
            dto.StartTime.Hour,
            dto.StartTime.Minute,
            0);

        var scheduledEndTime = new DateTime(
            projectSchedule.WorkingDate.Year,
            projectSchedule.WorkingDate.Month,
            projectSchedule.WorkingDate.Day,
            dto.EndTime.Hour,
            dto.EndTime.Minute,
            0);

        monthlyReport ??= new MonthlyReport
        {
            MonthlyReportUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = dto.EmployeeId,
            ReportFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1),
            ReportTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                   .AddMonths(1)
                   .AddDays(-1),
            TotalWorkDays = default,
            TotalOffDays = default,
            TotalWorkHours = default,
            TotalOvertime = default,
            SelfLeaveUsed = default,
            OrgLeaveUsed = default,
            IsRequested = default,
            IsApproved = default,
        };

        return new EmployeeShift
        {
            EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = dto.EmployeeId,
            ProjectUid = projectSchedule.ProjectUid,
            MonthlyReportUid = monthlyReport.MonthlyReportUid,
            ProjectScheduleUid = projectSchedule.ProjectScheduleUid,
            ScheduledStartTime = scheduledStartTime,
            ScheduledEndTime = scheduledEndTime,
            IsRequested = default,
            IsApproved = default,
            IsDeleted = default,
        };
    }

    /// <summary>
    /// Maps a CreateScheduledEmployeeShiftRequestDto to an EmployeeShift entity
    /// Used for creating scheduled employee shift
    /// </summary>
    /// <param name="dto">The CreateScheduledEmployeeShiftRequestDto to map</param>
    /// <param name="projectSchedule">The project schedule</param>
    /// <param name="monthlyReport">The monthly report</param>
    /// <returns>The mapped EmployeeShift entity</returns>
    public static EmployeeShift? ToEntity(
        this CreateScheduledEmployeeShiftRequestDto2 dto,
        ProjectSchedule? projectSchedule = null,
        MonthlyReport? monthlyReport = null)
    {
        if (dto == null)
            return null;

        projectSchedule ??= new ProjectSchedule
        {
            ProjectScheduleUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = dto.ProjectId,
            WorkingDate = dto.WorkingDate,
            PlannedWorkload = 0,
            PresignedWorkload = 0,
            IsDeleted = false,
        };

        var scheduledStartTime = new DateTime(
            projectSchedule.WorkingDate.Year,
            projectSchedule.WorkingDate.Month,
            projectSchedule.WorkingDate.Day,
            dto.StartTime.Hour,
            dto.StartTime.Minute,
            0);

        var scheduledEndTime = new DateTime(
            projectSchedule.WorkingDate.Year,
            projectSchedule.WorkingDate.Month,
            projectSchedule.WorkingDate.Day,
            dto.EndTime.Hour,
            dto.EndTime.Minute,
            0);

        monthlyReport ??= new MonthlyReport
        {
            MonthlyReportUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = dto.EmployeeId,
            ReportFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1),
            ReportTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                   .AddMonths(1)
                   .AddDays(-1),
            TotalWorkDays = default,
            TotalOffDays = default,
            TotalWorkHours = default,
            TotalOvertime = default,
            SelfLeaveUsed = default,
            OrgLeaveUsed = default,
            IsRequested = default,
            IsApproved = default,
        };

        return new EmployeeShift
        {
            EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = dto.EmployeeId,
            ProjectUid = projectSchedule.ProjectUid,
            MonthlyReportUid = monthlyReport.MonthlyReportUid,
            ProjectScheduleUid = projectSchedule.ProjectScheduleUid,
            ScheduledStartTime = scheduledStartTime,
            ScheduledEndTime = scheduledEndTime,
            IsRequested = default,
            IsApproved = default,
            IsDeleted = default,
            ProjectSchedule = projectSchedule,
            MonthlyReport = monthlyReport,
        };
    }

    public static void CheckIn(this EmployeeShift shift, string? checkInLocation = null)
    {
        if (shift.CheckInTime.HasValue)
            return;

        shift.CheckInTime = DateTime.Now;
        shift.CheckInLocation = checkInLocation;
    }

    public static void CheckOut(this EmployeeShift shift, string? checkOutLocation = null)
    {
        if (shift.CheckOutTime.HasValue)
            return;

        shift.CheckOutTime = DateTime.Now;
        shift.CheckOutLocation = checkOutLocation;
        if (shift.EmployeeShiftBreakTimes != null && shift.EmployeeShiftBreakTimes.Count > 0)
        {
            foreach (var breakTime in shift.EmployeeShiftBreakTimes)
            {
                if (breakTime.BreakOutTime is null && breakTime.BreakInTime.HasValue)
                    breakTime.BreakOutTime = DateTime.Now;
            }
        }
        else
        {
            shift.EmployeeShiftBreakTimes = AdjustBreakTime(shift, shift.TotalScheduledWorkTime);
        }
        shift.CalculateDailyWorkTime();
    }


    public static void BreakIn(this EmployeeShift shift)
    {
        var breakList = shift.EmployeeShiftBreakTimes;

        if (breakList is not null && breakList.Any(b => b.BreakInTime is not null && b.BreakOutTime is null))
        {
            return;
        }
        if (breakList is not null && breakList.Count == 3)
        {
            return;
        }
        var newBreak = new EmployeeShiftBreakTime
        {
            BreakInTime = DateTime.Now
        };
        breakList ??= [];
        breakList.Add(newBreak);
        shift.EmployeeShiftBreakTimes = breakList;
    }

    public static void BreakOut(this EmployeeShift shift)
    {
        var breakList = shift.EmployeeShiftBreakTimes?
            .OrderBy(b => b.BreakInTime)
            .ToList();

        if (breakList is null || breakList.Count == 0)
            return;

        if (!breakList.Any(b => b.BreakInTime.HasValue && b.BreakOutTime is null))
        {
            return;
        }
        if (breakList.Count == 3)
        {
            return;
        }
        var lastBreak = breakList.LastOrDefault();
        if (lastBreak is not null)
        {
            lastBreak.BreakOutTime = DateTime.Now;
        }
    }

    /// <summary>
    /// Maps a collection of CreateShiftRequestDto to a collection of EmployeeShift entities
    /// </summary>
    /// <param name="dto">The collection of CreateShiftRequestDto to map</param>
    /// <returns>The collection of EmployeeShift entities</returns>
    public static IEnumerable<EmployeeShift> ToEntities(
        this IEnumerable<RepresentativeCreateShiftRequestDto> dto)
    {
        foreach (var item in dto.Where(i => i.EmployeeId != Guid.Empty))
        {
            var shift = item.ToEntity(item.EmployeeId, item.RequiredWorkTime);
            if (shift is null)
            {
                continue;
            }
            yield return shift;
        }
    }

    /// <summary>
    /// Maps a collection of SynchronizeShiftRequestItem to a collection of EmployeeShift entities
    /// </summary>
    /// <param name="dto">The collection of SynchronizeShiftRequestItem to map</param>
    /// <param name="employeeShifts">The collection of EmployeeShift entities to update</param>
    /// <param name="currentEmployeeUid">The current employee uid</param>
    /// <returns>The collection of EmployeeShift entities</returns>
    public static IEnumerable<EmployeeShift> UpdateFromDto(
        this IEnumerable<EmployeeShift> employeeShifts,
        IEnumerable<SynchronizeShiftRequestItem> dto,
        Guid currentEmployeeUid)
    {
        foreach (var item in dto.Where(i => i.EmployeeShiftId is not null && i.EmployeeShiftId != Guid.Empty))
        {
            var shift = employeeShifts
                .Where(s => s.EmployeeShiftUid == item.EmployeeShiftId)
                .FirstOrDefault();
            if (shift is null)
            {
                continue;
            }

            var isSelf = shift.EmployeeUid == currentEmployeeUid;
            if (DateTime.TryParse(item.CheckInTime, out var checkInTime))
            {
                shift.CheckInTime = checkInTime;
                shift.CheckInTimeLastModifier = (int)LastModifier.Author;
            }
            if (DateTime.TryParse(item.CheckOutTime, out var checkOutTime))
            {
                shift.CheckOutTime = checkOutTime;
                shift.CheckOutTimeLastModifier = (int)LastModifier.Author;
            }
            shift.WorkingLocation = item.WorkingLocation;
            shift.Description = item.Description;
            if (item.Breaks != null)
            {
                shift.EmployeeShiftBreakTimes = item.Breaks?.Select(b =>
                    {
                        return new EmployeeShiftBreakTime
                        {
                            BreakInTime = DateTime.TryParse(b.BreakInTime, out var breakInTime)
                                ? breakInTime
                                : null,
                            BreakOutTime = DateTime.TryParse(b.BreakOutTime, out var breakOutTime)
                                ? breakOutTime
                                : null,
                        };
                    }).ToList() ?? [];
            }
            shift.CalculateDailyWorkTime();
            yield return shift;
        }
    }

    /// <summary>
    /// Updates an EmployeeShift entity from an UpdateShiftRequestDto
    /// Used for updating employee shift
    /// </summary>
    /// <param name="entity">The EmployeeShift entity to update</param>
    /// <param name="dto">The UpdateShiftRequestDto to update from</param>
    /// <param name="currentEmployeeUid">The current employee uid to determine if the shift is self-edited</param>
    public static void UpdateFromDto(this EmployeeShift entity, UpdateShiftRequestDto dto, Guid currentEmployeeUid)
    {
        if (entity == null || dto == null)
            return;

        entity.ProjectUid = dto.ProjectId ?? entity.ProjectUid;
        entity.WorkingLocation = dto.WorkingLocation ?? entity.WorkingLocation;
        entity.Description = dto.Description ?? entity.Description;

        if (dto.CheckInTime != null && DateTime.TryParse(dto.CheckInTime, out var parsedCheckInTime))
        {
            entity.CheckInTime = parsedCheckInTime;
            entity.CheckInTimeLastModifier = currentEmployeeUid == entity.EmployeeUid
                ? (int)LastModifier.Author
                : (int)LastModifier.Manager;
        }

        if (dto.CheckOutTime != null && DateTime.TryParse(dto.CheckOutTime, out var parsedCheckOutTime))
        {
            entity.CheckOutTime = parsedCheckOutTime;
            entity.CheckOutTimeLastModifier = currentEmployeeUid == entity.EmployeeUid
                ? (int)LastModifier.Author
                : (int)LastModifier.Manager;
        }

        if (dto.BreakList != null)
        {
            var breakTimes = dto.BreakList.Select(b => new EmployeeShiftBreakTime
            {
                BreakInTime = DateTime.TryParse(b.BreakInTime, out var breakInTime)
                    ? breakInTime
                    : null,
                BreakOutTime = DateTime.TryParse(b.BreakOutTime, out var breakOutTime)
                    ? breakOutTime
                    : null,
            }).ToList();

            entity.EmployeeShiftBreakTimes = breakTimes;
            entity.BreakTimeLastModifier = currentEmployeeUid == entity.EmployeeUid
                ? (int)LastModifier.Author
                : (int)LastModifier.Manager;
        }

        if (entity.EmployeeShiftBreakTimes is null || entity.EmployeeShiftBreakTimes.Count == 0)
        {
            entity.EmployeeShiftBreakTimes = AdjustBreakTime(entity, entity.TotalScheduledWorkTime);
        }
        entity.CalculateDailyWorkTime();
    }

    /// <summary>
    /// Updates an EmployeeShift entity from an UpdateShiftRequestDto
    /// Used for updating employee shift
    /// </summary>
    /// <param name="entities">The EmployeeShift entities to update</param>
    /// <param name="dtos">The UpdateShiftRequestDto to update from</param>
    /// <param name="currentEmployeeUid">The current employee uid to determine if the shift is self-edited</param>
    public static void UpdateFromDto(
        this IEnumerable<EmployeeShift> entities,
        IEnumerable<RepresentativeUpdateShiftRequestDto> dtos,
        Guid currentEmployeeUid)
    {
        if (entities == null || dtos == null)
            return;

        foreach (var entity in entities)
        {
            var matchedDto = dtos
                .Where(d => d.ShiftId == entity.EmployeeShiftUid)
                .FirstOrDefault();
            if (matchedDto is null)
            {
                continue;
            }
            else if (matchedDto.IsDeleted)
            {
                entity.IsDeleted = true;
            }
            else
            {
                entity.UpdateFromDto(matchedDto, currentEmployeeUid);
            }
        }
    }

    /// <summary>
    /// Clone an EmployeeShift entity
    /// </summary>
    /// <param name="shift">The EmployeeShift entity to clone</param>
    /// <param name="projectScheduleId">The project schedule id</param>
    /// <param name="date">The date to clone the shift to</param>
    /// <returns>The cloned EmployeeShift entity</returns>
    public static EmployeeShift? Clone(this EmployeeShift shift, Guid? projectScheduleId, DateOnly date)
    {
        if (shift.ScheduledStartTime is null || shift.ScheduledEndTime is null || projectScheduleId is null)
        {
            return null;
        }
        var newScheduledStartTime = new DateTime(
            date.Year,
            date.Month,
            date.Day,
            shift.ScheduledStartTime.Value.Hour,
            shift.ScheduledStartTime.Value.Minute,
            0);
        var newScheduledEndTime = new DateTime(
            date.Year,
            date.Month,
            date.Day,
            shift.ScheduledEndTime.Value.Hour,
            shift.ScheduledEndTime.Value.Minute,
            0);
        return new EmployeeShift
        {
            EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
            EmployeeUid = shift.EmployeeUid,
            ProjectUid = shift.ProjectUid,
            ProjectScheduleUid = projectScheduleId,
            ScheduledStartTime = newScheduledStartTime,
            ScheduledEndTime = newScheduledEndTime,
            TotalScheduledWorkTime = shift.TotalScheduledWorkTime,
            AssignedRole = shift.AssignedRole,
        };
    }

    #endregion

    #region Helper methods

    private static string? GetLastModifier(int? lastModifier)
    {
        return lastModifier switch
        {
            (int)LastModifier.Author => "AUTHOR",
            (int)LastModifier.Manager => "MANAGER",
            (int)LastModifier.System => "SYSTEM",
            (int)LastModifier.Job => "JOB",
            _ => null,
        };
    }

    public static int GetLastModifier(bool isAuthor, bool isManager)
    {
        if (isAuthor)
        {
            return (int)LastModifier.Author;
        }
        if (isManager)
        {
            return (int)LastModifier.Manager;
        }
        return (int)LastModifier.System;
    }

    /// <summary>
    /// Calculate the daily work time of the shift
    /// </summary>
    /// <param name="originalShift">The original shift</param>
    private static void CalculateDailyWorkTime(this EmployeeShift originalShift)
    {
        var checkInTime = originalShift.CheckInTime;
        var checkOutTime = originalShift.CheckOutTime ?? originalShift.AutoCheckOutTime;
        var breakTimes = originalShift.EmployeeShiftBreakTimes;

        if (checkInTime is null || checkOutTime is null)
        {
            return;
        }

        var checkedInDuration = (TimeSpan)(checkOutTime - checkInTime);
        TimeSpan breakDuration = TimeSpan.Zero;
        foreach (var breakTime in breakTimes ?? [])
        {
            if (breakTime.BreakInTime.HasValue && breakTime.BreakOutTime.HasValue)
            {
                breakDuration += breakTime.BreakOutTime.Value - breakTime.BreakInTime.Value;
            }
        }
        if (breakDuration > checkedInDuration)
        {
            return;
        }

        // Calculate raw break time in hours
        double breakTimeHours = breakDuration.TotalSeconds / 3600.0;
        originalShift.TotalBreakTime = (float)(Math.Floor(breakTimeHours * 20.0) / 20.0);

        // Calculate raw net work time in hours (gross work duration - break time)
        double netWorkTimeHours = (checkedInDuration.TotalSeconds / 3600.0) - breakTimeHours;
        originalShift.TotalWorkTime = (float)(Math.Floor(netWorkTimeHours * 20.0) / 20.0);

        // // Initialize TotalOverTime. 0.0f is inherently a 0.05 step.
        // originalShift.TotalOverTime = 0.0f;

        // if (originalShift.TotalWorkTime > originalShift.TotalScheduledWorkTime)
        // {
        //     // Calculate raw overtime. originalShift.TotalWorkTime is already rounded.
        //     // TotalScheduledWorkTime is float, cast to double for precision in calculation.
        //     double rawOverTime = (double)originalShift.TotalWorkTime - (double)originalShift.TotalScheduledWorkTime;
        //     originalShift.TotalOverTime = (float)(Math.Floor(rawOverTime * 20.0) / 20.0);

        //     // Cap TotalWorkTime at TotalScheduledWorkTime, ensuring the capped value also adheres to the 0.05 rounding rule.
        //     originalShift.TotalWorkTime = (float)(Math.Floor((double)originalShift.TotalScheduledWorkTime * 20.0) / 20.0);
        // }
    }

    /// <summary>
    /// Adjust the break time of the shift based on the required work time
    /// </summary>
    /// <param name="shift">The shift to adjust</param>
    /// <param name="requiredWorkTime">The required work time</param>
    /// <returns>The adjusted break time</returns>
    private static List<EmployeeShiftBreakTime> AdjustBreakTime(EmployeeShift shift, float requiredWorkTime)
    {
        var breakTimes = new List<EmployeeShiftBreakTime>();

        if (shift.CheckInTime == null || (shift.CheckOutTime ?? shift.AutoCheckOutTime) == null)
            return breakTimes;

        var checkIn = shift.CheckInTime.Value;
        var checkOut = (shift.CheckOutTime ?? shift.AutoCheckOutTime)!.Value;
        var totalCheckInMinutes = (checkOut - checkIn).TotalMinutes;
        var totalCheckInHours = totalCheckInMinutes / 60.0;

        // 1. total check in time < 2 hours -> no breaks required
        if (totalCheckInHours < 2)
            return breakTimes;

        // 2. total check in time > 2 and < 4 and required worktime != 8h -> add 15m breaks from the 2nd hour,
        if (totalCheckInHours > 2)
        {
            if (requiredWorkTime != 8)
            {
                // Add 15m break starting from the 2nd hour
                var breakStart = checkIn.AddHours(2);
                var breakEnd = breakStart.AddMinutes(15);
                if (breakEnd > checkOut) breakEnd = checkOut;
                breakTimes.Add(new EmployeeShiftBreakTime
                {
                    BreakInTime = breakStart,
                    BreakOutTime = breakEnd
                });
            }
        }

        // 4. total check in time > 4 and < 6, add 1h breaks from the 4th hour.
        if (totalCheckInHours > 4)
        {
            var breakStart = checkIn.AddHours(4);
            var breakEnd = breakStart.AddHours(1);
            if (breakEnd > checkOut) breakEnd = checkOut;
            breakTimes.Add(new EmployeeShiftBreakTime
            {
                BreakInTime = breakStart,
                BreakOutTime = breakEnd
            });
        }

        // 5. total check in time > 6 and < 8 and required worktime = 8, add no break
        if (totalCheckInHours > 6)
        {
            if (requiredWorkTime == 8)
            {
                return breakTimes;
            }
            // 6. total check in time > 6 and < 8 and required worktime !=8, add 15m breaks from the 6th hour.
            else
            {
                var breakStart = checkIn.AddHours(6);
                var breakEnd = breakStart.AddMinutes(15);
                if (breakEnd > checkOut) breakEnd = checkOut;
                breakTimes.Add(new EmployeeShiftBreakTime
                {
                    BreakInTime = breakStart,
                    BreakOutTime = breakEnd
                });
            }
        }

        return breakTimes;
    }

    #endregion
}