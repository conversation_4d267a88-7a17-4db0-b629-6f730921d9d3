using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class LanguageConfiguration(string schema) : IEntityTypeConfiguration<Language>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Language> builder)
    {
        builder.HasKey(e => e.LanguageUid).HasName("language_pkey");

        builder.ToTable("language", Schema);

        builder.Property(e => e.LanguageUid)
            .HasColumnName("language_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.LanguageCode)
            .HasColumnName("language_code");
        builder.Property(e => e.LanguageName)
            .HasColumnName("language_name");
        builder.Property(e => e.Status).HasColumnName("status");
    }
}
