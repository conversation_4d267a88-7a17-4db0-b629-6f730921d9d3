using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Notification;
using Kantoku.SharedKernel.Helpers;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Persistence.Queryables;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.Api.Dtos.Device.Request;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Api.Utils.Constants;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface INotificationService
{
    Task<ResultDto<Guid>> CreateNotification(CreateNotificationRequestDto notificationDto);
    Task<ResultDto<bool>> UpdateNotification(Guid notificationId, UpdateNotificationRequestDto notificationDto);
    Task<ResultDto<bool>> DeleteNotification(Guid notificationId);
    Task<ResultDto<bool>> PublishNotification(Guid notificationId, Guid targetId);
    Task<ResultDto<bool>> PublishNotification(string token, NotificationDto notification);

    Task<ResultDto<NotificationsResponseDto>> GetByFilter(ApiFilter.OrgNotificationFilter filter);
    Task<ResultDto<NotificationResponseDto>> GetById(Guid notificationId);
    Task<ResultDto<EmployeeNotificationsResponseDto>> GetEmployeeNotifications(ApiFilter.EmployeeNotificationFilter filter);
    Task<ResultDto<bool>> MarkNotificationAsRead(Guid notificationId);
    Task<ResultDto<bool>> MarkAllNotificationsAsRead();
    Task<ResultDto<bool>> MarkNotificationsAsUnread(Guid notificationId);

    Task<ResultDto<bool>> RegisterDeviceToken(DeviceRequestDto dto);
    Task<ResultDto<bool>> UnregisterDeviceToken(string firebaseToken);
}

[Service(ServiceLifetime.Scoped)]
public class NotificationService : BaseService<NotificationService>, INotificationService
{
    private readonly INotificationRepository notificationRepository;
    private readonly IEmployeeNotificationRepository employeeNotificationRepository;
    private readonly IDeviceTokenRepository deviceTokenRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IFirebaseService firebaseService;
    private readonly IFilterMapper<ApiFilter.EmployeeNotificationFilter, DomainFilter.EmployeeNotificationFilter> employeeNotificationFilterMapper;
    private readonly IFilterMapper<ApiFilter.OrgNotificationFilter, DomainFilter.OrgNotificationFilter> orgNotificationFilterMapper;
    public NotificationService(
        INotificationRepository notificationRepository,
        IEmployeeNotificationRepository employeeNotificationRepository,
        IDeviceTokenRepository deviceTokenRepository,
        IFirebaseService firebaseService, IEmployeeRepository employeeRepository,
        IFilterMapper<ApiFilter.EmployeeNotificationFilter, DomainFilter.EmployeeNotificationFilter> employeeNotificationFilterMapper,
        IFilterMapper<ApiFilter.OrgNotificationFilter, DomainFilter.OrgNotificationFilter> orgNotificationFilterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(logger, httpContextAccessor)
    {
        this.notificationRepository = notificationRepository;
        this.employeeNotificationRepository = employeeNotificationRepository;
        this.deviceTokenRepository = deviceTokenRepository;
        this.firebaseService = firebaseService;
        this.employeeRepository = employeeRepository;
        this.employeeNotificationFilterMapper = employeeNotificationFilterMapper;
        this.orgNotificationFilterMapper = orgNotificationFilterMapper;
    }

    public async Task<ResultDto<Guid>> CreateNotification(CreateNotificationRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgId))
        {
            logger.Error("Failed to get current org id");
            return new ErrorResultDto<Guid>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newNotification = requestDto.ToEntity(orgId);
        if (newNotification is null)
        {
            logger.Error("Failed to create notification");
            return new ErrorResultDto<Guid>(ResponseCodeConstant.BAD_REQUEST);
        }
        var createdId = await notificationRepository.Create(newNotification);
        if (createdId is null)
        {
            logger.Error("Failed to create notification");
            return new ErrorResultDto<Guid>(ResponseCodeConstant.NOTIFICATION_CREATE_FAILED);
        }
        return new SuccessResultDto<Guid>(createdId.Value);
    }

    public async Task<ResultDto<bool>> UpdateNotification(Guid notificationId, UpdateNotificationRequestDto requestDto)
    {
        var existNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
            IsTracking = true,
        });
        if (existNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }
        existNotification.UpdateFromDto(requestDto);

        var isNotificationUpdated = await notificationRepository.Update(existNotification);
        if (!isNotificationUpdated)
        {
            logger.Error("Failed to update notification: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_UPDATE_FAILED);
        }
        if (requestDto.Targets != null)
        {
            var needUpdate = new List<NotificationTarget>();
            foreach (var dto in requestDto.Targets)
            {
                var target = existNotification.NotificationTargets
                    .Where(t => t.NotificationTargetUid == dto.NotificationTargetId)
                    .FirstOrDefault();
                if (target == null)
                    continue;
                target.UpdateFromDto(dto);
                needUpdate.Add(target);
            }

            var newTargets = requestDto.Targets
                .Where(dto => !dto.NotificationTargetId.HasValue)
                .Select(dto => new NotificationTarget
                {
                    NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
                    NotificationUid = existNotification.NotificationUid,
                    TargetType = dto.TargetType!,
                    TargetIds = dto.TargetIds ?? [],
                    PublishStatus = NotificationStatusConstant.PENDING,
                    IsDeleted = false
                })
                .ToList();
            var isTargetUpdated = await notificationRepository.UpdateTargets(needUpdate, newTargets);
            if (!isTargetUpdated)
            {
                logger.Error("Failed to update notification targets: {NotificationId}", notificationId);
                return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_UPDATE_FAILED);
            }
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteNotification(Guid notificationId)
    {
        var existingNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }
        var isDeleted = await notificationRepository.Delete(existingNotification);
        if (!isDeleted)
        {
            logger.Error("Failed to delete notification: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> PublishNotification(Guid notificationId, Guid targetId)
    {
        var existingNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
        });
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }

        var target = existingNotification.NotificationTargets.FirstOrDefault(t => t.NotificationTargetUid == targetId);
        if (target == null)
        {
            logger.Error("Target not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.TARGET_NOT_FOUND);
        }

        switch (target.TargetType)
        {
            case TargetTypeConstant.INDIVIDUAL:
                return await SendNotificationToEmployee(target.TargetIds, target);
            case TargetTypeConstant.ROLE:
                return await SendNotificationByRole(target.TargetIds, target);
            case TargetTypeConstant.ALL:
                return await SendNotificationToAllEmployees(target);
            default:
                logger.Error("Unsupported target type: {NotificationId}", notificationId);
                return new ErrorResultDto<bool>(ResponseCodeConstant.UNSUPPORTED_TARGET_TYPE);
        }
    }

    public async Task<ResultDto<bool>> PublishNotification(string token, NotificationDto notification)
    {
        try
        {
            var response = await firebaseService.SendToDevice(token, notification);

            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_SEND_FAILED);
        }
    }

    // Send notification to specific employee
    private async Task<ResultDto<bool>> SendNotificationToEmployee(ICollection<Guid> employeeIds, NotificationTarget notificationTarget)
    {
        var newEmployeeNotifications = employeeIds.Select(id => new EmployeeNotification
        {
            EmployeeNotificationUid = GuidHelper.GenerateUUIDv7(),
            NotificationUid = notificationTarget.NotificationUid,
            EmployeeUid = id,
            IsRead = false,
            IsDeleted = false,
        }).ToList();
        await employeeNotificationRepository.Create(newEmployeeNotifications);

        var devices = await deviceTokenRepository.GetByEmployeeIds(employeeIds);

        if (!devices.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NO_DEVICES_REGISTERED);
        }
        try
        {
            var notificationPayload = new NotificationDto
            {
                Title = notificationTarget.Notification.Title,
                Body = notificationTarget.Notification.Body,
            };

            var deviceTokens = devices
                .Where(device => !string.IsNullOrEmpty(device.FirebaseToken))
                .Select(device => device.FirebaseToken!)
                .ToList();
            var response = await firebaseService.SendToDevices(deviceTokens, notificationPayload);

            notificationTarget.PublishAt = DateTime.Now;
            notificationTarget.PublishStatus = response.SuccessCount == deviceTokens.Count
                ? NotificationStatusConstant.PUBLISHED
                : response.SuccessCount > 0
                    ? NotificationStatusConstant.PARTIALLY_FAILED
                    : NotificationStatusConstant.FAILED;


            await notificationRepository.UpdateTarget(notificationTarget);
            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_SEND_FAILED);
        }
    }

    // Send notification to employees by role
    private async Task<ResultDto<bool>> SendNotificationByRole(ICollection<Guid> roleIds, NotificationTarget notificationTarget)
    {
        var employees = await employeeRepository.GetByRoleId(roleIds, new EmployeeQueryableOptions());
        if (!employees.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>(ResponseCodeConstant.TARGET_EMPTY);
        }

        var employeeIds = employees.Select(e => e.EmployeeUid).ToList();
        return await SendNotificationToEmployee(employeeIds, notificationTarget);
    }

    // Send notification to all employees
    private async Task<ResultDto<bool>> SendNotificationToAllEmployees(NotificationTarget notificationTarget)
    {
        // Get employee's device tokens
        var devices = await deviceTokenRepository.GetByOrgId(notificationTarget.Notification.OrgUid);

        if (!devices.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NO_DEVICES_REGISTERED);
        }

        try
        {
            var notificationPayload = new NotificationDto
            {
                Title = notificationTarget.Notification.Title,
                Body = notificationTarget.Notification.Body,
            };

            var deviceTokens = devices
                .Where(device => !string.IsNullOrEmpty(device.FirebaseToken))
                .Select(device => device.FirebaseToken!)
                .ToList();

            var response = await firebaseService.SendToDevices(deviceTokens, notificationPayload);

            notificationTarget.PublishAt = DateTime.Now;
            notificationTarget.PublishStatus = response.SuccessCount == deviceTokens.Count
                ? NotificationStatusConstant.PUBLISHED
                : response.SuccessCount > 0
                    ? NotificationStatusConstant.PARTIALLY_FAILED
                    : NotificationStatusConstant.FAILED;

            var newEmployeeNotifications = devices.Select(device => new EmployeeNotification
            {
                EmployeeNotificationUid = GuidHelper.GenerateUUIDv7(),
                NotificationUid = notificationTarget.NotificationUid,
                EmployeeUid = device.EmployeeUid,
                IsRead = false,
                IsDeleted = false,
            }).ToList();
            await employeeNotificationRepository.Create(newEmployeeNotifications);

            await notificationRepository.UpdateTarget(notificationTarget);
            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_SEND_FAILED);
        }
    }

    public async Task<ResultDto<EmployeeNotificationsResponseDto>> GetEmployeeNotifications(ApiFilter.EmployeeNotificationFilter filter)
    {
        if (!GetCurrentEmployeeGuid(out var employeeId))
        {
            logger.Error("Failed to get current employee id");
            return new ErrorResultDto<EmployeeNotificationsResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var domainFilter = employeeNotificationFilterMapper.MapToDomain(filter);
        var (employeeNotifications, total) = await employeeNotificationRepository
            .GetByFilter(employeeId, domainFilter, new EmployeeNotificationQueryableOptions
            {
                IncludedNotification = true,
            });
        if (!employeeNotifications.Any())
        {
            logger.Error("No employee notifications found");
            return new ErrorResultDto<EmployeeNotificationsResponseDto>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }
        var result = employeeNotifications.ToEmployeeNotificationsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<EmployeeNotificationsResponseDto>(result);
    }

    public async Task<ResultDto<NotificationsResponseDto>> GetByFilter(ApiFilter.OrgNotificationFilter filter)
    {
        var domainFilter = orgNotificationFilterMapper.MapToDomain(filter);
        var (notifications, total) = await notificationRepository.GetByFilter(domainFilter, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
        });
        if (!notifications.Any())
        {
            logger.Error("No notifications found");
            return new ErrorResultDto<NotificationsResponseDto>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }
        var result = notifications.ToNotificationsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<NotificationsResponseDto>(result);
    }

    public async Task<ResultDto<NotificationResponseDto>> GetById(Guid notificationId)
    {
        var notification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
        });
        if (notification is null)
        {
            logger.Error("No notifications found");
            return new ErrorResultDto<NotificationResponseDto>("No notifications found");
        }
        var result = notification.ToNotificationResponseDto();
        return new SuccessResultDto<NotificationResponseDto>(result);
    }

    public async Task<ResultDto<bool>> MarkNotificationAsRead(Guid notificationId)
    {
        var existingNotification = await employeeNotificationRepository.GetById(notificationId, new EmployeeNotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }

        existingNotification.IsRead = true;
        await employeeNotificationRepository.Update(existingNotification);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> MarkAllNotificationsAsRead()
    {
        if (!GetCurrentEmployeeGuid(out var employeeId))
        {
            logger.Error("Failed to get current employee id");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var existingNotifications = await employeeNotificationRepository.GetByEmployeeId(employeeId, new EmployeeNotificationQueryableOptions());
        if (!existingNotifications.Where(n => n.IsRead == false).Any())
        {
            logger.Error("No notifications found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }

        foreach (var notification in existingNotifications)
        {
            notification.IsRead = true;
        }
        await employeeNotificationRepository.UpdateRange(existingNotifications);

        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> MarkNotificationsAsUnread(Guid notificationId)
    {
        var existingNotification = await employeeNotificationRepository.GetById(notificationId, new EmployeeNotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.NOTIFICATION_NOT_EXIST);
        }

        existingNotification.IsRead = false;
        await employeeNotificationRepository.Update(existingNotification);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> RegisterDeviceToken(DeviceRequestDto dto)
    {
        var existingDevice = await deviceTokenRepository.GetByFirebaseToken(dto.FirebaseToken);
        if (existingDevice != null)
        {
            logger.Error("Device token already exists: {FirebaseToken}", dto.FirebaseToken);
            return new ErrorResultDto<bool>(ResponseCodeConstant.DEVICE_ALREADY_REGISTERED);
        }

        var employeeUid = GetCurrentEmployeeUid();
        var newDevice = new DeviceToken
        {
            DeviceTokenUid = GuidHelper.GenerateUUIDv7(),
            Platform = dto.Platform,
            DeviceId = dto.DeviceId,
            OsVersion = dto.OsVersion,
            LastActive = DateTime.Now,
            AppVersion = dto.AppVersion,
            FirebaseToken = dto.FirebaseToken,
            EmployeeUid = employeeUid,
        };
        var createdDevice = await deviceTokenRepository.Create(newDevice);
        if (createdDevice == null)
        {
            logger.Error("Failed to register device token: {FirebaseToken}", dto.FirebaseToken);
            return new ErrorResultDto<bool>(ResponseCodeConstant.REGISTER_DEVICE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> UnregisterDeviceToken(string firebaseToken)
    {
        var existingDevice = await deviceTokenRepository.GetByFirebaseToken(firebaseToken);
        if (existingDevice == null)
        {
            logger.Error("Device token not found: {FirebaseToken}", firebaseToken);
            return new ErrorResultDto<bool>(ResponseCodeConstant.REGISTERED_DEVICE_NOT_FOUND);
        }

        var isDeleted = await deviceTokenRepository.Delete(existingDevice);
        if (!isDeleted)
        {
            logger.Error("Failed to unregister device token: {FirebaseToken}", firebaseToken);
            return new ErrorResultDto<bool>(ResponseCodeConstant.UNREGISTER_DEVICE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
