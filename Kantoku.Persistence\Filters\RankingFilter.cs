namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for ranking queries - contains only business logic, no HTTP concerns
/// </summary>
public class RankingFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search for rankings (name or code)
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Minimum value of the ranking
    /// </summary>
    public float? MinValue { get; set; }

    /// <summary>
    /// Maximum value of the ranking
    /// </summary>
    public float? MaxValue { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Validate numeric range
        (MinValue, MaxValue) = ValidateRange(MinValue, MaxValue);
    }

    private static (float?, float?) ValidateRange(float? min, float? max)
    {
        if (min.HasValue && max.HasValue && min.Value > max.Value)
        {
            // Swap if min > max
            (min, max) = (max, min);
        }

        // Ensure non-negative values for ranking
        if (min.HasValue && min.Value < 0) min = 0;
        if (max.HasValue && max.Value < 0) max = 0;

        return (min, max);
    }
}

