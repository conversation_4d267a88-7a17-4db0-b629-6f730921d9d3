using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class EmployeeShiftQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedEmployee { get; set; } = false;
    public bool IncludedApprover { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedSchedule { get; set; } = false;
    public bool IncludedMonthlyReport { get; set; } = false;

    public EmployeeShiftQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }
}

public interface IEmployeeShiftQueryable
{
    IQueryable<EmployeeShift> GetShiftsQuery(
        EmployeeShiftQueryableOptions options
    );

    IQueryable<EmployeeShift> GetShiftsQueryIncluded(
        EmployeeShiftQueryableOptions options,
        IQueryable<EmployeeShift>? query = null
    );

    IQueryable<EmployeeShift> GetShiftsQueryFiltered(
        EmployeeShiftFilter filter,
        EmployeeShiftQueryableOptions options,
        IQueryable<EmployeeShift>? query = null
    );
}

public class EmployeeShiftQueryable(ApplicationDbContext context) :
    BaseQueryable<EmployeeShift>(context), IEmployeeShiftQueryable
{
    public IQueryable<EmployeeShift> GetShiftsQuery(
        EmployeeShiftQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(es => es.Employee.IsHidden == false && es.Employee.IsDeleted == false)
                    .Where(es => es.IsDeleted == false);
        return query;
    }

    public IQueryable<EmployeeShift> GetShiftsQueryIncluded(
        EmployeeShiftQueryableOptions options,
        IQueryable<EmployeeShift>? query = null
    )
    {
        query ??= GetShiftsQuery(options);
        if (options.IncludedEmployee || options.IncludedAll)
        {
            query = query.Include(es => es.Employee);
        }
        if (options.IncludedApprover || options.IncludedAll)
        {
            query = query.Include(es => es.Approver);
        }
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(es => es.Project);
        }
        if (options.IncludedSchedule || options.IncludedAll)
        {
            query = query.Include(es => es.ProjectSchedule);
        }
        if (options.IncludedMonthlyReport || options.IncludedAll)
        {
            query = query.Include(es => es.MonthlyReport);
        }
        return query;
    }

    public IQueryable<EmployeeShift> GetShiftsQueryFiltered(
        EmployeeShiftFilter filter,
        EmployeeShiftQueryableOptions options,
        IQueryable<EmployeeShift>? query = null
    )
    {
        query ??= GetShiftsQueryIncluded(options);
        if (filter.EmployeeId is not null && filter.EmployeeId != Guid.Empty)
        {
            query = query.Where(es => es.EmployeeUid == filter.EmployeeId);
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(es => es.ProjectUid == filter.ProjectId);
        }
        if (DateTime.TryParse(filter.TimeFrom, out DateTime timeFrom))
        {
            query = query.Where(es =>
                es.ScheduledStartTime >= timeFrom ||
                es.CheckInTime >= timeFrom
            );
        }
        if (DateTime.TryParse(filter.TimeTo, out DateTime timeTo))
        {
            query = query.Where(es =>
                es.ScheduledStartTime <= timeTo ||
                es.CheckInTime <= timeTo
            );
        }
        if (filter.HasCheckedIn.HasValue)
        {
            query = query.Where(es => es.CheckInTime != null == filter.HasCheckedIn.Value);
        }
        if (filter.HasCheckedOut.HasValue)
        {
            query = query.Where(es => es.CheckOutTime != null == filter.HasCheckedOut.Value);
        }
        if (filter.HasScheduledCheckIn.HasValue)
        {
            query = query.Where(es => es.ScheduledStartTime != null == filter.HasScheduledCheckIn.Value);
        }
        if (filter.HasScheduledCheckOut.HasValue)
        {
            query = query.Where(es => es.ScheduledEndTime != null == filter.HasScheduledCheckOut.Value);
        }
        if (filter.IsApproved.HasValue)
        {
            query = query.Where(es => es.IsApproved == filter.IsApproved.Value);
        }
        return query;
    }
}
