using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Services;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IBaseRepository<T>
{
    Guid GetCurrentAccountUid();
    Guid GetCurrentEmployeeUid();
    Guid GetCurrentOrgUid();
    TFilter ValidateFilter<TFilter>(TFilter filter) where TFilter : BaseFilter;
}

public abstract class BaseRepository<T> : IBaseRepository<T>
{
    protected readonly ApplicationDbContext context;
    protected readonly Serilog.ILogger logger;
    protected readonly ITenantContext tenantContext;

    public BaseRepository(ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext)
    {
        this.context = context;
        this.logger = logger.ForContext<T>();
        this.tenantContext = tenantContext;
    }

    public Guid GetCurrentAccountUid()
    {
        return tenantContext.GetCurrentAccountUid();
    }

    public Guid GetCurrentEmployeeUid()
    {
        return tenantContext.GetCurrentEmployeeUid();
    }

    public Guid GetCurrentOrgUid()
    {
        return tenantContext.GetCurrentOrgUid();
    }

    public TFilter ValidateFilter<TFilter>(TFilter filter) where TFilter : BaseFilter
    {
        filter.OrgId = GetCurrentOrgUid();
        filter.Validate();
        return filter;
    }
}