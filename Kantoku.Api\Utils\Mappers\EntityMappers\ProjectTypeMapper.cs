using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Common;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ProjectTypeMapper
{
    #region Entity to DTO mappings

    public static ProjectTypeResponseDto ToProjectTypeResponseDto(this ProjectType projectType, string languageCode)
    {
        if (projectType == null)
            return new ProjectTypeResponseDto();
            
        var responseDto = new ProjectTypeResponseDto
        {
            ProjectTypeId = projectType.ProjectTypeUid.ToString(),
            ProjectTypeName = projectType.TranslatedProjectType?
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                ?.ProjectTypeName
        };
        
        return responseDto;
    }

    // Add mapping for collection 
    public static IEnumerable<ProjectTypeResponseDto> ToProjectTypeResponseDtos(this IEnumerable<ProjectType> projectTypes, string languageCode)
    {
        return projectTypes?.Select(pt => pt.ToProjectTypeResponseDto(languageCode)) ?? Enumerable.Empty<ProjectTypeResponseDto>();
    }

    // Map to ProjectTypesResponseDto with pagination info
    public static ProjectTypesResponseDto ToProjectTypesResponseDto(this IEnumerable<ProjectType> projectTypes, string languageCode)
    {
        var items = projectTypes.ToProjectTypeResponseDtos(languageCode);
        
        return new ProjectTypesResponseDto
        {
            Items = items
        };
    }

    #endregion
} 