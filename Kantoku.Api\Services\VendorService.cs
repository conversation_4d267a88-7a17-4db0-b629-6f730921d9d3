﻿using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Vendor.Request;
using Kantoku.Api.Dtos.Vendor.Response;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;
public interface IVendorService
{
    Task<ResultDto<VendorDetailResponseDto>> GetById(Guid vendorId, string? keyword, int pageNum, int pageSize);
    Task<ResultDto<byte[]>> GetLogo(Guid vendorId, string orgId);
    Task<ResultDto<VendorsResponseDto>> GetByFilter(ApiFilter.VendorFilter filter);
    Task<ResultDto<VendorResponseDto>> Create(CreateVendorRequestDto requestDto);
    Task<ResultDto<VendorResponseDto>> Update(Guid vendorId, UpdateVendorRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid vendorId);
}

[Service(ServiceLifetime.Scoped)]
public class VendorService : BaseService<VendorService>, IVendorService
{
    private readonly IVendorRepository vendorRepository;
    private readonly IInputCostRepository inputCostRepository;
    private readonly IFileService fileService;
    private readonly IFilterMapper<ApiFilter.VendorFilter, DomainFilter.VendorFilter> vendorFilterMapper;
    public VendorService(IVendorRepository vendorRepository,
        IInputCostRepository inputCostRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IFilterMapper<ApiFilter.VendorFilter, DomainFilter.VendorFilter> vendorFilterMapper)
        : base(logger, httpContextAccessor)
    {
        this.vendorRepository = vendorRepository;
        this.inputCostRepository = inputCostRepository;
        this.fileService = fileService;
        this.vendorFilterMapper = vendorFilterMapper;
    }

    public async Task<ResultDto<VendorsResponseDto>> GetByFilter(ApiFilter.VendorFilter filter)
    {
        var domainFilter = vendorFilterMapper.MapToDomain(filter);
        var (total, vendors) = await vendorRepository.GetByFilter(domainFilter, new VendorQueryableOptions());
        if (vendors is null || !vendors.Any() || total == 0)
        {
            logger.Error("Vendors not found");
            return new ErrorResultDto<VendorsResponseDto>(ResponseCodeConstant.VENDOR_NOT_EXIST);
        }
        var res = vendors.ToVendorsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<VendorsResponseDto>(res);
    }

    public async Task<ResultDto<VendorDetailResponseDto>> GetById(Guid vendorId, string? keyword, int pageNum, int pageSize)
    {
        var vendor = await vendorRepository.GetById(vendorId, new VendorQueryableOptions());
        if (vendor is null)
        {
            logger.Error("Vendor not found");
            return new ErrorResultDto<VendorDetailResponseDto>(ResponseCodeConstant.VENDOR_NOT_EXIST);
        }
        var (total, inputCosts) = await inputCostRepository.GetByFilter(new DomainFilter.InputCostFilter
        {
            VendorId = vendorId,
            Keyword = keyword,
            PageNum = pageNum,
            PageSize = pageSize
        }, new InputCostQueryableOptions());
        var res = vendor.ToVendorDetailResponseDto(inputCosts, pageNum, pageSize, total);
        return new SuccessResultDto<VendorDetailResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetLogo(Guid vendorId, string orgId)
    {
        var vendor = await vendorRepository.GetById(vendorId, new VendorQueryableOptions());
        if (vendor == null)
        {
            logger.Error("Vendor not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.VENDOR_NOT_EXIST);
        }
        if (vendor.LogoUrl == null)
        {
            logger.Error("Logo not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.VENDOR_LOGO_NOT_EXIST);
        }
        var logo = await DownloadLogo(vendor.LogoUrl, orgId);
        if (logo == null || logo.Length == 0)
        {
            logger.Error("Logo not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.VENDOR_LOGO_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(logo);
    }

    public async Task<ResultDto<VendorResponseDto>> Create(CreateVendorRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<VendorResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newVendor = requestDto.ToEntity(orgUid);
        if (newVendor is null)
        {
            logger.Error("Vendor create failed");
            return new ErrorResultDto<VendorResponseDto>(ResponseCodeConstant.VENDOR_INPUT_DATA_INVALID);
        }
        var createdVendor = await vendorRepository.Create(newVendor, new VendorQueryableOptions());
        if (createdVendor is null)
        {
            logger.Error("Vendor create failed");
            return new ErrorResultDto<VendorResponseDto>(ResponseCodeConstant.VENDOR_CREATE_FAILED);
        }
        if (requestDto.Logo is not null)
        {
            var fileMetadata = await UploadLogo(createdVendor.VendorUid.ToString(), requestDto.Logo);
            if (fileMetadata is not null)
            {
                createdVendor.LogoUrl = fileMetadata.FileUrl;
            }
            await vendorRepository.Update(createdVendor);
        }
        var res = createdVendor.ToVendorResponseDto();
        return new SuccessResultDto<VendorResponseDto>(res);
    }

    public async Task<ResultDto<VendorResponseDto>> Update(Guid vendorId, UpdateVendorRequestDto requestDto)
    {
        var existVendor = await vendorRepository.GetById(vendorId, new VendorQueryableOptions());
        if (existVendor is null)
        {
            logger.Error("Vendor not found");
            return new ErrorResultDto<VendorResponseDto>(ResponseCodeConstant.VENDOR_NOT_EXIST);
        }

        existVendor.UpdateFromDto(requestDto);

        if (requestDto.Logo is not null)
        {
            var fileMetadata = await UploadLogo(vendorId.ToString(), requestDto.Logo);
            if (fileMetadata is not null)
            {
                existVendor.LogoUrl = fileMetadata.FileUrl;
            }
        }

        var updatedVendor = await vendorRepository.Update(existVendor, new VendorQueryableOptions());
        if (updatedVendor is null)
        {
            logger.Error("Vendor update failed");
            return new ErrorResultDto<VendorResponseDto>(ResponseCodeConstant.VENDOR_UPDATE_FAILED);
        }
        var result = updatedVendor.ToVendorResponseDto();
        return new SuccessResultDto<VendorResponseDto>(result);
    }

    public async Task<ResultDto<bool>> Delete(Guid vendorId)
    {
        var vendor = await vendorRepository.GetById(vendorId, new VendorQueryableOptions());
        if (vendor is null)
        {
            logger.Error("Vendor not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.VENDOR_NOT_EXIST, false);
        }
        vendor.IsDeleted = true;
        var isDeleted = await vendorRepository.Update(vendor);
        if (!isDeleted)
        {
            logger.Error("Vendor delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.VENDOR_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<FileMetadataResponseDto?> UploadLogo(string vendorUid, IFormFile logo)
    {
        try
        {
            var path = StorageConstant.VendorLogo(vendorUid);
            var fileName = "logo.jpg";
            var objectName = path + fileName;
            var fileMetadata = await fileService.UploadFileAsync(logo, objectName);
            if (fileMetadata is null || fileMetadata.Data is null)
            {
                logger.Error("Upload logo failed");
                return null;
            }
            return fileMetadata.Data;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error uploading logo");
            return null;
        }
    }

    private async Task<byte[]> DownloadLogo(string logoUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(logoUrl, orgId);
            if (file is null || file.Data is null)
            {
                logger.Error("Download logo failed");
                return [];
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting logo as base64");
            return [];
        }
    }
}