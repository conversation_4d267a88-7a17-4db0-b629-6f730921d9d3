using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class InputCostQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedVendor { get; set; } = false;
    public bool IncludedPaymentType { get; set; } = false;
    public bool IncludedConstruction { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedEntryType { get; set; } = false;
    public bool IncludedInputCostItems { get; set; } = false;
}

public interface IInputCostQueryable
{
    IQueryable<InputCost> GetInputCostsQuery(
        InputCostQueryableOptions options
    );
    IQueryable<InputCost> GetInputCostsQueryIncluded(
        InputCostQueryableOptions options,
        IQueryable<InputCost>? query = null
    );
    IQueryable<InputCost> GetInputCostsQueryFiltered(
        InputCostFilter filter,
        InputCostQueryableOptions options,
        IQueryable<InputCost>? query = null
    );
}

public class InputCostQueryable(ApplicationDbContext context) :
    BaseQueryable<InputCost>(context), IInputCostQueryable
{
    public IQueryable<InputCost> GetInputCostsQuery(
        InputCostQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<InputCost> GetInputCostsQueryIncluded(
        InputCostQueryableOptions options,
        IQueryable<InputCost>? query = null
    )
    {
        query ??= GetInputCostsQuery(options);
        if (options.IncludedVendor || options.IncludedAll)
        {
            query = query.Include(p => p.Vendor);
        }
        if (options.IncludedPaymentType || options.IncludedAll)
        {
            query = query.Include(p => p.PaymentType);
        }
        if (options.IncludedConstruction || options.IncludedAll)
        {
            query = query.Include(p => p.Construction);
        }
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(p => p.Construction.Project);
        }
        if (options.IncludedEntryType || options.IncludedAll)
        {
            query = query.Include(p => p.EntryType);
        }
        if (options.IncludedInputCostItems || options.IncludedAll)
        {
            query = query.Include(p => p.InputCostItems)
                .ThenInclude(p => p.Item);
            query = query.Include(p => p.InputCostItems)
                .ThenInclude(p => p.Vendor);
            query = query.Include(p => p.InputCostItems)
                .ThenInclude(p => p.Item)
                .ThenInclude(p => p.Category);
        }

        return query;
    }

    public IQueryable<InputCost> GetInputCostsQueryFiltered(
        InputCostFilter filter,
        InputCostQueryableOptions options,
        IQueryable<InputCost>? query = null
    )
    {
        query ??= GetInputCostsQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => (p.OriginalNumber != null && p.OriginalNumber.Contains(filter.Keyword))
                || (p.Title != null && p.Title.Contains(filter.Keyword)));
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(p => p.Construction.ProjectUid == filter.ProjectId);
        }
        if (filter.ConstructionId is not null && filter.ConstructionId != Guid.Empty)
        {
            query = query.Where(p => p.ConstructionUid == filter.ConstructionId);
        }
        if (filter.VendorId is not null && filter.VendorId != Guid.Empty)
        {
            query = query.Where(p => p.VendorUid == filter.VendorId);
        }
        if (filter.PaymentTypeId is not null && filter.PaymentTypeId != Guid.Empty)
        {
            query = query.Where(p => p.PaymentTypeUid == filter.PaymentTypeId);
        }
        if (filter.EntryTypeId is not null && filter.EntryTypeId != Guid.Empty)
        {
            query = query.Where(p => p.EntryTypeUid == filter.EntryTypeId);
        }
        if (DateOnly.TryParse(filter.IssueDateFrom, out var issueDateFrom) == true)
        {
            query = query.Where(p => issueDateFrom <= p.IssueDate);
        }
        if (DateOnly.TryParse(filter.IssueDateTo, out var issueDateTo) == true)
        {
            query = query.Where(p => issueDateTo >= p.IssueDate);
        }
        if (DateOnly.TryParse(filter.PaymentDateFrom, out var paymentDateFrom) == true)
        {
            query = query.Where(p => paymentDateFrom <= p.PaymentDate);
        }
        if (DateOnly.TryParse(filter.PaymentDateTo, out var paymentDateTo) == true)
        {
            query = query.Where(p => paymentDateTo >= p.PaymentDate);
        }
        if (decimal.TryParse(filter.TotalAmountMin, out var totalAmountMin) == true)
        {
            query = query.Where(p => totalAmountMin <= p.TotalAmount);
        }
        if (decimal.TryParse(filter.TotalAmountMax, out var totalAmountMax) == true)
        {
            query = query.Where(p => totalAmountMax >= p.TotalAmount);
        }
        return query;
    }
}

