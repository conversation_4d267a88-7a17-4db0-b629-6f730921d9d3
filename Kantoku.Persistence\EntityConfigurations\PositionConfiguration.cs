using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class PositionConfiguration(string schema) : IEntityTypeConfiguration<Position>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Position> builder)
    {
        builder.HasKey(e => e.PositionUid).HasName("position_pkey");

        builder.ToTable("position", Schema);

        builder.Property(e => e.PositionUid)
            .HasColumnName("position_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.PositionCode)
            .HasColumnName("position_code");
        builder.Property(e => e.PositionName)
            .HasColumnName("position_name");

        builder.HasOne(d => d.Org).WithMany(p => p.Positions)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("position_org_id_fkey");
    }
}
