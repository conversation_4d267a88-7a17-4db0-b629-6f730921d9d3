﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IVendorRepository
{
    Task<(int, IEnumerable<Vendor>)> GetByFilter(DomainFilter.VendorFilter filter, VendorQueryableOptions options);
    Task<Vendor?> GetById(Guid vendorId, VendorQueryableOptions options);
    Task<bool> ExistById(Guid? vendorId);
    Task<Vendor?> Create(Vendor vendor, VendorQueryableOptions options);
    Task<Vendor?> Update(Vendor vendor, VendorQueryableOptions options);

    Task<Guid?> Create(Vendor vendor);
    Task<bool> Update(Vendor vendor);
}

public class VendorRepository : BaseRepository<VendorRepository>, IVendorRepository
{
    private readonly IVendorQueryable vendorQueryable;
    public VendorRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IVendorQueryable vendorQueryable
    )
        : base(context, logger, tenantContext)
    {
        this.vendorQueryable = vendorQueryable;
    }

    public async Task<(int, IEnumerable<Vendor>)> GetByFilter(DomainFilter.VendorFilter filter, VendorQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = vendorQueryable.GetVendorQueryByFilter(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .OrderBy(v => v.VendorCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting vendors");
            return (0, []);
        }
    }

    public async Task<Vendor?> GetById(Guid vendorId, VendorQueryableOptions options)
    {
        try
        {
            var query = vendorQueryable.GetVendorQueryIncluded(options);
            query = query.Where(v => v.VendorUid == vendorId);
            var vendor = await query
                .FirstOrDefaultAsync();
            return vendor;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting vendor");
            return null;
        }
    }

    public async Task<bool> ExistById(Guid? vendorId)
    {
        try
        {
            return await context.Vendors.AnyAsync(v => v.VendorUid == vendorId);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking vendor existence");
            return false;
        }
    }

    public async Task<Vendor?> Create(Vendor vendor, VendorQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Vendors.AddAsync(vendor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(vendor.VendorUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating vendor");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(Vendor vendor)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Vendors.AddAsync(vendor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return vendor.VendorUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating vendor");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Vendor?> Update(Vendor vendor, VendorQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Vendors.Update(vendor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(vendor.VendorUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating vendor");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Vendor vendor)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Vendors.Update(vendor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating vendor");
            await transaction.RollbackAsync();
            return false;
        }
    }
}
