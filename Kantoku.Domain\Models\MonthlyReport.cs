using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class MonthlyReport : AuditableEntity
{
    public Guid MonthlyReportUid { get; set; }
    public Guid EmployeeUid { get; set; }

    public DateOnly ReportFrom { get; set; }
    public DateOnly ReportTo { get; set; }
    public float TotalWorkDays { get; set; }
    public float TotalOffDays { get; set; }
    public float TotalWorkHours { get; set; }
    public float TotalOvertime { get; set; }
    public float SelfLeaveUsed { get; set; }
    public float OrgLeaveUsed { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsRequested { get; set; }
    public bool? IsApproved { get; set; }
    public Guid? ApproverUid { get; set; }

    public DateTime? ApprovedTime { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Employee? Approver{ get; set; }

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];
}