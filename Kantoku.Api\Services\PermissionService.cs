using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.FunctionAccessibility.Request;
using Kantoku.Api.Dtos.FunctionAccessibility.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IPermissionService
{
    Task<ResultDto<MenuPrivilegeResponseDto>> GetMenuPrivileges();
    Task<ResultDto<SimpleFunctionPrivilegesResponseDto>> GetMenuPrivilegeByCurrentEmployee();
    Task<ResultDto<RolePrivilegeResponseDto>> GetMenuPrivilegeByRoleId(Guid roleId);
    Task<ResultDto<StructurePrivilegeResponseDto>> GetMenuPrivilegeByStructureId(Guid structureId);
    Task<ResultDto<FunctionPrivilegeResponseDto>> UpdateMenuPrivilege(UpdateMenuPrivilegeRequestDto dto);
}

[Service(ServiceLifetime.Scoped)]
public class PermissionService : BaseService<PermissionService>, IPermissionService
{
    private readonly IRoleFunctionRepository roleFunctionRepository;
    private readonly IStructureRepository structureRepository;
    private readonly IRoleRepository roleRepository;
    private readonly IFunctionRepository functionRepository;

    public PermissionService(
        IRoleFunctionRepository roleFunctionRepository,
        IStructureRepository structureRepository,
        IRoleRepository roleRepository,
        IFunctionRepository functionRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.roleFunctionRepository = roleFunctionRepository;
        this.structureRepository = structureRepository;
        this.roleRepository = roleRepository;
        this.functionRepository = functionRepository;
    }

    public async Task<ResultDto<MenuPrivilegeResponseDto>> GetMenuPrivileges()
    {
        var leafFunctions = await functionRepository.GetAll();
        if (leafFunctions is null || !leafFunctions.Any())
        {
            logger.Error("No leaf functions found");
            return new ErrorResultDto<MenuPrivilegeResponseDto>(ResponseCodeConstant.FUNCTION_NOT_EXIST);
        }

        var structures = await structureRepository.GetStructureWithRoles();
        if (structures is null || !structures.Any())
        {
            logger.Error("No structures found");

        }

        var independentRoles = await roleRepository.GetIndependentRoles();
        if (independentRoles is null || !independentRoles.Any())
        {
            logger.Error("No independent roles found");

        }

        var roleFunctions = await roleFunctionRepository.GetAllRoleFunctions(
            new RoleFunctionQueryableOptions
            {
                IncludedRole = true,
                IncludedFunction = true
            }
        );

        var independentRolePermission = new[]
            {
                new StructurePrivilegeResponseDto
                {
                    StructureId = string.Empty,
                    StructureName = string.Empty,
                    RolesPrivileges = independentRoles?.Select(r => r.ToRolePrivilegeResponseDto(leafFunctions, roleFunctions))
                }
            } ?? [];

        var structurePermission = structures?
            .OrderBy(s => s.StructureCode)
            .Select(s => new StructurePrivilegeResponseDto
            {
                StructureId = s.StructureUid.ToString(),
                StructureName = s.StructureName,
                RolesPrivileges = s.Roles.Select(r => r.ToRolePrivilegeResponseDto(leafFunctions, roleFunctions))
            }) ?? [];

        var items = independentRolePermission.Concat(structurePermission) ?? [];

        var result = new MenuPrivilegeResponseDto
        {
            StructuresPrivileges = items.OrderBy(s => string.IsNullOrEmpty(s.StructureName) ? 1 : 0)
                                      .ThenBy(s => s.StructureName)
                                      .ToList()
        };
        return new SuccessResultDto<MenuPrivilegeResponseDto>(result);
    }

    public async Task<ResultDto<SimpleFunctionPrivilegesResponseDto>> GetMenuPrivilegeByCurrentEmployee()
    {
        var functions = await functionRepository.GetAll();
        if (functions is null || !functions.Any())
        {
            logger.Error("No functions found");
            return new ErrorResultDto<SimpleFunctionPrivilegesResponseDto>(ResponseCodeConstant.FUNCTION_NOT_EXIST);
        }
        var roleFunctions = await roleFunctionRepository.GetByEmployeeId(GetCurrentEmployeeUid(), new RoleFunctionQueryableOptions
        {
            IncludedFunction = true,
            IncludedRole = true,
        });
        if (roleFunctions is null || !roleFunctions.Any())
        {
            logger.Error("No role configuration for employee {EmployeeUid} found", GetCurrentEmployeeUid());
            return new ErrorResultDto<SimpleFunctionPrivilegesResponseDto>(ResponseCodeConstant.FUNCTIONAL_ROLE_NOT_EXIST);
        }
        var res = functions.ToSimpleRolePrivilegeResponseDto(roleFunctions);
        return new SuccessResultDto<SimpleFunctionPrivilegesResponseDto>(res);
    }

    public async Task<ResultDto<RolePrivilegeResponseDto>> GetMenuPrivilegeByRoleId(Guid roleUid)
    {
        var role = await roleRepository.GetById(roleUid, new RoleQueryableOptions
        {
            IncludedFunctions = true,
            IncludedEmployees = true,
            IncludedStructure = true,
        });
        if (role is null)
        {
            logger.Error("Role {RoleId} not found", roleUid);
            return new ErrorResultDto<RolePrivilegeResponseDto>(ResponseCodeConstant.ROLE_NOT_EXIST);
        }
        var roleFunctions = await roleFunctionRepository.GetByRoleId(roleUid, new RoleFunctionQueryableOptions
        {
            IncludedFunction = true,
            IncludedRole = true,
        });
        var result = role.ToRolePrivilegeResponseDto(roleFunctions);
        return new SuccessResultDto<RolePrivilegeResponseDto>(result);
    }

    public async Task<ResultDto<StructurePrivilegeResponseDto>> GetMenuPrivilegeByStructureId(Guid structureUid)
    {
        var structure = await structureRepository.GetById(structureUid, new StructureQueryableOptions
        {
            IncludedRoles = true,
            IncludedEmployees = true,
            IncludedParent = true,
            IncludedChildren = true,
        });
        if (structure is null)
        {
            logger.Error("Structure {StructureId} not found", structureUid);
            return new ErrorResultDto<StructurePrivilegeResponseDto>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }
        var result = structure.ToStructurePrivilegeResponseDto();
        return new SuccessResultDto<StructurePrivilegeResponseDto>(result);
    }

    public async Task<ResultDto<FunctionPrivilegeResponseDto>> UpdateMenuPrivilege(UpdateMenuPrivilegeRequestDto dto)
    {
        var roleFunction = await roleFunctionRepository.GetByFunctionIdAndRoleId(dto.FunctionId, dto.RoleId, new RoleFunctionQueryableOptions
        {
            IncludedFunction = true,
            IncludedRole = true,
        });
        if (roleFunction is null)
        {
            logger.Error("No allowed role found for function {FunctionId}, role {RoleId}", dto.FunctionId, dto.RoleId);
            return new ErrorResultDto<FunctionPrivilegeResponseDto>(ResponseCodeConstant.FUNCTIONAL_ROLE_NOT_EXIST);
        }

        roleFunction.UpdateFromDto(dto);
        var updatedUrf = await roleFunctionRepository.Update(roleFunction);
        if (updatedUrf is null)
        {
            logger.Error("Failed to update access privilege for function {FunctionId}, role {RoleId}", dto.FunctionId, dto.RoleId);
            return new ErrorResultDto<FunctionPrivilegeResponseDto>(ResponseCodeConstant.FUNCTIONAL_ROLE_NOT_EXIST);
        }
        
        var result = updatedUrf.ToFunctionPrivilegeResponseDto(roleFunction.Function);
        return new SuccessResultDto<FunctionPrivilegeResponseDto>(result);
    }
}

