using Kantoku.Api.Services;
using Kantoku.Persistence.Services;

namespace Kantoku.Api.Extensions.Services;

public static class TenantContextExtensions
{
    /// <summary>
    /// Registers HTTP-based tenant context for API scenarios
    /// This should be called after AddPersistence() to override the default StaticTenantContext
    /// </summary>
    public static IServiceCollection AddHttpTenantContext(this IServiceCollection services)
    {
        // Override the default ITenantContext registration with HTTP-based implementation
        services.AddScoped<ITenantContext, HttpTenantContext>();
        return services;
    }
}
