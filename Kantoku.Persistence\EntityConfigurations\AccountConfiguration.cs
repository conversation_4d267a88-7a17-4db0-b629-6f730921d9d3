using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class AccountConfiguration(string schema) : IEntityTypeConfiguration<Account>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Account> builder)
    {
        builder.HasKey(e => e.AccountUid).HasName("account_pkey");

        builder.ToTable("account", Schema);

        builder.HasIndex(e => new { e.Email, e.IsDeleted }, "idx_account_email")
            .IsUnique()
            .HasFilter("is_deleted = false");
        builder.HasIndex(e => new { e.LoginId, e.IsDeleted }, "idx_account_login_id")
            .IsUnique()
            .HasFilter("is_deleted = false");

        builder.Property(e => e.AccountUid)
            .HasColumnName("account_uid");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.LoginId)
            .HasColumnName("login_id");
        builder.Property(e => e.Password)
            .HasColumnName("password");
        builder.Property(e => e.HashedPassword)
            .HasColumnName("hashed_password");
        builder.Property(e => e.AccountType)
            .HasColumnName("account_type");
        builder.Property(e => e.IsLocked)
            .HasDefaultValue(false)
            .HasColumnName("is_locked");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by");
        builder.Property(e => e.LastModifiedBy)
            .HasColumnName("last_modified_by");
        builder.Property(e => e.CreatedTime)
            .HasColumnName("created_time")
            .HasColumnType("timestamp with time zone");
        builder.Property(e => e.LastModifiedTime)
            .HasColumnName("last_modified_time")
            .HasColumnType("timestamp with time zone");
    }
} 