using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IGlobalConfigRepository
{
    Task<GlobalConfig?> Get<PERSON>y<PERSON><PERSON>(string key);
}

public class GlobalConfigRepository : BaseRepository<GlobalConfig>, IGlobalConfigRepository
{
    public GlobalConfigRepository(
        ApplicationDbContext context,
        ITenantContext tenantContext,
        Serilog.ILogger logger
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<GlobalConfig?> GetByKey(string key)
    {
        try
        {
            var orgUid = GetCurrentOrgUid();
            var globalConfig = await context.GlobalConfigs
                .Where(gc => gc.OrgUid.ToString().Equals(orgUid) && gc.Key.Equals(key))
                .FirstOrDefaultAsync();
            return globalConfig;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting global config with key: {Key}", key);
            return null;
        }
    }
}