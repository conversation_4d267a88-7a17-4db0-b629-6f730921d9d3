using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class ConstructionPaymentRequest : AuditableEntity
{
    public Guid ConstructionPaymentRequestUid { get; set; }
    public Guid OrgUid { get; set; }
    public Guid ConstructionCostUid { get; set; }
    public Guid ContractorUid { get; set; }

    [AuditProperty]
    public DateOnly IssueDateFrom { get; set; }

    [AuditProperty]
    public DateOnly IssueDateTo { get; set; }

    [AuditProperty]
    public long RequestAmount { get; set; }

    [AuditProperty]
    public int RetentionAmount { get; set; }

    [AuditProperty]
    public int ReleasedAmount { get; set; }

    public bool IsDeleted { get; set; }

    public virtual ConstructionCost ConstructionCost { get; set; } = null!;

    public virtual Contractor Contractor { get; set; } = null!;

    public virtual Org Org { get; set; } = null!;
}