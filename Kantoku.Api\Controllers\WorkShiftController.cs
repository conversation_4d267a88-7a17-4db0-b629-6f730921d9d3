using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Services;
using Kantoku.Api.Dtos.WorkShift.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Domain.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.WorkShift.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[ApiController]
[Route("/api/v1/[controller]")]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class WorkShiftController : BaseController
{
    private readonly IWorkShiftService workShiftService;
    private readonly IAuditLogService auditLogService;
    public WorkShiftController(
        ITResponseFactory responseFactory,
        IWorkShiftService workShiftService,
        Serilog.ILogger logger,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.workShiftService = workShiftService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get all work shifts with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for work shifts</param>
    /// <returns>List of work shifts matching the filter criteria</returns>
    [HttpGet]
    public async Task<GeneralResponse<WorkShiftsResponseDto>> GetAllWorkShifts([FromQuery] WorkShiftFilter filter)
    {
        try
        {
            var result = await workShiftService.GetAllWorkShifts(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<WorkShiftsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get a specific work shift by its ID
    /// </summary>
    /// <param name="id">Work shift ID </param>
    /// <returns>Work shift details if found</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<WorkShiftResponseDto>> GetWorkShiftById([FromRoute] Guid id)
    {
        try
        {
            var result = await workShiftService.GetWorkShiftById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<WorkShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new work shift
    /// </summary>
    /// <param name="createDto">Work shift creation data</param>
    /// <returns>Created work shift details</returns>
    [HttpPost]
    public async Task<GeneralResponse<WorkShiftResponseDto>> CreateWorkShift([FromBody] CreateWorkShiftRequestDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<WorkShiftResponseDto>();

            var result = await workShiftService.CreateWorkShift(createDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<WorkShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing work shift
    /// </summary>
    /// <param name="id">Work shift ID to update</param>
    /// <param name="updateDto">Updated work shift data</param>
    /// <returns>Updated work shift details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<WorkShiftResponseDto>> UpdateWorkShift([FromRoute] Guid id, [FromBody] UpdateWorkShiftRequestDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<WorkShiftResponseDto>();

            var result = await workShiftService.UpdateWorkShift(id, updateDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<WorkShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a work shift
    /// </summary>
    /// <param name="id">Work shift ID to delete</param>
    /// <returns>True if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteWorkShift([FromRoute] Guid id)
    {
        try
        {
            var result = await workShiftService.DeleteWorkShift(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific work shift
    /// </summary>
    /// <param name="id">Work shift ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Audit log entries for the specified work shift</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<WorkShift>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
