using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IRequestTypeRepository
{
    Task<RequestType?> GetByCode(string code);
    Task<IEnumerable<RequestType>> GetAll();
}

public class RequestTypeRepository : BaseRepository<RequestType>, IRequestTypeRepository
{
    public RequestTypeRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
    }

    public async Task<IEnumerable<RequestType>> GetAll()
    {
        try
        {
            return await context.RequestTypes
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request types");
            return [];
        }
    }

    public async Task<RequestType?> GetByCode(string code)
    {
        try
        {
            return await context.RequestTypes
                .Where(rt => rt.RequestTypeCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request type by code {Code}", code);
            return null;
        }
    }
}
