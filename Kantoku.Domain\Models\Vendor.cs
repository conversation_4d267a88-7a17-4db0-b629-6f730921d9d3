using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class Vendor : AuditableEntity
{
    public Guid VendorUid { get; set; }

    [AuditProperty]
    public string VendorCode { get; set; } = null!;

    [AuditProperty]
    public string VendorName { get; set; } = null!;

    [AuditProperty]
    public string? VendorSubName { get; set; }

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public string? CorporateNumber { get; set; }

    [AuditProperty]
    public string? PhoneNumber { get; set; }

    [AuditProperty]
    public string? Email { get; set; }

    [AuditProperty]
    public ContactPerson? ContactPerson { get; set; }
    
    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }
    public Guid OrgUid { get; set; }
    public string? LogoUrl { get; set; }

    public virtual Org? Org { get; set; } = null!;

    public virtual ICollection<ItemPrice> ItemPrices { get; set; } = [];

    public virtual ICollection<InputCost> InputCosts { get; set; } = [];

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];
}