using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class OutSourceShift : AuditableEntity
{
    public Guid OutSourceShiftUid { get; set; }
    public Guid ProjectScheduleUid { get; set; }
    public Guid OutSourceUid { get; set; }

    [AuditProperty]
    public DateTime ScheduledStartTime { get; set; }

    [AuditProperty]
    public DateTime ScheduledEndTime { get; set; }

    [AuditProperty]
    public float AssignedWorkload { get; set; }

    [AuditProperty]
    public string? Role { get; set; }

    public bool IsDeleted { get; set; }

    public virtual OutSource OutSource { get; set; } = null!;

    public virtual ProjectSchedule ProjectSchedule { get; set; } = null!;
}
