using StackExchange.Redis;
using Kantoku.Api.Configurations;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Services;

public interface IRedisCacheService
{
    IDatabase GetDatabase();
    Task<T?> GetAsync<T>(string key);
    Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null);
    Task<bool> DeleteAsync(string key);
    Task<bool> DeleteByPatternAsync(string pattern);
}

[Service(ServiceLifetime.Singleton)]
public class RedisCacheService : BaseService<RedisCacheService>, IRedisCacheService
{
    private readonly IDatabase cache;
    private readonly ConnectionMultiplexer redisConnection;
    private readonly JsonSerializerSettings jsonSerializerSettings;

    public RedisCacheService(
        IOptions<RedisConfig> redisConfig,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    ) : base(logger, httpContextAccessor)
    {
        redisConnection = ConnectionMultiplexer.Connect(redisConfig.Value.ConnectionString);
        cache = redisConnection.GetDatabase();
        jsonSerializerSettings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.Objects,
            Formatting = Formatting.Indented
        };
    }

    public IDatabase GetDatabase()
    {
        return cache;
    }

    public async Task<T?> GetAsync<T>(string key)
    {
        try
        {
            var value = await cache.StringGetAsync(key);
            if (value.IsNullOrEmpty)
            {
                return default;
            }
            var result = JsonConvert.DeserializeObject<T>(value.ToString(), jsonSerializerSettings);
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting cached value");
            return default;
        }
    }

    public async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        try
        {
            var json = JsonConvert.SerializeObject(value, jsonSerializerSettings);
            await cache.StringSetAsync(key, json, expiry);
            return true;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error setting cached value");
            return false;
        }
    }

    public async Task<bool> DeleteAsync(string key)
    {
        try
        {
            await cache.KeyDeleteAsync(key);
            return true;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting cached value");
            return false;
        }
    }

    public async Task<bool> DeleteByPatternAsync(string pattern)
    {
        try
        {
            var server = redisConnection.GetServer(redisConnection.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern + "*").ToArray();
            await cache.KeyDeleteAsync(keys);
            return true;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting cached value by pattern");
            return false;
        }
    }
}
