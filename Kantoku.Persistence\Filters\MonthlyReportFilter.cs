using System.ComponentModel.DataAnnotations;

namespace Kantoku.Persistence.Filters;

public class MonthlyReportFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search for name or code of employee
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Working status of employee  
    /// </summary>
    public string? WorkingStatus { get; set; }

    /// <summary>
    /// Report from date
    /// </summary>
    [Required]
    public required string ReportFrom { get; set; }

    /// <summary>
    /// Report to date
    /// </summary>
    [Required]
    public required string ReportTo { get; set; }
}