using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class RequestConfiguration(string schema) : IEntityTypeConfiguration<Request>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Request> builder)
    {
        builder.ToTable("request", Schema);

        builder.<PERSON>Key(e => e.RequestUid).HasName("request_pkey");

        builder.Property(e => e.RequestUid)
            .HasColumnName("request_uid");
        builder.Property(e => e.Approver1Uid)
            .HasColumnName("approver1_uid");
        builder.Property(e => e.Approver1Notes)
            .HasColumnName("approver1_notes");
        builder.Property(e => e.Approver1Status)
            .HasColumnName("approver1_status");
        builder.Property(e => e.Approver1Time)
            .HasColumnType("timestamp")
            .HasColumnName("approver1_time");
        builder.Property(e => e.Approver2Uid)
            .HasColumnName("approver2_uid");
        builder.Property(e => e.Approver2Notes)
            .HasColumnName("approver2_notes");
        builder.Property(e => e.Approver2Status)
            .HasColumnName("approver2_status");
        builder.Property(e => e.Approver2Time)
            .HasColumnType("timestamp")
            .HasColumnName("approver2_time");
        builder.Property(e => e.AuthorUid)
            .HasColumnName("author_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.RequestFrom)
            .HasColumnType("timestamp")
            .HasColumnName("request_from");
        builder.Property(e => e.RequestTo)
            .HasColumnType("timestamp")
            .HasColumnName("request_to");
        builder.Property(e => e.RequestTypeCode)
            .HasColumnName("request_type_code");
        builder.Property(e => e.IsUserRequestedLeave)
            .HasColumnName("is_user_requested_leave");
        builder.Property(e => e.StatusCode)
            .HasColumnName("status_code");

        builder.HasOne(d => d.Approver1)
            .WithMany(p => p.RequestApprover1s)
            .HasForeignKey(d => d.Approver1Uid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("request_approver1_id_fkey")
            .IsRequired(false);

        builder.HasOne(d => d.Approver2)
            .WithMany(p => p.RequestApprover2s)
            .HasForeignKey(d => d.Approver2Uid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("request_approver2_id_fkey")
            .IsRequired(false);

        builder.HasOne(d => d.Author)
            .WithMany(p => p.RequestAuthors)
            .HasForeignKey(d => d.AuthorUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("request_create_by_fkey")
            .IsRequired();

        builder.HasOne(d => d.Project)
            .WithMany(p => p.Requests)
            .HasForeignKey(d => d.ProjectUid)
            .HasConstraintName("request_project_id_fkey");

        builder.HasOne(d => d.RequestType)
            .WithMany(p => p.Requests)
            .HasForeignKey(d => d.RequestTypeCode)
            .HasConstraintName("request_request_type_id_fkey");

        builder.HasOne(d => d.Status)
            .WithMany(p => p.Requests)
            .HasForeignKey(d => d.StatusCode)
            .HasConstraintName("request_status_id_fkey");
    }
} 