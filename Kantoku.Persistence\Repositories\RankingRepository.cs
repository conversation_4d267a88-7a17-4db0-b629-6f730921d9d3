using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IRankingRepository
{
    Task<(IEnumerable<Ranking>, int)> GetByFilter(DomainFilter.RankingFilter filter, RankingQueryableOptions options);
    Task<Ranking?> GetById(Guid rankingId, RankingQueryableOptions options);
    Task<bool> IsExistHighestRanking();
    Task<bool> IsExistLowestRanking();
    Task<Ranking?> Create(Ranking ranking);
    Task<Ranking?> Update(Ranking ranking);

    Task<(IEnumerable<EmployeeCost>, int)> GetEmployeeCostsByFilter(DomainFilter.EmployeeCostFilter filter, EmployeeCostQueryableOptions options);
    Task<(IEnumerable<EmployeeCost>, int)> GetEmployeeCostsByEmployeeId(
        Guid employeeId, DomainFilter.EmployeeCostFilter filter, EmployeeCostQueryableOptions options);
}

public class RankingRepository : BaseRepository<Ranking>, IRankingRepository
{
    private readonly IRankingQueryable rankingQueryable;
    private readonly IEmployeeCostQueryable employeeCostQueryable;
    public RankingRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IRankingQueryable rankingQueryable,
        IEmployeeCostQueryable employeeCostQueryable
    )
        : base(context, logger, tenantContext)
    {
        this.rankingQueryable = rankingQueryable;
        this.employeeCostQueryable = employeeCostQueryable;
    }

    public async Task<(IEnumerable<Ranking>, int)> GetByFilter(DomainFilter.RankingFilter filter, RankingQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = rankingQueryable.GetRankingsQueryFiltered(filter, options);

            var rankings = await query
                .OrderByDescending(rk => rk.MaxValue)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (rankings, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting rankings by filter");
            return ([], 0);
        }
    }

    public async Task<bool> IsExistHighestRanking()
    {
        try
        {
            var orgUid = GetCurrentOrgUid();
            var query = rankingQueryable.GetRankingsQuery(new RankingQueryableOptions())
                .Where(rk => rk.OrgUid == orgUid)
                .Where(rk => rk.MinValue != null && rk.MaxValue == null);
            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if highest ranking exists");
            return false;
        }
    }

    public async Task<bool> IsExistLowestRanking()
    {
        try
        {
            var orgUid = GetCurrentOrgUid();
            var query = rankingQueryable.GetRankingsQuery(new RankingQueryableOptions())
                .Where(rk => rk.OrgUid == orgUid)
                .Where(rk => rk.MinValue == null && rk.MaxValue != null);
            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if lowest ranking exists");
            return false;
        }
    }

    public async Task<Ranking?> GetById(Guid rankingId, RankingQueryableOptions options)
    {
        try
        {
            var query = rankingQueryable.GetRankingsQuery(options)
                .Where(rk => rk.RankingUid == rankingId);

            var ranking = await query.FirstOrDefaultAsync();

            return ranking;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting ranking by id {RankingId}", rankingId);
            return null;
        }
    }

    public async Task<Ranking?> Create(Ranking ranking)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Rankings.AddAsync(ranking);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return ranking;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating ranking {RankingName}", ranking.RankingName);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Ranking?> Update(Ranking ranking)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Rankings.Update(ranking);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return ranking;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating ranking {RankingName}", ranking.RankingName);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<(IEnumerable<EmployeeCost>, int)> GetEmployeeCostsByFilter(DomainFilter.EmployeeCostFilter filter, EmployeeCostQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = employeeCostQueryable.GetEmployeeCostsQueryFiltered(filter, options);

            var employeeCosts = await query
                .OrderByDescending(ec => ec.EffectiveDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (employeeCosts, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee costs by filter");
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<EmployeeCost>, int)> GetEmployeeCostsByEmployeeId(
        Guid employeeId, DomainFilter.EmployeeCostFilter filter, EmployeeCostQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = employeeCostQueryable.GetEmployeeCostsQueryFiltered(filter, options)
                .Where(ec => ec.EmployeeUid == employeeId);

            var employeeCosts = await query
                .OrderByDescending(ec => ec.EffectiveDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (employeeCosts, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee costs by employee id {EmployeeId}", employeeId);
            return ([], 0);
        }
    }
}
