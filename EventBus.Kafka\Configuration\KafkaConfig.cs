namespace EventBus.Kafka.Configuration;

/// <summary>
/// Kafka configuration settings
/// </summary>
public class KafkaConfig
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = nameof(KafkaConfig);

    /// <summary>
    /// Kafka bootstrap servers
    /// </summary>
    public required string BootstrapServers { get; set; }

    /// <summary>
    /// Consumer group ID
    /// </summary>
    public required string GroupId { get; set; }

    /// <summary>
    /// Auto offset reset policy
    /// </summary>
    public required string AutoOffsetReset { get; set; }

    /// <summary>
    /// Enable auto commit
    /// </summary>
    public required bool EnableAutoCommit { get; set; }

    /// <summary>
    /// Session timeout in milliseconds
    /// </summary>
    public required int SessionTimeoutMs { get; set; }

    /// <summary>
    /// Default topic prefix
    /// </summary>
    public required string TopicPrefix { get; set; }

    /// <summary>
    /// Number of partitions for new topics
    /// </summary>
    public required int DefaultPartitions { get; set; }

    /// <summary>
    /// Replication factor for new topics
    /// </summary>
    public required short DefaultReplicationFactor { get; set; }
}

/// <summary>
/// Kafka producer specific configuration
/// </summary>
public class KafkaProducerConfig : KafkaConfig
{
    /// <summary>
    /// Acknowledgment level
    /// </summary>
    public required string Acks { get; set; }

    /// <summary>
    /// Batch size
    /// </summary>
    public required int BatchSize { get; set; }

    /// <summary>
    /// Linger time in milliseconds
    /// </summary>
    public required int LingerMs { get; set; }
}

/// <summary>
/// Kafka consumer specific configuration
/// </summary>
public class KafkaConsumerConfig : KafkaConfig
{
    /// <summary>
    /// Fetch minimum bytes
    /// </summary>
    public required int FetchMinBytes { get; set; }

    /// <summary>
    /// Maximum poll interval in milliseconds
    /// </summary>
    public required int MaxPollIntervalMs { get; set; }

    /// <summary>
    /// Heartbeat interval in milliseconds
    /// </summary>
    public required int HeartbeatIntervalMs { get; set; }

    /// <summary>
    /// Auto commit interval in milliseconds
    /// </summary>
    public required int AutoCommitIntervalMs { get; set; }
}
