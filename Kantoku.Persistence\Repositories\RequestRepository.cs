using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IRequestRepository
{
    Task<(IEnumerable<Request>, int)> GetByFilter(DomainFilter.RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByAuthor(Guid authorId, DomainFilter.RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByManager(Guid managerId, DomainFilter.RequestFilter filter, RequestQueryableOptions options);
    Task<(IEnumerable<Request>, int)> GetByProject(Guid projectId, DomainFilter.RequestFilter filter, RequestQueryableOptions options);
    Task<Request?> GetById(Guid requestId, RequestQueryableOptions options);
    Task<Request?> Create(Request request, RequestQueryableOptions options);
    Task<Request?> Update(Request request, RequestQueryableOptions options);
}

public class RequestRepository : BaseRepository<Request>, IRequestRepository
{
    private readonly IRequestQueryable requestQueryable;

    public RequestRepository(
        ApplicationDbContext context,
        IRequestQueryable requestQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
        this.requestQueryable = requestQueryable;
    }

    public async Task<Request?> GetById(Guid requestId, RequestQueryableOptions options)
    {
        try
        {
            var query = requestQueryable.GetRequestQueryIncluded(options)
                .Where(r => r.RequestUid == requestId);

            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request with requestId {RequestId}", requestId);
            return null;
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByFilter(DomainFilter.RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all paginated requests");
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByAuthor(Guid authorId, DomainFilter.RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options)
                .Where(r => r.AuthorUid == authorId);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error("Error get requests by user: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByManager(Guid managerId, DomainFilter.RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options);

            var managedProjects = await context.ProjectManagers
                .Where(p => p.EmployeeUid == managerId)
                .Where(p => p.Project.IsDeleted == false && p.Project.StatusCode == ProjectStatus.STARTED)
                .Select(p => p.Project)
                .ToListAsync();

            var isAlternativeApprover = await context.Employees
                .Where(e => e.EmployeeUid == managerId)
                .Where(e => e.HasApprovalAuthority == true)
                .AnyAsync();

            if (isAlternativeApprover)
            {
                // TODO: Get all requests
                var entireRequests = await query
                    .OrderBy(r => r.CreatedTime)
                    .Skip((filter.PageNum - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();
                var totalEntireRequests = await query
                    .CountAsync();
                return (entireRequests, totalEntireRequests);
            }
            if (managedProjects.Any(p => p.IsOffice == true))
            {
                // TODO: Get office requests
                var officeRequestTypes = new List<string> { RequestTypeConstants.LEAVE, RequestTypeConstants.WORK };
                var officeRequests = await query
                    .Where(r => r.ProjectUid == null)
                    .Where(r => officeRequestTypes.Contains(r.RequestTypeCode))
                    .OrderBy(r => r.CreatedTime)
                    .Skip((filter.PageNum - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();
                var totalOfficeRequests = await query
                    .CountAsync();
                return (officeRequests, totalOfficeRequests);
            }
            else if (managedProjects.Count > 0)
            {
                // TODO: Get requests by project
                var worksiteRequestTypes = new List<string> { RequestTypeConstants.OVERTIME, RequestTypeConstants.IN_OUT };
                var projectIds = managedProjects.Select(p => p.ProjectUid).ToList();
                var requestsByProject = await query
                    .Where(r => r.ProjectUid != null && projectIds.Contains(r.ProjectUid.Value))
                    .Where(r => worksiteRequestTypes.Contains(r.RequestTypeCode))
                    .OrderBy(r => r.CreatedTime)
                    .Skip((filter.PageNum - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();
                var totalRequestsByProject = await query
                    .CountAsync();
                return (requestsByProject, totalRequestsByProject);
            }
            return ([], 0);
        }
        catch (System.Exception ex)
        {
            logger.Error("Error: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<Request>, int)> GetByProject(Guid projectId, DomainFilter.RequestFilter filter, RequestQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = requestQueryable.GetRequestQueryFiltered(filter, options)
                .Where(r => r.ProjectUid == projectId);

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (System.Exception ex)
        {
            logger.Error("Error: {error}", ex);
            return ([], 0);
        }
    }

    public async Task<Request?> Create(Request request, RequestQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Requests.AddAsync(request);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(request.RequestUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating request {RequestId}", request.RequestUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Request?> Update(Request request, RequestQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Requests.Update(request);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(request.RequestUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating request {RequestId}", request.RequestUid);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
