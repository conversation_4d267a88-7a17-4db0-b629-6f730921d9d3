using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Queryables;

public class CategoryQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedParent { get; set; } = false;
    public bool IncludedChildren { get; set; } = false;
    public bool IncludedItems { get; set; } = false;
}

public interface ICategoryQueryable
{
    IQueryable<Category> GetCategoriesQuery(
        CategoryQueryableOptions options
    );
    IQueryable<Category> GetCategoriesQueryIncluded(
        CategoryQueryableOptions options,
        IQueryable<Category>? query = null
    );
    IQueryable<Category> GetCategoriesQueryFiltered(
        CategoryFilter filter,
        CategoryQueryableOptions options,
        IQueryable<Category>? query = null
    );
}

public class CategoryQueryable(ApplicationDbContext context) :
    BaseQueryable<Category>(context), ICategoryQueryable
{
    public IQueryable<Category> GetCategoriesQuery(
        CategoryQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Category> GetCategoriesQueryIncluded(
        CategoryQueryableOptions options,
        IQueryable<Category>? query = null
    )
    {
        query ??= GetCategoriesQuery(options);
        if (options.IncludedParent || options.IncludedAll)
        {
            query = query.Include(p => p.ParentCategory);
        }
        if (options.IncludedChildren || options.IncludedAll)
        {
            query = query.Include(p => p.Children);
        }
        if (options.IncludedItems || options.IncludedAll)
        {
            query = query.Include(p => p.Items);
        }

        return query;
    }

    public IQueryable<Category> GetCategoriesQueryFiltered(
        CategoryFilter filter,
        CategoryQueryableOptions options,
        IQueryable<Category>? query = null
    )
    {
        query ??= GetCategoriesQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId || p.OrgUid == null);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => p.CategoryCode.Contains(filter.Keyword)
            || p.CategoryName.Contains(filter.Keyword));
        }
        if (filter.ParentId != Guid.Empty)
        {
            query = query.Where(p => p.ParentUid == filter.ParentId);
        }
        return query;
    }
}

