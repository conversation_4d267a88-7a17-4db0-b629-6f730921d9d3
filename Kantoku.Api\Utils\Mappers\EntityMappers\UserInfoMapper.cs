using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Account.Request;
using Kantoku.Api.Dtos.UserInfo.Request;
using Kantoku.Api.Dtos.UserInfo.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class UserInfoMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a UserInfo entity to a UserInfoResponseDto
    /// </summary>
    /// <param name="userInfo"></param>
    /// <returns> UserInfoResponseDto </returns>
    public static UserInfoResponseDto ToUserInfoResponseDto(this UserInfo userInfo)
    {
        if (userInfo == null)
            return new UserInfoResponseDto();

        return new UserInfoResponseDto
        {
            UserName = userInfo.Name,
            Address = userInfo.Address,
            Phone = userInfo.Phone,
            Gender = userInfo.Gender,
            Birthday = userInfo.Birthday?.ToString("yyyy-MM-dd"),
            AvatarUrl = userInfo.AvatarUrl
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateUserInfoRequestDto to a UserInfo entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="accountUid"></param>
    /// <returns> UserInfo entity </returns>
    public static UserInfo? ToEntity(this CreateUserInfoRequestDto dto, Guid accountUid)
    {
        if (dto == null)
            return null;

        var entity = new UserInfo
        {
            UserInfoUid = GuidHelper.GenerateUUIDv7(),
            AccountUid = accountUid,
            Name = dto.Name,
            Address = dto.Address,
            Phone = dto.Phone,
            Gender = dto.Gender,
            Birthday = !string.IsNullOrEmpty(dto.Birthday) ? DateOnly.Parse(dto.Birthday) : null
        };

        return entity;
    }

    /// <summary>
    /// Maps a CreateUserInfoRequestDto to a UserInfo entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="accountUid"></param>
    /// <returns> UserInfo entity </returns>
    public static UserInfo? ToEntity(this UserInfoRequestDto dto, Guid accountUid)
    {
        if (dto == null)
            return null;

        return new UserInfo
        {
            UserInfoUid = GuidHelper.GenerateUUIDv7(),
            AccountUid = accountUid,
            Name = dto.Name,
            Address = dto.Address,
            Phone = dto.Phone,
            Gender = dto.Gender,
            Birthday = DateOnly.TryParse(dto.Birthday, out var birthday) ? birthday : null,
        };
    }

    /// <summary>
    /// Updates a UserInfo entity from an UpdateUserInfoRequestDto
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dto"></param>
    public static void UpdateFromDto(this UserInfo entity, UpdateUserInfoRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.Name != null)
            entity.Name = dto.Name;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.Phone != null)
            entity.Phone = dto.Phone;

        if (dto.Gender.HasValue)
            entity.Gender = dto.Gender.Value;

        if (dto.Birthday != null)
        {
            entity.Birthday = !string.IsNullOrEmpty(dto.Birthday) ?
                DateOnly.Parse(dto.Birthday) :
                null;
        }
    }

    #endregion
}