using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Contractor.Request;
using Kantoku.Api.Dtos.Contractor.Response;
using Kantoku.SharedKernel.Helpers;
using System.Linq;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ContractorMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Contractor entity to a ContractorResponseDto
    /// </summary>
    /// <param name="contractor">The Contractor entity to map</param>
    /// <returns>The mapped ContractorResponseDto</returns>
    public static ContractorResponseDto ToContractorResponseDto(this Contractor contractor)
    {
        if (contractor == null)
            return new ContractorResponseDto();

        var responseDto = new ContractorResponseDto
        {
            ContractorId = contractor.ContractorUid.ToString(),
            ContractorCode = contractor.ContractorCode,
            ContractorName = contractor.ContractorName,
            ContractorSubName = contractor.ContractorSubName,
            CorporateNumber = contractor.CorporateNumber,
            Address = contractor.Address,
            PhoneNumber = contractor.PhoneNumber,
            Email = contractor.Email,
            Description = contractor.Description,
            LogoUrl = contractor.LogoUrl,
            ContactPerson = contractor.ContactPerson == null ? null : new ContactPerson
            {
                Name = contractor.ContactPerson.Name,
                Email = contractor.ContactPerson.Email,
                PhoneNumber = contractor.ContactPerson.PhoneNumber
            },
            CreateTime = DateTimeHelper.ParseToLocalTime(contractor.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(contractor.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };

        return responseDto;
    }

    /// <summary>
    /// Maps a collection of Contractor entities to a collection of ContractorResponseDto
    /// </summary>
    /// <param name="contractors">The collection of Contractor entities to map</param>
    /// <returns>The mapped collection of ContractorResponseDto</returns>
    public static IEnumerable<ContractorResponseDto> ToContractorResponseDtos(this IEnumerable<Contractor> contractors)
    {
        if (contractors == null || !contractors.Any())
            return [];

        return contractors.Select(c => c.ToContractorResponseDto());
    }

    /// <summary>
    /// Maps a collection of Contractor entities to a ContractorsResponseDto
    /// </summary>
    /// <param name="contractors">The collection of Contractor entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    public static ContractorsResponseDto ToContractorsResponseDto(
        this IEnumerable<Contractor> contractors,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        return new ContractorsResponseDto
        {
            Items = contractors.ToContractorResponseDtos(),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }



    /// <summary>
    /// Maps a Contractor entity to a ContractorDetailResponseDto
    /// </summary>
    /// <param name="contractor">The Contractor entity to map</param>
    /// <param name="projects">The projects to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns>The mapped ContractorDetailResponseDto</returns>
    public static ContractorDetailResponseDto ToContractorDetailResponseDto(
        this Contractor contractor,
        IEnumerable<Project> projects,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        // res.PaymentRequests = contractor.ConstructionPaymentRequests.Select(paymentRequest => new ContractorPaymentResponseDto
        // {
        //     ProjectName = paymentRequest.ConstructionCost.Construction.Project.ProjectName,
        //     ConstructionName = paymentRequest.ConstructionCost.Construction.ConstructionName,
        //     RequestPaymentFrom = paymentRequest.IssueDateFrom.ToString("yyyy-MM-dd"),
        //     RequestPaymentTo = paymentRequest.IssueDateTo.ToString("yyyy-MM-dd"),
        //     PaymentDate = paymentRequest.IssueDateFrom.ToString("yyyy-MM-dd"),
        //     RequestAmount = paymentRequest.RequestAmount,
        //     RetentionAmount = paymentRequest.RetentionAmount,
        //     ReleasedAmount = paymentRequest.ReleasedAmount,
        //     TotalClaimedAmount = paymentRequest.RequestAmount - paymentRequest.RetentionAmount + paymentRequest.ReleasedAmount,
        // });

        return new ContractorDetailResponseDto
        {
            ContractorId = contractor.ContractorUid.ToString(),
            ContractorCode = contractor.ContractorCode,
            ContractorName = contractor.ContractorName,
            ContractorSubName = contractor.ContractorSubName,
            CorporateNumber = contractor.CorporateNumber,
            Address = contractor.Address,
            PhoneNumber = contractor.PhoneNumber,
            Email = contractor.Email,
            Description = contractor.Description,
            LogoUrl = contractor.LogoUrl,
            ContactPerson = contractor.ContactPerson == null ? null : new ContactPerson
            {
                Name = contractor.ContactPerson.Name,
                Email = contractor.ContactPerson.Email,
                PhoneNumber = contractor.ContactPerson.PhoneNumber
            },
            CreateTime = contractor.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = contractor.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            Projects = projects.ToContractedProjectResponseDto(),
            ProjectListPageNum = pageNum,
            ProjectListPageSize = pageSize,
            ProjectListTotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateContractorRequestDto to a Contractor entity
    /// </summary>
    /// <param name="dto">The CreateContractorRequestDto to map</param>
    /// <param name="orgUid">The organization UID</param>
    /// <returns>The mapped Contractor entity</returns>
    public static Contractor? ToEntity(this CreateContractorRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Contractor
        {
            ContractorUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            ContractorCode = dto.ContractorCode,
            ContractorName = dto.ContractorName,
            ContractorSubName = dto.ContractorSubName,
            CorporateNumber = dto.CorporateNumber,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            Description = dto.Description,
            IsDeleted = false,
            ContactPerson = dto.ContactPerson == null ? null : new ContactPerson
            {
                Name = dto.ContactPerson.Name,
                Email = dto.ContactPerson.Email,
                PhoneNumber = dto.ContactPerson.PhoneNumber
            }
        };

        return entity;
    }

    /// <summary>
    /// Maps an UpdateContractorRequestDto to a Contractor entity
    /// </summary>
    /// <param name="entity">The Contractor entity to update</param>
    /// <param name="dto">The UpdateContractorRequestDto to map</param>
    /// <param name="logoUrl">The logo URL</param>
    public static void UpdateFromDto(this Contractor entity, UpdateContractorRequestDto dto, string? logoUrl = null)
    {
        if (entity == null || dto == null)
            return;

        if (dto.ContractorCode != null)
            entity.ContractorCode = dto.ContractorCode;

        if (dto.ContractorName != null)
            entity.ContractorName = dto.ContractorName;

        if (dto.ContractorSubName != null)
            entity.ContractorSubName = dto.ContractorSubName;

        if (dto.CorporateNumber != null)
            entity.CorporateNumber = dto.CorporateNumber;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (logoUrl != null)
            entity.LogoUrl = logoUrl;

        if (dto.ContactPerson != null)
        {
            entity.ContactPerson = new ContactPerson // Or update existing entity.ContactPerson fields?
            {
                Name = dto.ContactPerson.Name,
                Email = dto.ContactPerson.Email,
                PhoneNumber = dto.ContactPerson.PhoneNumber
            };
        }
    }

    #endregion
}