using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Manufacturer.Request;
using Kantoku.Api.Dtos.Manufacturer.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ManufacturerController : BaseController
{
    private readonly IManufacturerService manufacturerService;
    private readonly IAuditLogService auditLogService;
    public ManufacturerController(IManufacturerService manufacturerService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService)
        : base(responseFactory)
    {
        this.manufacturerService = manufacturerService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get manufacturers by filter criteria
    /// </summary>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of manufacturers matching the filter</returns>
    [HttpGet]
    public async Task<GeneralResponse<ManufacturersResponseDto>> GetByFilter([FromQuery] ManufacturerFilter filter)
    {
        try
        {
            var res = await manufacturerService.GetByFilter(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ManufacturersResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get manufacturer by ID
    /// </summary>
    /// <param name="id">Manufacturer ID</param>
    /// <param name="keyword">Keyword to search for (item code/name)</param>
    /// <param name="pageNum">Page number of item list</param>
    /// <param name="pageSize">Page size of item list</param>
    /// <returns>Manufacturer details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ManufacturerDetailResponseDto>> GetById(
        [FromRoute] Guid id,
        [FromQuery] string? keyword,
        [FromQuery] int pageNum = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ManufacturerDetailResponseDto>();

            var result = await manufacturerService.GetById(id, keyword, pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ManufacturerDetailResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>   
    /// Get manufacturer logo
    /// </summary>
    /// <param name="id">Manufacturer ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>Manufacturer logo</returns>
    [AllowAnonymous]
    [HttpGet("{id}/logo")]
    public async Task<dynamic> GetLogo([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await manufacturerService.GetLogo(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.MANUFACTURER_LOGO_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new manufacturer
    /// </summary>
    /// <param name="requestDto">Manufacturer creation data</param>
    /// <returns>Created manufacturer details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<ManufacturerResponseDto>> Create([FromForm] CreateManufacturerRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ManufacturerResponseDto>();

            var result = await manufacturerService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ManufacturerResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing manufacturer
    /// </summary>
    /// <param name="id">Manufacturer ID</param>
    /// <param name="requestDto">Updated manufacturer data</param>
    /// <returns>Updated manufacturer details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<ManufacturerResponseDto>> Update([FromRoute] Guid id, [FromForm] UpdateManufacturerRequestDto requestDto)
    {
        try
        {
            var result = await manufacturerService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ManufacturerResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a manufacturer
    /// </summary>
    /// <param name="id">Manufacturer ID to delete</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await manufacturerService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a manufacturer
    /// </summary>
    /// <param name="id">Manufacturer ID</param>
    /// <param name="filter">Audit log filter parameters</param>
    /// <returns>List of audit logs</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Manufacturer>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
