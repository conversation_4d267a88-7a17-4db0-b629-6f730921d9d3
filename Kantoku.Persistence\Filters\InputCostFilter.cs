﻿namespace Kantoku.Persistence.Filters;

public class InputCostFilter : BaseFilter
{
    /// <summary>
    /// The keyword to search for
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// The project ID
    /// </summary>
    public Guid? ProjectId { get; set; }

    /// <summary>
    /// The construction ID
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// The vendor ID
    /// </summary>
    public Guid? VendorId { get; set; }

    /// <summary>
    /// The title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// The payment type ID
    /// </summary>
    public Guid? PaymentTypeId { get; set; }

    /// <summary>
    /// The entry type ID
    /// </summary>
    public Guid? EntryTypeId { get; set; }

    /// <summary>
    /// The issue date from
    /// </summary>
    public string? IssueDateFrom { get; set; }

    /// <summary>
    /// The issue date to
    /// </summary>
    public string? IssueDateTo { get; set; }

    /// <summary>
    /// The payment date from
    /// </summary>
    public string? PaymentDateFrom { get; set; }

    /// <summary>
    /// The payment date to
    /// </summary>
    public string? PaymentDateTo { get; set; }

    /// <summary>
    /// The total amount min
    /// </summary>
    public string? TotalAmountMin { get; set; }

    /// <summary>
    /// The total amount max
    /// </summary>
    public string? TotalAmountMax { get; set; }
}
