using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class ConstructionCostConfiguration(string schema) : IEntityTypeConfiguration<ConstructionCost>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ConstructionCost> builder)
    {
        builder.HasKey(e => e.ConstructionCostUid).HasName("construction_cost_pkey");

        builder.ToTable("construction_cost", Schema);

        builder.Property(e => e.ConstructionCostUid)
            .HasColumnName("construction_cost_uid");
        builder.Property(e => e.ConstructionUid)
            .HasColumnName("construction_uid");
        builder.Property(e => e.StartDate)
            .HasColumnName("start_date");
        builder.Property(e => e.EndDate)
            .HasColumnName("end_date");
        builder.Property(e => e.TotalCostAmount)
            .HasColumnName("total_cost_amount");
        builder.Property(e => e.RiskModifiedAmount)
            .HasColumnName("risk_modified_amount");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.ConstructionCosts)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_cost_org_id_fkey");

        builder.HasOne(d => d.Construction)
            .WithMany(p => p.ConstructionCosts)
            .HasForeignKey(d => d.ConstructionUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_cost_construction_id_fkey");
    }
} 