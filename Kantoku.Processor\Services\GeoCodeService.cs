using System.Text.Json;
using Kantoku.Processor.Configurations;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Services.Interfaces;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Kantoku.Processor.Services;

/// <inheritdoc/>
public class GeoCodeService : IGeoCodeService
{
    private readonly ILogger<GeoCodeService> _logger;
    private readonly IDistributedCache _cache;
    private readonly AppConfig _appConfig;

    public GeoCodeService(
        ILogger<GeoCodeService> logger,
        IDistributedCache cache,
        IOptions<AppConfig> appConfig)
    {
        _logger = logger;
        _cache = cache;
        _appConfig = appConfig.Value;
    }

    /// <inheritdoc/>
    public async Task<Coordinates?> GetCoordinatesAsync(string? address, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(address))
            return null;

        string cacheKey = $"coordinates:{Uri.EscapeDataString(address)}";
        var cachedCoordinates = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (!string.IsNullOrEmpty(cachedCoordinates))
        {
            _logger.LogDebug("Coordinates for address '{Address}' found in cache.", address);
            return JsonSerializer.Deserialize<Coordinates>(cachedCoordinates);
        }

        _logger.LogDebug("Coordinates for address '{Address}' not found in cache. Fetching from API.", address);
        string requestUri = string.Format("https://maps.googleapis.com/maps/api/geocode/json?key={1}&address={0}", Uri.EscapeDataString(address), _appConfig.GeoCodingAPK);
        var client = new HttpClient();
        try
        {
            var responseMessage = await client.GetAsync(requestUri, cancellationToken);
            if (!responseMessage.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to fetch coordinates for address '{Address}'. Status: {StatusCode}", address, responseMessage.StatusCode);
                return null;
            }
            var responseStream = await responseMessage.Content.ReadAsStreamAsync(cancellationToken);
            var geocodingResponse = await JsonSerializer.DeserializeAsync<GeocodingResponseDto>(responseStream, cancellationToken: cancellationToken);
            if (geocodingResponse is null || geocodingResponse.Results is null || !geocodingResponse.Results.Any())
            {
                _logger.LogWarning("No geocoding results for address '{Address}'.", address);
                return null;
            }
            var coordinates = new Coordinates(geocodingResponse.Results.First().Geometry.Location.Lat, geocodingResponse.Results.First().Geometry.Location.Lng);

            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1)); // Cache for 1 hour, extendable
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(coordinates), cacheEntryOptions, cancellationToken);
            _logger.LogDebug("Coordinates for address '{Address}' cached.", address);

            return coordinates;
        }
        catch (JsonException jsonEx)
        {
            _logger.LogError(jsonEx, "Error deserializing geocoding response for address '{Address}'.", address);
            return null;
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error getting project coordinates for address '{Address}' from API.", address);
            return null;
        }
    }

    /// <inheritdoc/>
    public async Task<string?> GetAddressAsync(double latitude, double longitude, CancellationToken cancellationToken = default)
    {
        string cacheKey = $"address:{latitude}:{longitude}";
        var cachedAddress = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (!string.IsNullOrEmpty(cachedAddress))
        {
            _logger.LogDebug("Address for coordinates '{Latitude}, {Longitude}' found in cache.", latitude, longitude);
            return cachedAddress;
        }

        string requestUri = string.Format("https://maps.googleapis.com/maps/api/geocode/json?key={1}&latlng={0}", Uri.EscapeDataString($"{latitude},{longitude}"), _appConfig.GeoCodingAPK);
        var client = new HttpClient();
        try
        {
            var responseMessage = await client.GetAsync(requestUri, cancellationToken);
            if (!responseMessage.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to fetch address for coordinates '{Latitude}, {Longitude}'. Status: {StatusCode}", latitude, longitude, responseMessage.StatusCode);
                return null;
            }
            var responseStream = await responseMessage.Content.ReadAsStreamAsync(cancellationToken);
            var geocodingResponse = await JsonSerializer.DeserializeAsync<ReverseGeocodingResponseDto>(responseStream, cancellationToken: cancellationToken);
            if (geocodingResponse is null || geocodingResponse.Results is null || !geocodingResponse.Results.Any())
            {
                _logger.LogWarning("No geocoding results for coordinates '{Latitude}, {Longitude}'.", latitude, longitude);
                return null;
            }
            var pointOfInterest = geocodingResponse.Results
                .Where(r => r.Types?.Contains("point_of_interest") == true)
                .OrderByDescending(r => r.Types?.Count(t => t == "point_of_interest"))
                .FirstOrDefault() ?? geocodingResponse.Results.First();

            var addressComponents = pointOfInterest.AddressComponents?
                .Where(ac => ac.Types != null && ac.Types.Contains("country") == false && ac.Types.Contains("postal_code") == false)
                .Select(ac => ac.LongName)
                .OrderDescending()
                .ToList() ?? [];

            var address = addressComponents.Count != 0
                ? string.Join(", ", addressComponents)
                : pointOfInterest.FormattedAddress;

            if (string.IsNullOrEmpty(address))
            {
                _logger.LogWarning("No address components found for coordinates '{Latitude}, {Longitude}'.", latitude, longitude);
                return null;
            }

            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1)); // Cache for 1 hour, extendable
            await _cache.SetStringAsync(cacheKey, address, cacheEntryOptions, cancellationToken);
            _logger.LogDebug("Address for coordinates '{Latitude}, {Longitude}' cached.", latitude, longitude);

            return address;
        }
        catch (JsonException jsonEx)
        {
            _logger.LogError(jsonEx, "Error deserializing geocoding response for coordinates '{Latitude}, {Longitude}'.", latitude, longitude);
            return null;
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error getting address for coordinates '{Latitude}, {Longitude}' from API.", latitude, longitude);
            return null;
        }
    }
}
