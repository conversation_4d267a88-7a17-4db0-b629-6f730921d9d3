using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

// public static class WorkShiftRedisKeys
// {
//     private static readonly string prefix = "workshift";
//     public static string Prefix { get => prefix; }
//     public static string GetById(string workshiftUid) => $"{Prefix}:id:{workshiftUid}";
//     public static string GetByFilter(WorkShiftFilter filter) =>
//         $"{Prefix}:filter:{filter.OrgId}:keyword:{filter.Keyword}:page:{filter.PageNum}:size:{filter.PageSize}";
//     public static string GetByProject(string projectUid) =>
//         $"{Prefix}:by-project:{projectUid}";
// }

public interface IWorkShiftRepository
{
    Task<WorkShift?> GetById(Guid workshiftId, WorkShiftQueryableOptions options);
    Task<(IEnumerable<WorkShift>, int)> GetByFilter(DomainFilter.WorkShiftFilter filter, WorkShiftQueryableOptions options);
    Task<IEnumerable<WorkShift>> GetByProjectId(Guid projectId, WorkShiftQueryableOptions options);
    Task<WorkShift?> Create(WorkShift workShift, WorkShiftQueryableOptions options);
    Task<WorkShift?> Update(WorkShift workShift, WorkShiftQueryableOptions options);

    Task<Guid?> Create(WorkShift workShift);
    Task<bool> Update(WorkShift workShift);
}

public class WorkShiftRepository : BaseRepository<WorkShift>, IWorkShiftRepository
{
    private readonly IWorkShiftQueryable workShiftQueryable;
    public WorkShiftRepository(
        ApplicationDbContext context,
        ITenantContext tenantContext,
        Serilog.ILogger logger,
        IWorkShiftQueryable workShiftQueryable
    )
        : base(context, logger, tenantContext)
    {
        this.workShiftQueryable = workShiftQueryable;
    }

    public async Task<WorkShift?> GetById(Guid workshiftId, WorkShiftQueryableOptions options)
    {
        try
        {
            var query = workShiftQueryable.GetWorkShiftQueryIncluded(options);

            var workShift = await query
                .Where(ws => ws.WorkShiftUid == workshiftId)
                .FirstOrDefaultAsync();

            return workShift;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting work shift by ID {WorkShiftId}", workshiftId);
            return null;
        }
    }

    public async Task<(IEnumerable<WorkShift>, int)> GetByFilter(DomainFilter.WorkShiftFilter filter, WorkShiftQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = workShiftQueryable.GetWorkShiftQueryFiltered(filter, options);

            var workShifts = await query
                .OrderBy(ws => ws.WorkShiftUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var totalRecords = await query
                .CountAsync();
            return (workShifts, totalRecords);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all work shifts");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<WorkShift>> GetByProjectId(Guid projectId, WorkShiftQueryableOptions options)
    {
        try
        {
            var query = workShiftQueryable.GetWorkShiftQueryIncluded(options)
                .Where(ws => ws.ProjectWorkShifts.Any(pws => pws.ProjectUid == projectId));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting work shifts by project ID {ProjectId}", projectId);
            return [];
        }
    }

    public async Task<WorkShift?> Create(WorkShift workShift, WorkShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.WorkShifts.AddAsync(workShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(workShift.WorkShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating work shift ID {WorkShiftId}", workShift.WorkShiftUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(WorkShift workShift)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.WorkShifts.AddAsync(workShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return workShift.WorkShiftUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating work shift ID {WorkShiftId}", workShift.WorkShiftUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<WorkShift?> Update(WorkShift workShift, WorkShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.WorkShifts.Update(workShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(workShift.WorkShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating work shift with ID {WorkShiftUid}", workShift.WorkShiftUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(WorkShift workShift)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.WorkShifts.Update(workShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating work shift with ID {WorkShiftUid}", workShift.WorkShiftUid);
            await transaction.RollbackAsync();
            return false;
        }
    }
}
