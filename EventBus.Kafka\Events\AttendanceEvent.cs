using EventBus.Interfaces;

namespace EventBus.Kafka.Events;

/// <summary>
/// Event published when an employee checks in
/// </summary>
public class AttendanceEvent : IIntegrationEvent
{
    /// <summary>
    /// Unique identifier for the event
    /// </summary>
    public Guid EventId { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the event occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// The organization ID associated with the check-in
    /// </summary>
    public Guid OrgId { get; set; }

    /// <summary>
    /// The shift ID associated with the check-in
    /// </summary>
    public Guid ShiftId { get; set; }

    /// <summary>   
    /// Determine if this event is for check-in or check-out  
    /// </summary>
    public bool IsCheckIn { get; set; }

    /// <summary>
    /// GPS coordinates of the check-in location
    /// </summary>
    public GpsCoordinates Coordinates { get; set; } = new();
}

/// <summary>
/// GPS coordinates
/// </summary>
public class GpsCoordinates
{
    /// <summary>
    /// Latitude coordinate
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// Longitude coordinate
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// Checks if coordinates are valid
    /// </summary>
    public bool IsValid => Latitude is not null && Latitude >= -90 && Latitude <= 90 &&
                          Longitude is not null && Longitude >= -180 && Longitude <= 180;
}