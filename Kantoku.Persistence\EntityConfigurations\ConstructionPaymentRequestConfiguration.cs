using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class ConstructionPaymentRequestConfiguration(string schema) : IEntityTypeConfiguration<ConstructionPaymentRequest>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ConstructionPaymentRequest> builder)
    {
        builder.HasKey(e => e.ConstructionPaymentRequestUid)
            .HasName("construction_payment_request_pkey");

        builder.ToTable("construction_payment_request", Schema);

        builder.Property(e => e.ConstructionPaymentRequestUid)
            .HasColumnName("payment_request_uid");
        builder.Property(e => e.IssueDateFrom)
            .HasColumnName("issue_date_from");
        builder.Property(e => e.IssueDateTo)
            .HasColumnName("issue_date_to");
        builder.Property(e => e.RequestAmount)
            .HasColumnName("request_amount");
        builder.Property(e => e.Retention<PERSON>mount)
            .HasColumnName("retention_amount");
        builder.Property(e => e.ReleasedAmount)
            .HasColumnName("released_amount");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.ConstructionCostUid)
            .HasColumnName("construction_cost_uid");
        builder.Property(e => e.ContractorUid)
            .HasColumnName("contractor_uid");

        builder.HasIndex(e => e.OrgUid).HasDatabaseName("idx_construction_payment_requestorg");
        builder.HasIndex(e => e.ConstructionCostUid).HasDatabaseName("idx_construction_payment_request_construction_cost");
        builder.HasIndex(e => e.ContractorUid).HasDatabaseName("idx_construction_payment_request_contractor");
        builder.HasIndex(e => e.IsDeleted).HasDatabaseName("idx_construction_payment_request_is_deleted");

        builder.HasOne(d => d.ConstructionCost)
            .WithOne(p => p.ConstructionPaymentRequest)
            .HasForeignKey<ConstructionPaymentRequest>(d => d.ConstructionCostUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_payment_request_construction_cost_id_fkey");

        builder.HasOne(d => d.Contractor)
            .WithMany(p => p.ConstructionPaymentRequests)
            .HasForeignKey(d => d.ContractorUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_payment_request_contractor_id_fkey");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.ConstructionPaymentRequests)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_payment_request_org_id_fkey");
    }
} 