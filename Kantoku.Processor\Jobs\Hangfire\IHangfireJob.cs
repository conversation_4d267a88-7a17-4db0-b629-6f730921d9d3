namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Interface for Hangfire job implementations
/// </summary>
public interface IHangfireJob
{
    /// <summary>
    /// Executes the job
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ExecuteAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for Hangfire jobs with parameters
/// </summary>
/// <typeparam name="T">Parameter type</typeparam>
public interface IHangfireJob<in T>
{
    /// <summary>
    /// Executes the job with parameters
    /// </summary>
    /// <param name="parameters">Job parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ExecuteAsync(T parameters, CancellationToken cancellationToken = default);
}
