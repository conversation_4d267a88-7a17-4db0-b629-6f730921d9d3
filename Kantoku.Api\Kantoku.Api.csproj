﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Kantoku.Api</RootNamespace>
    <UserSecretsId>0716927a-9439-46d6-afd8-7e15da560e1c</UserSecretsId>
    <InvariantGlobalization>false</InvariantGlobalization>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="FirebaseAdmin" Version="3.1.0" />
    <PackageReference Include="itext7" Version="9.0.0" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="9.0.0" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="Minio" Version="6.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
    <PackageReference Include="Serilog.Enrichers.ClientInfo" Version="2.1.1" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Exceptions" Version="5.4.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.12" />
    <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EventBus\EventBus.csproj" />
    <ProjectReference Include="..\EventBus.Kafka\EventBus.Kafka.csproj" />
    <ProjectReference Include="..\Kantoku.Persistence\Kantoku.Persistence.csproj" />
    <ProjectReference Include="..\Kantoku.SharedKernel\Kantoku.SharedKernel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\Templates\*.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\Errors\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\Fonts\*.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </EmbeddedResource>
  </ItemGroup>

</Project>
