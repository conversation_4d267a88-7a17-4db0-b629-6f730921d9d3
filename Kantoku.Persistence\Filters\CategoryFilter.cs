namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for category queries - contains only business logic, no HTTP concerns
/// </summary>
public class CategoryFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in category name or code
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by parent category ID
    /// </summary>
    public Guid? ParentId { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}

