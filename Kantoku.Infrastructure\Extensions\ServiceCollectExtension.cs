namespace Kantoku.Infrastructure.Extensions
{
    public static class ServiceCollectExtension
    {
        public static IServiceCollection AddEmailServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure EmailSettings
            services.Configure<EmailSettings>(configuration.GetSection("EmailSettings"));

            // Register IEmailService
            services.AddTransient<IEmailService, SmtpEmailService>();

            return services;
        }

        public static IServiceCollection AddCachingServices(this IServiceCollection services, IConfiguration configuration)
        {
            var redisConnectionString = configuration.GetConnectionString("Redis");
            if (string.IsNullOrEmpty(redisConnectionString))
            {
                throw new ArgumentNullException(nameof(redisConnectionString), "Redis connection string is not configured.");
            }

            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnectionString;
                options.InstanceName = configuration["Redis:InstanceName"] ?? "KantokuService3_"; // Optional: Prefix for cache keys
            });

            services.AddSingleton<ICachingService, RedisCachingService>();

            return services;
        }
    }
}
