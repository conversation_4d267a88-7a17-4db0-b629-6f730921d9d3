﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IInputCostRepository
{
    Task<(int, IEnumerable<InputCost>)> GetByFilter(DomainFilter.InputCostFilter filter, InputCostQueryableOptions options);
    Task<InputCost?> GetById(Guid inputCostId, InputCostQueryableOptions options);
    Task<InputCost?> Create(InputCost inputCost, InputCostQueryableOptions options);
    Task<InputCost?> Update(InputCost inputCost, InputCostQueryableOptions options);

    Task<Guid?> Create(InputCost inputCost);
    Task<bool> Update(InputCost inputCost);
}

public class InputCostRepository : BaseRepository<InputCostRepository>, IInputCostRepository
{
    private readonly IInputCostQueryable queryable;
    public InputCostRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IInputCostQueryable queryable
    ) : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(int, IEnumerable<InputCost>)> GetByFilter(DomainFilter.InputCostFilter filter, InputCostQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetInputCostsQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .OrderByDescending(st => st.IssueDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting input costs");
            return (0, []);
        }
    }

    public async Task<InputCost?> GetById(Guid inputCostId, InputCostQueryableOptions options)
    {
        try
        {
            var query = queryable.GetInputCostsQueryIncluded(options)
                .Where(st => st.InputCostUid == inputCostId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting input cost by id");
            return null;
        }
    }

    public async Task<InputCost?> Create(InputCost inputCost, InputCostQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.InputCosts.AddAsync(inputCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(inputCost.InputCostUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating input cost");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<InputCost?> Update(InputCost inputCost, InputCostQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.InputCosts.Update(inputCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(inputCost.InputCostUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating input cost");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(InputCost inputCost)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.InputCosts.AddAsync(inputCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return inputCost.InputCostUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating input cost");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(InputCost inputCost)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.InputCosts.Update(inputCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating input cost");
            await transaction.RollbackAsync();
            return false;
        }
    }
}
