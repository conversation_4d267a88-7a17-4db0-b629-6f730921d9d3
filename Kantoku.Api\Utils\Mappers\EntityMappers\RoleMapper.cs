using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Role.Request;
using Kantoku.Api.Dtos.Role.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class RoleMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Role entity to a RoleResponseDto
    /// </summary>
    /// <param name="role"></param>
    /// <returns> RoleResponseDto </returns>
    public static RoleResponseDto ToRoleResponseDto(this Role role)
    {
        if (role == null)
            return new RoleResponseDto();

        return new RoleResponseDto
        {
            RoleId = role.RoleUid.ToString(),
            RoleName = role.RoleName,
            Description = role.Description
        };
    }

    /// <summary>
    /// Maps a collection of Role entities to a RolesResponseDto
    /// </summary>
    /// <param name="roles"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> RolesResponseDto </returns>
    public static RolesResponseDto ToRolesResponseDto(
        this IEnumerable<Role> roles,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (roles == null)
            return new RolesResponseDto
            {
                Items = [],
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0
            };

        return new RolesResponseDto
        {
            Items = roles.Select(r => r.ToRoleResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateRoleRequestDto to a Role entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="orgUid"></param>
    /// <param name="functions"></param>
    /// <returns> Role entity </returns>
    public static Role? ToEntity(this CreateRoleRequestDto dto, Guid orgUid, IEnumerable<Function> functions)
    {
        if (dto == null)
            return null;

        var entity = new Role
        {
            RoleUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            RoleName = dto.RoleName,
            StructureUid = Guid.TryParse(dto.StructureId, out var structureUid) && structureUid != Guid.Empty
                ? structureUid
                : null,
            Description = dto.Description,
            IsDeleted = false,
            IsDefault = false,
            IsHidden = false
        };

        entity.RoleFunctions = functions.Select(f => new RoleFunction
        {
            RoleUid = entity.RoleUid,
            FunctionUid = f.FunctionUid,
            CanRead = false,
            CanCreate = false,
            CanUpdate = false,
            CanDelete = false
        }).ToList();

        return entity;
    }

    /// <summary>
    /// Updates a Role entity from an UpdateRoleRequestDto
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dto"></param>
    public static void UpdateFromDto(this Role entity, UpdateRoleRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.RoleName != null)
            entity.RoleName = dto.RoleName;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
}