using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Kantoku.Persistence.EntityConfigurations;

public class EmployeeInvitationConfiguration(string schema) : IEntityTypeConfiguration<EmployeeInvitation>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeInvitation> builder)
    {
        builder.HasKey(e => e.EmployeeInvitationUid).HasName("employee_invitation_pkey");

        builder.ToTable("employee_invitation", Schema);

        builder.Property(e => e.EmployeeInvitationUid)
            .HasColumnName("employee_invitation_uid");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.IsAccepted)
            .HasDefaultValue(false)
            .HasColumnName("is_accepted");
        builder.Property(e => e.Is<PERSON>eleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.PreassignedEmployeeCode)
            .HasColumnName("preassigned_employee_code");
        builder.Property(e => e.PreassignedRoleUid)
            .HasColumnType("jsonb")
            .HasColumnName("preassigned_role_uid")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ICollection<Guid>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<Guid>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.InvitationDescription)
            .HasColumnName("invitation_description");
        builder.Property(e => e.AcceptedTime)
            .HasColumnType("timestamp")
            .HasColumnName("accepted_time");
        builder.Property(e => e.ExpiredTime)
            .HasColumnType("timestamp")
            .HasColumnName("expired_time");

        builder.HasOne(d => d.Org).WithMany(p => p.EmployeeInvitations)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("employee_invitation_org_id_fkey");
    }
}
