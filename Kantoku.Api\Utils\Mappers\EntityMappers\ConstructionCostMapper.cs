using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.CategorizedCost.Response;
using Kantoku.Api.Dtos.ConstructionCost.Response;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ConstructionCostMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Construction entity to a ConstructionCostSummaryResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <param name="constructionCosts">The list of ConstructionCost entities to map</param>
    /// <param name="dailyReports">The list of ProjectDailyReport entities to map</param>  
    /// <param name="employees">The list of Employee entities to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped ConstructionCostSummaryResponseDto</returns>
    public static ConstructionCostSummaryResponseDto ToConstructionCostSummaryResponseDto(
        this Construction construction,
        IEnumerable<ConstructionCost> constructionCosts,
        IEnumerable<ProjectDailyReport> dailyReports,
        IEnumerable<Employee> employees,
        string languageCode
        )
    {
        var currentCost = constructionCosts
            .Where(c => c.StartDate <= DateOnly.FromDateTime(DateTime.Now) && c.EndDate >= DateOnly.FromDateTime(DateTime.Now))
            .OrderByDescending(c => c.StartDate)
            .FirstOrDefault();
        var currentAccumulateCost = constructionCosts
            .Where(c => c.StartDate <= DateOnly.FromDateTime(DateTime.Now) && c.EndDate >= DateOnly.FromDateTime(DateTime.Now))
            .OrderByDescending(c => c.StartDate)
            .FirstOrDefault();
        var lastAccumulateCost = constructionCosts
            .Where(c => c.StartDate <= DateOnly.FromDateTime(DateTime.Now.AddDays(-1)) && c.EndDate >= DateOnly.FromDateTime(DateTime.Now.AddDays(-1)))
            .OrderByDescending(c => c.StartDate)
            .FirstOrDefault();

        return new ConstructionCostSummaryResponseDto
        {
            ConstructionId = construction.ConstructionUid,
            IsPrimary = construction.IsPrimary,
            CurrentCost = currentCost?.ToConstructionPartialCostResponseDto(languageCode),
            CurrentAccumulateCost = currentAccumulateCost?.ToConstructionPartialCostResponseDto(languageCode),
            LastAccumulateCost = lastAccumulateCost?.ToConstructionPartialCostResponseDto(languageCode),
        };
    }

    /// <summary>
    /// Maps a ConstructionCost entity to a ConstructionPartialCostResponseDto
    /// </summary>
    /// <param name="constructionCost">The ConstructionCost entity to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped ConstructionPartialCostResponseDto</returns>
    public static ConstructionPartialCostResponseDto ToConstructionPartialCostResponseDto(
        this ConstructionCost constructionCost,
        string languageCode
    )
    {
        if (constructionCost == null)
        {
            return new ConstructionPartialCostResponseDto();
        }



        return new ConstructionPartialCostResponseDto
        {
            ConstructionCostId = constructionCost.ConstructionCostUid.ToString(),
            ReportFrom = constructionCost.StartDate.ToString("yyyy-MM-dd"),
            ReportTo = constructionCost.EndDate.ToString("yyyy-MM-dd"),
            ConstructionPaymentRequest = constructionCost.ConstructionPaymentRequest.ToConstructionPaymentResponseDto(constructionCost.ConstructionUid),
            TotalCost = constructionCost.TotalCostAmount,
            RiskAmount = constructionCost.RiskModifiedAmount,
            CategorizedCosts = constructionCost.CategorizedCosts.ToHumanCategorizedCostResponseDtos(languageCode),
        };
    }

    /// <summary>
    /// Maps a ConstructionPaymentRequest entity to a ConstructionPaymentResponseDto
    /// </summary>
    /// <param name="paymentRequest">The ConstructionPaymentRequest entity to map</param>
    /// <param name="constructionUid">The construction uid</param>
    /// <returns>The mapped ConstructionPaymentResponseDto</returns>
    public static ConstructionPaymentResponseDto ToConstructionPaymentResponseDto(
        this ConstructionPaymentRequest paymentRequest,
        Guid constructionUid)
    {
        if (paymentRequest == null)
        {
            return new ConstructionPaymentResponseDto();
        }

        return new ConstructionPaymentResponseDto
        {
            ConstructionId = constructionUid,
            RequestAmount = paymentRequest.RequestAmount,
            RetentionAmount = paymentRequest.RetentionAmount,
            ReleasedAmount = paymentRequest.ReleasedAmount,
            TotalClaimedAmount = paymentRequest.RequestAmount - paymentRequest.RetentionAmount + paymentRequest.ReleasedAmount,
        };
    }

    /// <summary>   
    /// Maps a list of CategorizedCost entities to a list of CategorizedCostResponseDto
    /// </summary>
    /// <param name="categorizedCosts">The list of CategorizedCost entities to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped list of CategorizedCostResponseDto</returns>
    public static IEnumerable<HumanCategorizedCostResponseDto> ToHumanCategorizedCostResponseDtos(
        this IEnumerable<CategorizedCost> categorizedCosts,
        string languageCode
        )
    {
        if (categorizedCosts == null || !categorizedCosts.Any())
        {
            return [];
        }

        return categorizedCosts.Select(c => ToHumanCategorizedCostResponseDto(c, languageCode));
    }

    /// <summary>
    /// Maps a CategorizedCost entity to a CategorizedCostResponseDto
    /// </summary>
    /// <param name="categorizedCost">The CategorizedCost entity to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped CategorizedCostResponseDto</returns>
    public static HumanCategorizedCostResponseDto ToHumanCategorizedCostResponseDto(
        this CategorizedCost categorizedCost,
        string languageCode
    )
    {
        if (categorizedCost == null)
        {
            return new HumanCategorizedCostResponseDto();
        }

        return new HumanCategorizedCostResponseDto
        {
            CategoryId = categorizedCost.CategoryUid.ToString(),
            CategoryCode = categorizedCost.Category.CategoryCode,
            CategoryName = categorizedCost.Category.IsDefault
                ? categorizedCost.Category.CategoryName
                : categorizedCost.Category.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName,
            TotalAmount = categorizedCost.TotalAmount,
        };
    }

    #endregion
}
