using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Filters.Domains;
using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Project.Request;
using Kantoku.Api.Dtos.Project.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ProjectController : BaseController
{
    private readonly IProjectService projectService;
    private readonly IAuditLogService auditLogService;

    public ProjectController(
        ITResponseFactory responseFactory,
        IProjectService projectService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.projectService = projectService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated list of projects with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for projects</param>
    /// <returns>Paginated list of projects</returns>
    [HttpGet]
    public async Task<GeneralResponse<ProjectsResponseDto>> GetProjectPaginated([FromQuery] ProjectFilter filter)
    {
        try
        {
            var result = await projectService.GetProjectsPaginated(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get project details by ID
    /// </summary>
    /// <param name="id">Project ID to retrieve</param>
    /// <returns>Project details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ProjectResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectResponseDto>();

            var result = await projectService.GetProjectById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Summary of current user's managed projects
    /// </summary>
    /// <param name="pageNum">Page number for pagination</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="dateFrom">Date from in format yyyy-MM-dd</param>
    /// <param name="dateTo">Date to in format yyyy-MM-dd</param>
    /// <returns>Project details</returns>
    [HttpGet("summary")]
    public async Task<GeneralResponse<ProjectSummariesResponseDto>> GetProjectSummary(
        [FromQuery] int pageNum = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] DateOnly? dateFrom = null,
        [FromQuery] DateOnly? dateTo = null
    )
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectSummariesResponseDto>();

            DateOnly fromDate = dateFrom ?? DateOnly.FromDateTime(DateTime.Now);
            DateOnly toDate = dateTo ?? DateOnly.FromDateTime(DateTime.Now);

            var result = await projectService.GetProjectSummaries(pageNum, pageSize, fromDate, toDate);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectSummariesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get daily report of a project
    /// </summary>
    /// <param name="id">Project ID</param>
    /// <param name="reportDate">Report date in format yyyy-MM-dd (*)</param>
    /// <returns>Daily report of the project</returns>
    [HttpGet("{id}/daily-report")]
    public async Task<GeneralResponse<ProjectDailyReportResponseDto>> GetProjectDailyReport([FromRoute] Guid id, [FromQuery] string reportDate)
    {
        try
        {
            if (!ModelState.IsValid || !DateOnly.TryParse(reportDate, out var reportDateOnly))
                return BadRequest<ProjectDailyReportResponseDto>();

            var result = await projectService.GetProjectDailyReport(id, reportDateOnly);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectDailyReportResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new project daily report
    /// </summary>
    /// <param name="id">Project ID</param>
    /// <param name="requestDto">Daily report data</param>
    /// <returns>Created daily report details</returns>
    [HttpPost("{id}/daily-report")]
    public async Task<GeneralResponse<ProjectDailyReportResponseDto>> CreateProjectDailyReport([FromRoute] Guid id, [FromBody] CreateDailyReportRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid || !DateOnly.TryParse(requestDto.ReportDate, out _))
                return BadRequest<ProjectDailyReportResponseDto>();

            var result = await projectService.CreateProjectDailyReport(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectDailyReportResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update a project daily report
    /// </summary>
    /// <param name="reportId">Report ID</param>
    /// <param name="requestDto">Daily report data</param>
    /// <returns>Updated daily report details</returns>
    [HttpPut("daily-report/{reportId}")]
    public async Task<GeneralResponse<ProjectDailyReportResponseDto>> UpdateProjectDailyReport([FromRoute] Guid reportId, [FromBody] UpdateDailyReportRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectDailyReportResponseDto>();

            var result = await projectService.UpdateProjectDailyReport(reportId, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectDailyReportResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a project daily report
    /// </summary>
    /// <param name="reportId">Report ID</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("daily-report/{reportId}")]
    public async Task<GeneralResponse<bool>> DeleteProjectDailyReport([FromRoute] Guid reportId)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await projectService.DeleteProjectDailyReport(reportId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get projects managed by the current user
    /// </summary>
    /// <returns>List of projects managed by current user</returns>
    [HttpGet("managed-project")]
    public async Task<GeneralResponse<SimpleProjectsResponseDto>> GetManagedProject()
    {
        try
        {
            var result = await projectService.GetProjectByManager();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleProjectsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get simplified project information with optional pagination
    /// </summary>
    /// <param name="pageNum">Page number for pagination</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <returns>Simplified project information</returns>
    [HttpGet("simple-project-info")]
    public async Task<GeneralResponse<SimpleProjectsResponseDto>> GetSimpleProjectInfo([FromQuery] int? pageNum, [FromQuery] int? pageSize)
    {
        try
        {
            var result = await projectService.GetSimpleProjectInfo(pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleProjectsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new project
    /// </summary>
    /// <param name="requestDto">Project creation data</param>
    /// <returns>Created project details</returns>
    [HttpPost]
    public async Task<GeneralResponse<ProjectResponseDto>> CreateProject([FromBody] CreateProjectRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectResponseDto>();

            var result = await projectService.CreateProject(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing project
    /// </summary>
    /// <param name="id">Project ID to update</param>
    /// <param name="requestDto">Updated project data</param>
    /// <returns>Updated project details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<ProjectResponseDto>> UpdateProject([FromRoute] Guid id, [FromBody] UpdateProjectRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectResponseDto>();

            var result = await projectService.UpdateProject(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a project
    /// </summary>
    /// <param name="id">Project ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteProject([FromRoute] Guid id)
    {
        try
        {
            var result = await projectService.DeleteProject(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a project
    /// </summary>
    /// <param name="id">Project ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetProjectLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Project>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}