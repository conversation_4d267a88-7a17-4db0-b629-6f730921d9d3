using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Leave.Request;
using Kantoku.Api.Dtos.Leave.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IEmployeeLeaveService
{
    Task<ResultDto<EmployeeLeavesResponseDto>> GetEmployeeLeaveByUser(Guid employeeId, DateOnly dateFrom, DateOnly dateTo);
    Task<ResultDto<LeavesResponseDto>> GetEmployeeLeaveByOrg(ApiFilter. EmployeeLeaveFilter filter);
    Task<ResultDto<UsedLeavesResponseDto>> GetLeaveUsage(Guid employeeId, DateOnly dateFrom, DateOnly dateTo);
    Task<ResultDto<LeaveResponseDto>> CreateEmployeeLeave(Guid employeeId, float baseLeave, DateOnly baseLeaveExpire);
    Task<ResultDto<LeaveResponseDto>> UpdateEmployeeLeave(Guid employeeLeaveId, UpdateEmployeeLeaveRequestDto dto);
    Task<ResultDto<bool>> DeleteEmployeeLeave(Guid employeeLeaveId);
}

[Service(ServiceLifetime.Scoped)]
public class EmployeeLeaveService : BaseService<EmployeeLeaveService>, IEmployeeLeaveService
{
    private readonly IEmployeeLeaveRepository employeeLeaveRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IFilterMapper<ApiFilter.EmployeeLeaveFilter, DomainFilter.EmployeeLeaveFilter> filterMapper;
    private readonly EmployeeLeaveQueryableOptions options = new()
    {
        IncludedEmployee = true,
    };
    public EmployeeLeaveService(
        IEmployeeLeaveRepository employeeLeaveRepository,
        IRequestRepository requestRepository,
        IEmployeeRepository employeeRepository,
        IFilterMapper<ApiFilter.EmployeeLeaveFilter, DomainFilter.EmployeeLeaveFilter> filterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.employeeLeaveRepository = employeeLeaveRepository;
        this.requestRepository = requestRepository;
        this.employeeRepository = employeeRepository;
        this.filterMapper = filterMapper;
    }

    public async Task<ResultDto<EmployeeLeavesResponseDto>> GetEmployeeLeaveByUser(Guid employeeId, DateOnly dateFrom, DateOnly dateTo)
    {
        var employee = await employeeRepository.GetById(employeeId, new EmployeeQueryableOptions());
        if (employee is null)
        {
            logger.Error("Employee not found for employee {EmployeeId}", employeeId);
            return new ErrorResultDto<EmployeeLeavesResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }

        var leaves = await employeeLeaveRepository.GetByEmployeeIdAndDateRange(
            employeeId, dateFrom, dateTo, options);
        var result = employee.ToEmployeeLeavesResponseDto(leaves);
        return new SuccessResultDto<EmployeeLeavesResponseDto>(result);
    }

    public async Task<ResultDto<LeavesResponseDto>> GetEmployeeLeaveByOrg(ApiFilter.EmployeeLeaveFilter filter)
    {
        var domainFilter = filterMapper.MapToDomain(filter);
        var (leaves, total) = await employeeLeaveRepository.GetByFilter(domainFilter, options);
        if (leaves is null || !leaves.Any() || total == 0)
        {
            logger.Error("Employee leave not found");
            return new ErrorResultDto<LeavesResponseDto>(ResponseCodeConstant.EMPLOYEE_LEAVE_NOT_EXIST);
        }
        var result = leaves.LeavesResponseDtoParser(total, filter.PageNum, filter.PageSize);
        return new SuccessResultDto<LeavesResponseDto>(result);
    }

    public async Task<ResultDto<UsedLeavesResponseDto>> GetLeaveUsage(Guid employeeId, DateOnly dateFrom, DateOnly dateTo)
    {
        var options = new RequestQueryableOptions();
        var (leaveRequests, _) = await requestRepository.GetByAuthor(employeeId, new DomainFilter.RequestFilter
        {
            FromDate = dateFrom.ToDateTime(TimeOnly.MinValue).ToString("yyyy-MM-dd HH:mm:ss"),
            ToDate = dateTo.ToDateTime(TimeOnly.MinValue).ToString("yyyy-MM-dd HH:mm:ss"),
            StatusCode = StatusConstants.APPROVED,
            RequestTypeCode = Domain.Models.RequestTypeConstants.LEAVE,
        }, options);
        var items = leaveRequests.Select(r => new UsedLeaveResponseDto
        {
            RequestFrom = r.RequestFrom,
            RequestTo = r.RequestTo,
            IsPersonalUse = r.IsUserRequestedLeave == true,
        });
        var result = new UsedLeavesResponseDto
        {
            Items = items,
        };
        return new SuccessResultDto<UsedLeavesResponseDto>(result);
    }

    public async Task<ResultDto<LeaveResponseDto>> CreateEmployeeLeave(Guid employeeId, float baseLeave, DateOnly baseLeaveExpire)
    {
        var options = new EmployeeLeaveQueryableOptions
        {
            IncludedEmployee = true,
        };
        var currentLeave = await employeeLeaveRepository.GetCurrentByEmployeeId(employeeId, options);

        if (currentLeave is not null && currentLeave.BaseLeaveExpire >= DateOnly.FromDateTime(DateTime.Now))
        {
            logger.Warning("Employee {EmployeeId} already has a leave", employeeId);
            return new ErrorResultDto<LeaveResponseDto>(ResponseCodeConstant.EMPLOYEE_LEAVE_ALREADY_EXIST);
        }

        var newLeave = new EmployeeLeave
        {
            EmployeeUid = employeeId,
            BaseLeave = baseLeave,
            BaseLeaveExpire = baseLeaveExpire,
        };

        if (currentLeave is not null)
        {
            var lastUsableLeave = currentLeave.BaseLeave + currentLeave.LastRemainLeave - currentLeave.OrgTakenLeave - currentLeave.SelfTakenLeave;
            newLeave.LastRemainLeave = lastUsableLeave;
            newLeave.LastRemainLeaveExpire = currentLeave.BaseLeaveExpire;
        }

        var createdLeave = await employeeLeaveRepository.Create(newLeave);
        if (createdLeave is null)
        {
            logger.Error("Failed to create employee leave for employee {EmployeeId}", employeeId);
            return new ErrorResultDto<LeaveResponseDto>(ResponseCodeConstant.INITIAL_EMPLOYEE_LEAVE_FAILED);
        }

        var result = createdLeave.ToLeaveResponseDto();
        return new SuccessResultDto<LeaveResponseDto>(result);
    }

    public async Task<ResultDto<LeaveResponseDto>> UpdateEmployeeLeave(Guid employeeLeaveId, UpdateEmployeeLeaveRequestDto dto)
    {
        var options = new EmployeeLeaveQueryableOptions
        {
            IncludedEmployee = true,
        };
        var employeeLeave = await employeeLeaveRepository.GetById(employeeLeaveId, options);
        if (employeeLeave is null)
        {
            logger.Warning("Employee leave not found for employee {EmployeeId}", employeeLeaveId);
            return new ErrorResultDto<LeaveResponseDto>(ResponseCodeConstant.EMPLOYEE_LEAVE_NOT_EXIST);
        }

        employeeLeave.UpdateFromDto(dto);

        var updatedLeave = await employeeLeaveRepository.Update(employeeLeave);
        if (updatedLeave is null)
        {
            logger.Error("Failed to update employee leave for employee {EmployeeId}", employeeLeaveId);
            return new ErrorResultDto<LeaveResponseDto>(ResponseCodeConstant.UPDATE_EMPLOYEE_LEAVE_FAILED);
        }

        var result = updatedLeave.ToLeaveResponseDto();
        return new SuccessResultDto<LeaveResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteEmployeeLeave(Guid employeeLeaveId)
    {
        var options = new EmployeeLeaveQueryableOptions
        {
            IncludedEmployee = true,
        };
        var employeeLeave = await employeeLeaveRepository.GetCurrentByEmployeeId(employeeLeaveId, options);
        if (employeeLeave is null)
        {
            logger.Warning("Employee leave not found for employee {EmployeeId}", employeeLeaveId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_LEAVE_NOT_EXIST);
        }
        employeeLeave.IsDeleted = true;
        var deleted = await employeeLeaveRepository.Update(employeeLeave);
        if (deleted is null)
        {
            logger.Error("Failed to delete employee leave for employee {EmployeeId}", employeeLeaveId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.DELETE_EMPLOYEE_LEAVE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
