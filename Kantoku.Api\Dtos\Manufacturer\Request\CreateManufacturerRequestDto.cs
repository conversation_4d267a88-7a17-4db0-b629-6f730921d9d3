﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Domain.Models;

namespace Kantoku.Api.Dtos.Manufacturer.Request;

/// <summary>
/// Data transfer object for creating a new manufacturer
/// </summary>
public class CreateManufacturerRequestDto
{
    /// <summary>
    /// The code of the manufacturer (*)
    /// </summary>
    [Required]
    public string ManufacturerCode { get; set; } = null!;

    /// <summary>
    /// The name of the manufacturer (*)
    /// </summary>
    [Required]
    public string ManufacturerName { get; set; } = null!;

    /// <summary>
    /// The name of the manufacturer in Japanese
    /// </summary>
    public string? ManufacturerSubName { get; set; }

    /// <summary>
    /// The description of the manufacturer
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The corporate number of the manufacturer
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the manufacturer
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the manufacturer
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the manufacturer
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the manufacturer
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The logo of the manufacturer
    /// </summary>
    public IFormFile? Logo { get; set; }
}
