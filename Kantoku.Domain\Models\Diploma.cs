﻿// using System.ComponentModel.DataAnnotations.Schema;

// namespace Kantoku.Persistence.Models
// {
//     public class Diploma : AuditableEntity
//     {
//         [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
//         public int DiplomaId { get; set; }

//         public Guid DiplomaUid { get; set; }

//         public Guid AccountUid { get; set; }

//         // Essential certification details
//         public string CertificationName { get; set; } = null!;
//         public string IssuingAuthority { get; set; } = null!;
//         public string LicenseNumber { get; set; } = null!;

//         // Dates
//         public DateOnly IssueDate { get; set; }
//         public DateOnly? ExpiryDate { get; set; }
//         public DateOnly? LastRenewalDate { get; set; }

//         // Certification specifics
//         public string? CertificationLevel { get; set; }
//         public string? SpecializedField { get; set; }
//         public bool RequiresRenewal { get; set; }
//         public int? RenewalPeriodMonths { get; set; }

//         // Verification
//         public DiplomaStatus Status { get; set; }
//         public string? CertificateUrl { get; set; }            // Scanned copy or digital certificate

//         // Navigation property
//         public virtual Account Account { get; set; } = null!;
//     }

//     public enum DiplomaStatus
//     {
//         Pending,
//         Verified,
//         Rejected,
//         Expired,
//         PendingRenewal
//     }
// }
