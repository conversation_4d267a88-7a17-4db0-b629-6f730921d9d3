using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IUserInfoRepository
{
    Task<UserInfo?> GetByAccountId(Guid accountId, UserInfoQueryableOptions options);
    Task<UserInfo?> GetById(Guid userInfoId, UserInfoQueryableOptions options);
    Task<UserInfo?> Create(UserInfo user);
    Task<UserInfo?> Update(UserInfo user);
}

public class UserInfoRepository : BaseRepository<UserInfo>, IUserInfoRepository
{
    private readonly IUserInfoQueryable userInfoQueryable;
    public UserInfoRepository(
        ApplicationDbContext context,
        IUserInfoQueryable userInfoQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
    : base(context, logger, tenantContext)
    {
        this.userInfoQueryable = userInfoQueryable;
    }

    public async Task<UserInfo?> GetById(Guid userInfoId, UserInfoQueryableOptions options)
    {
        try
        {
            var query = userInfoQueryable.GetUserInfoQueryIncluded(options)
                .Where(u => u.UserInfoUid == userInfoId);

            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting user by id: {Id}", userInfoId);
            return null;
        }
    }

    public async Task<UserInfo?> GetByAccountId(Guid accountId, UserInfoQueryableOptions options)
    {
        try
        {
            var query = userInfoQueryable.GetUserInfoQueryIncluded(options)
                .Where(u => u.AccountUid == accountId);

            var result = await query.FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting user by account id: {Id}", accountId);
            return null;
        }
    }

    public async Task<UserInfo?> Create(UserInfo user)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.UserInfos.AddAsync(user);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(user.UserInfoUid, new UserInfoQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating user: {User}", user.UserInfoUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<UserInfo?> Update(UserInfo user)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.UserInfos.Update(user);
            var entry = context.Entry(user);
            if (entry.Property(u => u.Name).IsModified)
            {
                var employees = await context.Employees
                    .Where(e => e.AccountUid == user.AccountUid)
                    .ToListAsync();
                foreach (var employee in employees)
                {
                    employee.EmployeeName = user.Name;
                    context.Entry(employee).Property(e => e.EmployeeName).IsModified = true;
                }
            }
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(user.UserInfoUid, new UserInfoQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating user: {User}", user.UserInfoUid);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
