namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for project daily report queries - contains only business logic, no HTTP concerns
/// </summary>
public class ProjectDailyReportFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in report content
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by project ID
    /// </summary>
    public Guid? ProjectId { get; set; }

    /// <summary>
    /// Filter by construction ID
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// Filter reports from this date (yyyy-MM-dd format)
    /// </summary>
    public string? ReportFrom { get; set; }

    /// <summary>
    /// Filter reports to this date (yyyy-MM-dd format)
    /// </summary>
    public string? ReportTo { get; set; }

    /// <summary>
    /// Filter by specific report date (yyyy-MM-dd format)
    /// </summary>
    public string? ReportDate { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Validate date formats
        ReportFrom = ValidateDate(ReportFrom);
        ReportTo = ValidateDate(ReportTo);
        ReportDate = ValidateDate(ReportDate);
    }

    private static string? ValidateDate(string? dateString)
    {
        if (!string.IsNullOrWhiteSpace(dateString) && !DateTime.TryParse(dateString, out _))
        {
            return null; // Invalid date, ignore
        }
        return dateString;
    }
}
