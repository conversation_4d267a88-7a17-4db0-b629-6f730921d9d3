using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class OutSourcePriceConfiguration(string schema) : IEntityTypeConfiguration<OutSourcePrice>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<OutSourcePrice> builder)
    {
        builder.HasKey(e => e.OutSourcePriceUid).HasName("outsource_price_pkey");

        builder.ToTable("outsource_price", Schema);

        builder.Property(e => e.OutSourcePriceUid)
            .HasColumnName("outsource_price_uid");
        builder.Property(e => e.OutSourceUid)
            .HasColumnName("outsource_uid");
        builder.Property(e => e.PricePerDay)
            .HasColumnName("price_per_day");
        builder.Property(e => e.EffectiveDate)
            .HasColumnName("effective_date")
            .HasColumnType("date");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.OutSource).WithMany(p => p.OutSourcePrices)
            .HasForeignKey(d => d.OutSourceUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("outsource_price_outsource_id_fkey");
    }
}
