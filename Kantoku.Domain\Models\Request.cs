﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class Request : AuditableEntity
{
    public Guid RequestUid { get; set; }
    public Guid AuthorUid { get; set; }
    public Guid? ProjectUid { get; set; }

    [AuditProperty]
    public DateTime RequestFrom { get; set; }

    [AuditProperty]
    public DateTime RequestTo { get; set; }

    [AuditProperty]
    public string RequestTypeCode { get; set; } = null!;

    [AuditProperty]
    public bool? IsUserRequestedLeave { get; set; }
    [AuditProperty]
    public string? Description { get; set; }

    public string? StatusCode { get; set; }
    public bool IsDeleted { get; set; } = false;

    public Guid? Approver1Uid { get; set; }
    public bool? Approver1Status { get; set; }
    public string? Approver1Notes { get; set; }
    public DateTime? Approver1Time { get; set; }

    public Guid? Approver2Uid { get; set; }
    public bool? Approver2Status { get; set; }
    public string? Approver2Notes { get; set; }
    public DateTime? Approver2Time { get; set; }

    public virtual Employee? Approver1 { get; set; }

    public virtual Employee? Approver2 { get; set; }

    public virtual Employee Author { get; set; } = null!;

    public virtual Project Project { get; set; } = null!;

    public virtual RequestType RequestType { get; set; } = null!;

    public virtual Status Status { get; set; } = null!;
}

public class RequestStatusConstants
{
    public const string PENDING = nameof(PENDING);
    public const string APPROVED = nameof(APPROVED);
    public const string REJECTED = nameof(REJECTED);
    public const string CANCELLED = nameof(CANCELLED);
}

public class RequestTypeConstants
{
    public const string LEAVE = nameof(LEAVE);
    public const string WORK = nameof(WORK);
    public const string IN_OUT = nameof(IN_OUT);
    public const string OVERTIME = nameof(OVERTIME);
}

public class LeaveTypeConstants
{
    public const string UNPAID = nameof(UNPAID);
    public const string COMPENSATORY = nameof(COMPENSATORY);
    public const string OTHER = nameof(OTHER);
    public const string PAID_ORG = nameof(PAID_ORG);
    public const string PAID = nameof(PAID);
}

