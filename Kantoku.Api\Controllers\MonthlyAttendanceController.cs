using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.MonthlyAttReport;
using Kantoku.Api.Dtos.MonthlyAttReport.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class MonthlyAttendanceController : BaseController
{
    private readonly IMonthlyReportService monthlyReportService;
    private readonly IAuditLogService auditLogService;

    public MonthlyAttendanceController(
        ITResponseFactory responseFactory,
        IMonthlyReportService monthlyReportService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.monthlyReportService = monthlyReportService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated monthly attendance reports within a date range
    /// </summary>
    /// <param name="filter">Filter parameters for monthly attendance reports</param>
    /// <returns>Paginated list of monthly attendance reports</returns>
    [HttpGet]
    public async Task<GeneralResponse<MonthlyReportsResponseDto>> GetMonthlyAttendance([FromQuery] MonthlyReportFilter filter)
    {
        try
        {
            var res = await monthlyReportService.GetMonthlyAttendance(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<MonthlyReportsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get detailed monthly attendance report for a specific employee
    /// </summary>
    /// <param name="employeeId">Employee ID to get attendance details for</param>
    /// <param name="filter">Filter parameters for monthly attendance reports</param>
    /// <returns>Detailed monthly attendance report</returns>
    [HttpGet("{employeeId}")]
    public async Task<GeneralResponse<EmployeeMonthlyDetailsResponseDto>> GetMonthlyAttendanceDetails(
        [FromRoute] Guid employeeId, [FromQuery] MonthlyReportFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Fail<EmployeeMonthlyDetailsResponseDto>(ResponseCodeConstant.BAD_REQUEST);
            }
            var res = await monthlyReportService.GetMonthlyAttendanceDetails(employeeId, filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeMonthlyDetailsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update a monthly attendance report
    /// </summary>
    /// <param name="reportId">ID of the report to update</param>
    /// <param name="dto">Updated report data</param>
    /// <returns>Success response if update was successful</returns>
    [HttpPut("{reportId}")]
    public async Task<GeneralResponse<MonthlyReportResponseDto>> UpdateMonthlyAttendance([FromRoute] Guid reportId, [FromBody] UpdateMonthlyReportRequestDto dto)
    {
        try
        {
            var result = await monthlyReportService.UpdateMonthlyReport(reportId, dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<MonthlyReportResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Request approval for a monthly attendance report
    /// </summary>
    /// <param name="dto">Request approval data</param>
    /// <returns>Success response if request approval was successful</returns>
    [HttpPut("approval")]
    public async Task<GeneralResponse<MonthlyReportResponseDto>> RequestApproval([FromBody] RequestApprovalRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid || !DateOnly.TryParse(dto.ReportFrom, out var _) || !DateOnly.TryParse(dto.ReportTo, out var _))
            {
                return Fail<MonthlyReportResponseDto>(ResponseCodeConstant.BAD_REQUEST);
            }
            var result = await monthlyReportService.RequestApproval(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<MonthlyReportResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a monthly attendance report
    /// </summary>
    /// <param name="id">Report ID to get logs for</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetMonthlyAttendanceLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<MonthlyReport>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
