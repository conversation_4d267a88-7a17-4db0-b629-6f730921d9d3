using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Kantoku.Api.Configurations;
using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Auth;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Kantoku.Api.Services;

public interface ITokenService
{
    public string? GenerateAccountAccessToken(Account account);
    public string? GenerateAccountRefreshToken(Account account);
    public string? GenerateOrgAccessToken(Employee employee);
    public string? GenerateOrgRefreshToken(Employee employee);
    public bool ValidateAccessToken(string token, out AuthenticatedUser? authenticatedUser);
}

[Service(ServiceLifetime.Scoped)]
public class TokenService : BaseService<TokenService>, ITokenService
{
    private readonly AuthConfig authConfig;
    public TokenService(IOptions<AuthConfig> authConfig, IHttpContextAccessor httpContextAccessor, Serilog.ILogger logger)
    : base(logger, httpContextAccessor)
    {
        this.authConfig = authConfig.Value;
    }

    public string? GenerateAccountAccessToken(Account account)
    {
        try
        {
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();

            var signingCredentials = GetSigningCredentials();
            var claims = GenerateAccountClaims(account);

            var Issuer = authConfig.Issuer;
            var Audience = authConfig.Audience;
            var TokenExpires = authConfig.AccessTokenExpires;

            var token = new JwtSecurityToken(
                issuer: Issuer,
                audience: Audience,
                expires: new DateTimeOffset(DateTime.Now).DateTime.AddDays(TokenExpires),
                claims: claims,
                notBefore: new DateTimeOffset(DateTime.Now).DateTime,
                signingCredentials: signingCredentials
            );
            return jwtSecurityTokenHandler.WriteToken(token);
        }
        catch (Exception)
        {
            return null;
        }
    }

    public string? GenerateAccountRefreshToken(Account account)
    {
        try
        {
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();

            var signingCredentials = GetSigningCredentials();
            var claims = GenerateAccountClaims(account);

            var Issuer = authConfig.Issuer;
            var Audience = authConfig.Audience;
            var TokenExpires = authConfig.RefreshTokenExpires;

            var token = new JwtSecurityToken(
                issuer: Issuer,
                audience: Audience,
                expires: new DateTimeOffset(DateTime.Now).DateTime.AddDays(TokenExpires),
                claims: claims,
                notBefore: new DateTimeOffset(DateTime.Now).DateTime,
                signingCredentials: signingCredentials
            );
            return jwtSecurityTokenHandler.WriteToken(token);
        }
        catch (Exception)
        {
            return null;
        }
    }

    public string? GenerateOrgAccessToken(Employee employee)
    {
        try
        {
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();

            var signingCredentials = GetSigningCredentials();
            var claims = GenerateOrgClaims(employee);

            var Issuer = authConfig.Issuer;
            var Audience = authConfig.Audience;
            var TokenExpires = authConfig.AccessTokenExpires;

            var token = new JwtSecurityToken(
                issuer: Issuer,
                audience: Audience,
                expires: new DateTimeOffset(DateTime.Now).DateTime.AddDays(TokenExpires),
                claims: claims,
                notBefore: new DateTimeOffset(DateTime.Now).DateTime,
                signingCredentials: signingCredentials
            );
            return jwtSecurityTokenHandler.WriteToken(token);
        }
        catch (Exception)
        {
            return null;
        }
    }

    public string? GenerateOrgRefreshToken(Employee employee)
    {
        try
        {
            logger.Information("Generate org refresh token for employee {EmployeeUid}", employee.EmployeeUid);
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
            var signingCredentials = GetSigningCredentials();
            var claims = GenerateOrgClaims(employee);
            var Issuer = authConfig.Issuer;
            var Audience = authConfig.Audience;
            var TokenExpires = authConfig.RefreshTokenExpires;

            var token = new JwtSecurityToken(
                issuer: Issuer,
                audience: Audience,
                expires: new DateTimeOffset(DateTime.Now).DateTime.AddDays(TokenExpires),
                claims: claims,
                notBefore: new DateTimeOffset(DateTime.Now).DateTime,
                signingCredentials: signingCredentials
            );
            return jwtSecurityTokenHandler.WriteToken(token);
        }
        catch (Exception e)
        {
            logger.Error(e, "Generate org refresh token failed {e.Message}", e.Message);
            return null;
        }
    }

    private SigningCredentials? GetSigningCredentials()
    {
        try
        {
            var secret = Encoding.UTF8.GetBytes(authConfig.SecretKey);
            var secretKey = new SymmetricSecurityKey(secret);
            return new SigningCredentials(secretKey, SecurityAlgorithms.HmacSha512);
        }
        catch (Exception)
        {
            return null;
        }
    }

    private static Claim[] GenerateAccountClaims(Account account)
    {
        return
        [
            new Claim(ClaimConstant.ACCOUNT_UID, account.AccountUid.ToString()),
            new Claim(ClaimConstant.SCOPE, ScopeConstant.ACCOUNT),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        ];
    }

    private static Claim[] GenerateOrgClaims(Employee employee)
    {
        return
        [
            new Claim(ClaimConstant.ACCOUNT_UID, employee.AccountUid.ToString()),
            new Claim(ClaimConstant.ORG_UID, employee.OrgUid.ToString()),
            new Claim(ClaimConstant.EMPLOYEE_UID, employee.EmployeeUid.ToString()),
            new Claim(ClaimConstant.SCOPE, ScopeConstant.ORG),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        ];
    }


    private ClaimsPrincipal? GetPrincipal(string token)
    {
        var symmetricKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(authConfig.SecretKey));
        var Issuer = authConfig.Issuer;
        var Audience = authConfig.Audience;
        var tokenHandler = new JwtSecurityTokenHandler();

        try
        {
            var validationParameters = new TokenValidationParameters()
            {
                RequireExpirationTime = true,
                SaveSigninToken = true,
                ValidateActor = true,
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = Issuer,
                ValidAudience = Audience,
                IssuerSigningKey = symmetricKey,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken securityToken);

            if (securityToken is not JwtSecurityToken jwtSecurityToken || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha512, StringComparison.InvariantCultureIgnoreCase))
            {
                logger.Error("Invalid security token");
                throw new UnauthorizedException(ResponseCodeConstant.TOKEN_INVALID);
            }

            return principal;
        }
        catch (SecurityTokenExpiredException e)
        {
            logger.Error(e, "Security token expired");
            throw new UnauthorizedException(ResponseCodeConstant.TOKEN_EXPIRED);
        }
        catch (SecurityTokenException e)
        {
            logger.Error(e, "Security token validate failed");
            throw new UnauthorizedException(ResponseCodeConstant.TOKEN_VALIDATE_FAILED);
        }
        catch (Exception e)
        {
            logger.Error(e, "Security token validate failed");
            throw new UnauthorizedException(ResponseCodeConstant.TOKEN_VALIDATE_FAILED);
        }
    }

    public bool ValidateAccessToken(string token, out AuthenticatedUser? authenticatedUser)
    {
        try
        {
            var principal = GetPrincipal(token);
            if (principal?.Identity is not ClaimsIdentity identity)
            {
                logger.Error("Parse principal failed");
                authenticatedUser = null;
                return false;
            }

            if (!identity.IsAuthenticated)
            {
                logger.Error("Token is not authenticated");
                authenticatedUser = null;
                return false;
            }

            var claims = principal?.Identities.FirstOrDefault()?.Claims.ToList();
            if (claims is null || claims.Count == 0)
            {
                logger.Error("Claims is null or empty");
                authenticatedUser = null;
                return false;
            }
            var scope = claims.FirstOrDefault(x => x.Type == ClaimConstant.SCOPE)?.Value;
            if (scope is null)
            {
                logger.Error("Scope is null");
                authenticatedUser = null;
                return false;
            }
            if (scope.Equals(ScopeConstant.ACCOUNT))
            {
                authenticatedUser = new AuthenticatedUser
                {
                    Scope = scope
                };
                var accountUid = claims.FirstOrDefault(x => x.Type == ClaimConstant.ACCOUNT_UID)?.Value;
                if (accountUid is null)
                {
                    logger.Error("Account uid is null");
                    return false;
                }
                authenticatedUser.AccountUid = accountUid;
                return true;
            }
            if (scope.Equals(ScopeConstant.ORG))
            {
                authenticatedUser = new AuthenticatedUser
                {
                    Scope = scope
                };
                var accountUid = claims.FirstOrDefault(x => x.Type == ClaimConstant.ACCOUNT_UID)?.Value;
                if (accountUid is null)
                {
                    logger.Error("Account uid is null");
                    return false;
                }
                authenticatedUser.AccountUid = accountUid;
                var orgUid = claims.FirstOrDefault(x => x.Type == ClaimConstant.ORG_UID)?.Value;
                if (orgUid is null)
                {
                    logger.Error("Org uid is null");
                    return false;
                }
                authenticatedUser.OrgUid = orgUid;
                var employeeUid = claims.FirstOrDefault(x => x.Type == ClaimConstant.EMPLOYEE_UID)?.Value;
                if (employeeUid is null)
                {
                    logger.Error("Employee uid is null");
                    return false;
                }
                authenticatedUser.EmployeeUid = employeeUid;
                return true;
            }
            authenticatedUser = null;
            return false;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Validate token failed");
            authenticatedUser = null;
            return false;
        }
    }
}
