using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.EventCalendar.Request;
using Kantoku.Api.Dtos.EventCalendar.Response;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EventCalendarMapper
{
    #region "From Entity to Dto"
    /// <summary>
    /// Maps an EventCalendar entity to an EventCalendarDto
    /// </summary>
    /// <param name="entity">EventCalendar entity</param>
    /// <param name="currentDate">Current date</param>
    /// <returns>EventCalendarDto or null if entity is null</returns>
    private static EventCalendarDto ToEventCalendarDto(this EventCalendar entity, DateOnly currentDate)
    {
        if (entity == null || !entity.IsMatchRule(currentDate))
            return new EventCalendarDto();

        var res = new EventCalendarDto
        {
            EventId = entity.EventUid.ToString(),
            EventName = entity.EventName,
            ApplyDate = currentDate.ToString("yyyy-MM-dd"),
            DayOfWeek = currentDate.DayOfWeek.ToString(),
            WeekOfMonth = GetWeekNumberOfMonth(currentDate.ToDateTime(TimeOnly.MinValue), out var weekOfMonth)
                ? weekOfMonth.ToString()
                : null,
            EventStartTime = entity.EventStartTime?.ToString("HH:mm:ss"),
            EventEndTime = entity.EventEndTime?.ToString("HH:mm:ss"),
            Description = entity.Description,
            IsDayOff = entity.IsDayOff
        };
        return res;
    }

    /// <summary>
    /// Maps an EventCalendar entity to a collection of EventCalendarDtos
    /// </summary>
    /// <param name="entity">EventCalendar entity</param>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Collection of EventCalendarDtos</returns>
    public static IEnumerable<EventCalendarDto> ToEventCalendarDtos(
        this EventCalendar entity,
        DateOnly startDate,
        DateOnly endDate)
    {
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            yield return entity.ToEventCalendarDto(date);
        }
    }

    /// <summary>
    /// Maps a collection of EventCalendar entities to an EventCalendarsResponseDto
    /// </summary>
    /// <param name="entities">Collection of EventCalendar entities</param>
    /// <param name="dateFrom">Start date</param>
    /// <param name="dateTo">End date</param>
    /// <returns>EventCalendarsResponseDto with mapped EventCalendarDtos</returns>
    public static EventCalendarsResponseDto ToEventCalendarsResponseDto(
        this IEnumerable<EventCalendar> entities, DateOnly dateFrom, DateOnly dateTo)
    {
        var res = entities
            .SelectMany(e => e.ToEventCalendarDtos(dateFrom, dateTo))
            .Where(e => e.EventId is not null)
            .OrderBy(e => e.ApplyDate)
            .ToList();
        return new EventCalendarsResponseDto
        {
            EventCalendars = res
        };
    }

    /// <summary>
    /// Maps an EventCalendar entity to an EventCalendarRuleDto
    /// </summary>
    /// <param name="entity">EventCalendar entity</param>
    /// <returns>EventCalendarRuleDto or null if entity is null</returns>
    public static EventCalendarRuleDto ToEventCalendarRuleDto(this EventCalendar? entity)
    {
        if (entity == null) return new EventCalendarRuleDto();

        return new EventCalendarRuleDto
        {
            EventId = entity.EventUid.ToString(),
            EventName = entity.EventName,
            EventStartDate = entity.EventStartDate?.ToString("yyyy-MM-dd"),
            EventEndDate = entity.EventEndDate?.ToString("yyyy-MM-dd"),
            EventStartTime = entity.EventStartTime?.ToString("HH:mm:ss"),
            EventEndTime = entity.EventEndTime?.ToString("HH:mm:ss"),
            IsRecurring = entity.IsRecurring,
            RecurringType = entity.RecurringType,
            RecurringFrom = entity.RecurringFrom?.ToString("yyyy-MM-dd"),
            RecurringTo = entity.RecurringTo?.ToString("yyyy-MM-dd"),
            RecurringDay = entity.RecurringDay?.ToList() ?? [],
            RecurringWeek = entity.RecurringWeek?.ToList() ?? [],
            RecurringMonth = entity.RecurringMonth?.ToList() ?? [],
            Description = entity.Description,
            IsDayOff = entity.IsDayOff
        };
    }

    /// <summary>
    /// Maps a collection of EventCalendar entities to an EventCalendarRulesResponseDto
    /// </summary>
    /// <param name="entities">Collection of EventCalendar entities</param>
    /// <param name="pageNumber">Current page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="totalRecords">Total number of records</param>
    /// <returns>EventCalendarRulesResponseDto with mapped EventCalendarRuleDtos</returns>
    public static EventCalendarRulesResponseDto ToEventCalendarRulesResponseDto(
        this IEnumerable<EventCalendar> entities,
        int pageNumber,
        int pageSize,
        int totalRecords)
    {
        return new EventCalendarRulesResponseDto
        {
            EventCalendarRules = entities.Select(entity => entity.ToEventCalendarRuleDto()),
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }
    #endregion

    #region "From Dto to Entity"

    /// <summary>
    /// Converts a CreateEventCalendarRequestDto to an EventCalendar entity
    /// </summary>
    /// <param name="dto">CreateEventCalendarRequestDto</param>
    /// <param name="orgUid">Organization UID</param>
    /// <returns>New EventCalendar entity</returns>
    public static EventCalendar ToEntity(this CreateEventCalendarRequestDto dto, Guid orgUid)
    {
        var entity = new EventCalendar
        {
            EventUid = Guid.NewGuid(),
            EventName = dto.EventName,
            IsRecurring = dto.IsRecurring,
            Description = dto.Description,
            IsDayOff = dto.IsDayOff,
            OrgUid = orgUid,
            IsDeleted = false,
            EventStartDate = DateOnly.TryParse(dto.EventStartDate, out var eventStartDate)
                ? eventStartDate
                : null,
            EventEndDate = DateOnly.TryParse(dto.EventEndDate, out var eventEndDate)
                ? eventEndDate
                : null,
            EventStartTime = TimeOnly.TryParse(dto.EventStartTime, out var eventStartTime)
                ? eventStartTime
                : null,
            EventEndTime = TimeOnly.TryParse(dto.EventEndTime, out var eventEndTime)
                ? eventEndTime
                : null,
        };


        // Set recurring properties if event is recurring
        if (dto.IsRecurring)
        {
            entity.RecurringType = dto.RecurringType;
            entity.RecurringFrom = DateOnly.TryParse(dto.RecurringFrom, out var recurringFrom)
                ? recurringFrom
                : null;
            entity.RecurringTo = DateOnly.TryParse(dto.RecurringTo, out var recurringTo)
                ? recurringTo
                : null;
            entity.RecurringDay = dto.RecurringDay?.ToList();
            entity.RecurringWeek = dto.RecurringWeek?.ToList();
            entity.RecurringMonth = dto.RecurringMonth?.ToList();
        }

        return entity;
    }

    /// <summary>
    /// Updates an existing EventCalendar entity with data from an UpdateEventCalendarRequestDto
    /// </summary>
    /// <param name="entity">Existing EventCalendar entity to update</param>
    /// <param name="dto">UpdateEventCalendarRequestDto with new data</param>
    public static void UpdateFromDto(this EventCalendar entity, UpdateEventCalendarRequestDto dto)
    {
        // Update basic properties if provided
        if (!string.IsNullOrEmpty(dto.EventName))
        {
            entity.EventName = dto.EventName;
        }

        // Update description if provided
        if (dto.Description != null)
        {
            entity.Description = dto.Description;
        }

        // Update IsDayOff if provided
        if (dto.IsDayOff.HasValue)
        {
            entity.IsDayOff = dto.IsDayOff.Value;
        }

        // Update event dates if provided
        if (!string.IsNullOrEmpty(dto.EventStartDate))
        {
            entity.EventStartDate = DateOnly.Parse(dto.EventStartDate);
        }
        else if (dto.EventStartDate == string.Empty)
        {
            entity.EventStartDate = null;
        }

        if (!string.IsNullOrEmpty(dto.EventEndDate))
        {
            entity.EventEndDate = DateOnly.Parse(dto.EventEndDate);
        }
        else if (dto.EventEndDate == string.Empty)
        {
            entity.EventEndDate = null;
        }

        // Update event times if provided
        if (!string.IsNullOrEmpty(dto.EventStartTime))
        {
            entity.EventStartTime = TimeOnly.Parse(dto.EventStartTime);
        }
        else if (dto.EventStartTime == string.Empty)
        {
            entity.EventStartTime = null;
        }

        if (!string.IsNullOrEmpty(dto.EventEndTime))
        {
            entity.EventEndTime = TimeOnly.Parse(dto.EventEndTime);
        }
        else if (dto.EventEndTime == string.Empty)
        {
            entity.EventEndTime = null;
        }

        // Update recurring status
        entity.IsRecurring = dto.IsRecurring;

        // Update recurring properties if is recurring
        if (entity.IsRecurring)
        {
            entity.RecurringType = dto.RecurringType;

            if (!string.IsNullOrEmpty(dto.RecurringFrom))
            {
                entity.RecurringFrom = DateOnly.Parse(dto.RecurringFrom);
            }
            else if (dto.RecurringFrom == string.Empty)
            {
                entity.RecurringFrom = null;
            }

            if (!string.IsNullOrEmpty(dto.RecurringTo))
            {
                entity.RecurringTo = DateOnly.Parse(dto.RecurringTo);
            }
            else if (dto.RecurringTo == string.Empty)
            {
                entity.RecurringTo = null;
            }

            entity.RecurringDay = dto.RecurringDay?.ToList();
            entity.RecurringWeek = dto.RecurringWeek?.ToList();
            entity.RecurringMonth = dto.RecurringMonth?.ToList();
        }
        else
        {
            // Clear recurring properties if not recurring
            entity.RecurringType = null;
            entity.RecurringFrom = null;
            entity.RecurringTo = null;
            entity.RecurringDay = null;
            entity.RecurringWeek = null;
            entity.RecurringMonth = null;
        }
    }
    #endregion

    #region "Helper methods"

    /// <summary>
    /// Checks if a date matches a recurring day pattern
    /// </summary>
    /// <param name="date">Date to check</param>
    /// <param name="rule">EventCalendar entity</param>
    /// <returns>True if date matches recurring day pattern, false otherwise</returns>
    private static bool IsMatchDate(DateOnly date, EventCalendar rule)
    {
        var dayOfWeek = date.DayOfWeek == DayOfWeek.Sunday ? 7 : (int)date.DayOfWeek;

        if (rule.RecurringDay is null)
            return false;

        return rule.RecurringDay.Contains(dayOfWeek);
    }

    /// <summary>
    /// Checks if a date matches a recurring week pattern
    /// </summary>
    /// <param name="date">Date to check</param>
    /// <param name="rule">EventCalendar entity</param>
    /// <returns>True if date matches recurring week pattern, false otherwise</returns>
    private static bool IsMatchWeek(DateOnly date, EventCalendar rule)
    {
        if (rule.RecurringWeek is null)
            return false;

        if (!GetWeekNumberOfMonth(date.ToDateTime(TimeOnly.MinValue), out var weekOfMonth))
            return false;

        return rule.RecurringWeek.Contains(weekOfMonth);
    }

    /// <summary>
    /// Gets the week number of a date
    /// </summary>
    /// <param name="date">Date to check</param>
    /// <param name="weekOfMonth">Week number of the date</param>
    /// <returns>True if week number is valid, false otherwise</returns>
    private static bool GetWeekNumberOfMonth(DateTime date, out int weekOfMonth)
    {
        try
        {
            date = date.Date;
            DateTime firstMonthDay = new(date.Year, date.Month, 1);
            DateTime firstMonthMonday = firstMonthDay.AddDays((DayOfWeek.Monday + 7 - firstMonthDay.DayOfWeek) % 7);
            if (firstMonthMonday > date)
            {
                firstMonthDay = firstMonthDay.AddMonths(-1);
                firstMonthMonday = firstMonthDay.AddDays((DayOfWeek.Monday + 7 - firstMonthDay.DayOfWeek) % 7);
            }
            weekOfMonth = (date - firstMonthMonday).Days / 7 + 1;
            return true;
        }
        catch (System.Exception)
        {
            weekOfMonth = 0;
            return false;
        }
    }

    /// <summary>
    /// Checks if a date matches a recurring month pattern
    /// </summary>
    /// <param name="date">Date to check</param>
    /// <param name="rule">EventCalendar entity</param>
    /// <returns>True if date matches recurring month pattern, false otherwise</returns>
    private static bool IsMatchMonth(DateOnly date, EventCalendar rule)
    {
        if (rule.RecurringMonth is null)
            return false;

        return rule.RecurringMonth.Contains(date.Month);
    }

    /// <summary>
    /// Checks if a date matches a recurring rule
    /// </summary>
    /// <param name="entity">EventCalendar entity</param>
    /// <param name="dateToCheck">Date to check</param>
    /// <returns>True if date matches recurring rule, false otherwise</returns>
    private static bool IsMatchRule(this EventCalendar entity, DateOnly dateToCheck)
    {
        if (entity.IsRecurring == false)
        {
            return entity.EventStartDate <= dateToCheck && entity.EventEndDate >= dateToCheck;
        }

        // Check if date is within the recurring range
        if (entity.RecurringFrom.HasValue && dateToCheck < entity.RecurringFrom.Value)
            return false;
        if (entity.RecurringTo.HasValue && dateToCheck > entity.RecurringTo.Value)
            return false;

        bool isMatchRule = true;

        // Handle daily events
        if (entity.RecurringType == EventCalendarRecurringType.DAILY)
        {
            isMatchRule = true;
        }

        // Check day of week pattern if specified
        if (entity.RecurringDay?.Count > 0)
        {
            isMatchRule &= IsMatchDate(dateToCheck, entity);
        }

        // Check week of month pattern if specified
        if (entity.RecurringWeek?.Count > 0)
        {
            isMatchRule &= IsMatchWeek(dateToCheck, entity);
        }

        // Check month pattern if specified
        if (entity.RecurringMonth?.Count > 0)
        {
            isMatchRule &= IsMatchMonth(dateToCheck, entity);
        }

        return isMatchRule;
    }


    #endregion
}