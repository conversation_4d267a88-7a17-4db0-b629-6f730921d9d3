namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for project queries - contains only business logic, no HTTP concerns
/// </summary>
public class ProjectFilter : BaseFilter
{
    /// <summary>
    /// Filter by project type ID
    /// </summary>
    public Guid? TypeId { get; set; }

    /// <summary>
    /// Filter by project status code
    /// </summary>
    public string? StatusCode { get; set; }

    /// <summary>
    /// Filter by project code
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// Filter by project name
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// Keyword to search in project code, name, or description
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Expected start date (yyyy-MM-dd format)
    /// </summary>
    public string? ExStartDate { get; set; }

    /// <summary>
    /// Expected end date (yyyy-MM-dd format)
    /// </summary>
    public string? ExEndDate { get; set; }

    /// <summary>
    /// Actual start date (yyyy-MM-dd format)
    /// </summary>
    public string? ActStartDate { get; set; }

    /// <summary>
    /// Actual end date (yyyy-MM-dd format)
    /// </summary>
    public string? ActEndDate { get; set; }

    /// <summary>
    /// Minimum budget amount
    /// </summary>
    public float? BudgetMin { get; set; }

    /// <summary>
    /// Maximum budget amount
    /// </summary>
    public float? BudgetMax { get; set; }

    /// <summary>
    /// Minimum cost amount
    /// </summary>
    public float? CostMin { get; set; }

    /// <summary>
    /// Maximum cost amount
    /// </summary>
    public float? CostMax { get; set; }

    /// <summary>
    /// Filter by contractor ID
    /// </summary>
    public Guid? ContractorUid { get; set; }

    /// <summary>
    /// Filter by customer ID
    /// </summary>
    public Guid? CustomerUid { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize string fields
        if (!string.IsNullOrWhiteSpace(StatusCode))
        {
            StatusCode = StatusCode.ToUpperInvariant().Trim();
        }

        if (!string.IsNullOrWhiteSpace(ProjectCode))
        {
            ProjectCode = ProjectCode.Trim();
        }

        if (!string.IsNullOrWhiteSpace(ProjectName))
        {
            ProjectName = ProjectName.Trim();
        }

        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Validate date formats
        ExStartDate = ValidateDate(ExStartDate);
        ExEndDate = ValidateDate(ExEndDate);
        ActStartDate = ValidateDate(ActStartDate);
        ActEndDate = ValidateDate(ActEndDate);

        // Validate numeric ranges
        (BudgetMin, BudgetMax) = ValidateRange(BudgetMin, BudgetMax);
        (CostMin, CostMax) = ValidateRange(CostMin, CostMax);
    }

    private static string? ValidateDate(string? dateString)
    {
        if (!string.IsNullOrWhiteSpace(dateString) && !DateTime.TryParse(dateString, out _))
        {
            return null; // Invalid date, ignore
        }
        return dateString;
    }

    private static (float?, float?) ValidateRange(float? min, float? max)
    {
        if (min.HasValue && max.HasValue && min.Value > max.Value)
        {
            // Swap if min > max
            (min, max) = (max, min);
        }

        // Ensure non-negative values for budget and cost
        if (min.HasValue && min.Value < 0) min = 0;
        if (max.HasValue && max.Value < 0) max = 0;

        return (min, max);
    }
}
