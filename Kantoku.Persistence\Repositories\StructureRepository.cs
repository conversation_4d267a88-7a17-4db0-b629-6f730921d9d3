using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IStructureRepository
{
    Task<(IEnumerable<Structure>, int)> GetByFilter(DomainFilter.StructureFilter filter, StructureQueryableOptions options);
    Task<IEnumerable<Structure>> GetStructureWithRoles();
    Task<Structure?> GetById(Guid structureId, StructureQueryableOptions options);
    Task<Structure?> Create(Structure structure, StructureQueryableOptions options);
    Task<Structure?> Update(Structure structure, StructureQueryableOptions options);

    Task<Guid?> Create(Structure structure);
    Task<bool> Update(Structure structure);
}

public class StructureRepository : BaseRepository<Structure>, IStructureRepository
{
    private readonly IStructureQueryable structureQueryable;
    public StructureRepository(
        ApplicationDbContext context,
        IStructureQueryable structureQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.structureQueryable = structureQueryable;
    }

    public async Task<(IEnumerable<Structure>, int)> GetByFilter(DomainFilter.StructureFilter filter, StructureQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = structureQueryable.GetStructuresQueryFiltered(filter, options);
            var isOrgSuperUser = await IsOrgSuperUser();
            if (!isOrgSuperUser)
            {
                query = query.Where(st => st.IsHidden == false);
            }
            var result = await query
                .OrderBy(st => st.StructureCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all structures for organization {OrgId} with paging", filter.OrgId);
            return ([], 0);
        }
    }

    public async Task<Structure?> GetById(Guid structureId, StructureQueryableOptions options)
    {
        try
        {
            var query = structureQueryable.GetStructuresQueryIncluded(options)
                .Where(st => st.StructureUid == structureId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting structure by ID {Id}", structureId);
            return null;
        }
    }

    public async Task<IEnumerable<Structure>> GetStructureWithRoles()
    {
        try
        {
            var orgUid = GetCurrentOrgUid();
            var query = structureQueryable.GetStructuresQueryIncluded(new StructureQueryableOptions
            {
                IncludedRoles = true
            });
            var structures = await query
                .Where(s => s.OrgUid == orgUid && !s.IsDeleted && !s.IsHidden)
                .Include(s => s.Roles.Where(r => !r.IsDeleted && !r.IsHidden))
                .ToListAsync();

            return structures;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all structures");
            return [];
        }
    }

    public async Task<Structure?> Create(Structure structure, StructureQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Structures.AddAsync(structure);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(structure.StructureUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating structure");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(Structure structure)
    {
        await context.Structures.AddAsync(structure);
        var affectedRow = await context.SaveChangesAsync();
        return affectedRow > 0 ? structure.StructureUid : null;
    }


    public async Task<Structure?> Update(Structure structure, StructureQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Entry(structure).State = EntityState.Modified;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(structure.StructureUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating structure with ID {StructureUid}", structure.StructureUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Structure structure)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Entry(structure).State = EntityState.Modified;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating structure with ID {StructureUid}", structure.StructureUid);
            await transaction.RollbackAsync();
            return false;
        }
    }

        private async Task<bool> IsOrgSuperUser()
    {
        var accountUid = GetCurrentAccountUid();
        var orgUid = GetCurrentOrgUid();

        if (accountUid == Guid.Empty || orgUid == Guid.Empty)
        {
            return false;
        }

        var employee = await context.Employees
            .Include(e => e.EmployeeRoles)
            .ThenInclude(er => er.Role)
            .Where(e => e.OrgUid == orgUid && e.AccountUid == accountUid)
            .FirstOrDefaultAsync();

        if (employee is null)
        {
            return false;
        }

        return employee.IsOrgAdmin || employee.IsOrgOwner || employee.IsHidden ||
               employee.EmployeeRoles.Any(er => er.Role.IsHidden);
    } 
}
