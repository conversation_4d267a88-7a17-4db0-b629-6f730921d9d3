using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Contractor.Response;
using Kantoku.Api.Dtos.Project.Request;
using Kantoku.Api.Dtos.Project.Response;
using Kantoku.Api.Dtos.WorkShift.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ProjectMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Project entity to a ProjectResponseDto
    /// </summary>
    /// <param name="project">The Project entity to map</param>
    /// <param name="languageCode">The language code to use for the translation</param>
    /// <returns>The mapped ProjectResponseDto</returns>
    public static ProjectResponseDto ToProjectResponseDto(this Project project, string languageCode)
    {
        if (project == null)
            return new ProjectResponseDto();

        return new ProjectResponseDto
        {
            ProjectId = project.ProjectUid.ToString(),
            ProjectCode = project.ProjectCode,
            ProjectName = project.ProjectName,
            ProjectTypeId = project.ProjectType?.ProjectTypeUid.ToString(),
            ProjectTypeName = project.ProjectType?.TranslatedProjectType
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.ProjectTypeName,
            CustomerId = project.Customer?.CustomerUid.ToString(),
            CustomerName = project.Customer?.CustomerName,
            ContractorId = project.Contractor?.ContractorUid.ToString(),
            ContractorName = project.Contractor?.ContractorName,
            Address = project.Address,
            StatusCode = project.StatusCode,
            StatusName = project.Status.TranslatedStatus
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.StatusName,
            ExpectedStartDate = project.ExpectedStartDate?.ToString("yyyy-MM-dd"),
            ExpectedEndDate = project.ExpectedEndDate?.ToString("yyyy-MM-dd"),
            ActualStartDate = project.ActualStartDate?.ToString("yyyy-MM-dd"),
            ActualEndDate = project.ActualEndDate?.ToString("yyyy-MM-dd"),
            InitialBudget = project.InitialBudget,
            ActualBudget = project.ActualBudget,
            Description = project.Description,
            ManagersInfo = project.Managers.Select(m => new ProjectManagerResponseDto
            {
                ManagerId = m.Employee?.EmployeeUid.ToString(),
                ManagerName = m.Employee?.EmployeeName,
                IsPrimaryManager = m.IsPrimary,
            }),
            WorkShifts = project.ProjectWorkShifts.Select(pws => new WorkShiftResponseDto
            {
                WorkShiftId = pws.WorkShift?.WorkShiftUid.ToString(),
                WorkShiftName = pws.WorkShift?.WorkShiftName,
                CheckInTime = pws.WorkShift?.CheckInTime.ToString("HH:mm:ss"),
                CheckOutTime = pws.WorkShift?.CheckOutTime.ToString("HH:mm:ss"),
                Description = pws.WorkShift?.Description,
                WorkShiftBreaks = pws.WorkShift?.WorkShiftBreakTimes?.Select(wsb => new WorkShiftBreakResponseDto
                {
                    BreakInTime = wsb.BreakInTime.ToString("HH:mm:ss"),
                    BreakOutTime = wsb.BreakOutTime.ToString("HH:mm:ss")
                }) ?? [],
                TotalRequiredTime = pws.WorkShift?.TotalRequiredTime
            }),
            CreateTime = DateTimeHelper.ParseToLocalTime(project.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(project.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Maps a Project entity to a SimpleProjectResponseDto
    /// </summary>
    /// <param name="project">The Project entity to map</param>
    /// <returns>The mapped SimpleProjectResponseDto</returns>
    public static SimpleProjectResponseDto ToSimpleProjectResponseDto(this Project project)
    {
        if (project == null)
            return new SimpleProjectResponseDto();

        return new SimpleProjectResponseDto
        {
            ProjectId = project.ProjectUid.ToString(),
            ProjectCode = project.ProjectCode,
            ProjectName = project.ProjectName,
            Address = project.Address,
            Latitude = project.Latitude,
            Longitude = project.Longitude,
            IsDefault = project.IsDefault,
            IsOffice = project.IsOffice,
            Constructions = project.Constructions?.Select(c => c.ToConstructionBaseResponseDto()),
            ProjectManagers = project.Managers?.Select(m => m.Employee.ToBaseEmployeeResponseDto())
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to a ProjectsResponseDto
    /// </summary>
    /// <param name="projects">The collection of Project entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <param name="languageCode">The language code to use for the translation</param>
    /// <returns>The mapped ProjectsResponseDto</returns>
    public static ProjectsResponseDto ToProjectsResponseDto(
        this IEnumerable<Project> projects,
        int pageNum,
        int pageSize,
        int totalRecords,
        string languageCode)
    {
        if (projects == null)
            return new ProjectsResponseDto();

        return new ProjectsResponseDto
        {
            Items = projects.Select(p => p.ToProjectResponseDto(languageCode)),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to a SimpleProjectsResponseDto
    /// </summary>
    /// <param name="projects">The collection of Project entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns>The mapped SimpleProjectsResponseDto</returns>
    public static SimpleProjectsResponseDto ToSimpleProjectsResponseDto(
        this IEnumerable<Project> projects,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (projects == null)
            return new SimpleProjectsResponseDto();

        return new SimpleProjectsResponseDto
        {
            Items = projects.Select(p => p.ToSimpleProjectResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to a ContractedProjectResponseDto
    /// </summary>
    /// <param name="projects">The collection of Project entities to map</param>
    /// <returns>The mapped ContractedProjectResponseDto</returns>
    public static IEnumerable<ContractedProjectResponseDto> ToContractedProjectResponseDto(
        this IEnumerable<Project> projects)
    {
        if (projects == null)
            return [];

        return projects.Select(p => p.ToContractedProjectResponseDto());
    }

    /// <summary>
    /// Maps a Project entity to a ContractedProjectResponseDto
    /// </summary>
    /// <param name="project">The Project entity to map</param>
    /// <returns>The mapped ContractedProjectResponseDto</returns>
    public static ContractedProjectResponseDto ToContractedProjectResponseDto(
        this Project project)
    {
        if (project == null)
            return new ContractedProjectResponseDto();

        return new ContractedProjectResponseDto
        {
            ProjectId = project.ProjectUid.ToString(),
            ProjectCode = project.ProjectCode,
            ProjectName = project.ProjectName,
            Address = project.Address,
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to a ProjectDistanceResponseDto
    /// </summary>
    /// <param name="projects">The collection of Project entities to map</param>
    /// <param name="latitude">The latitude of the project location</param>
    /// <param name="longitude">The longitude of the project location</param>
    /// <returns>The mapped ProjectDistanceResponseDto</returns>
    public static ProjectDistanceResponseDto ToProjectDistanceResponseDto(
        this IEnumerable<Project> projects,
        double latitude,
        double longitude)
    {
        if (projects == null)
            return new ProjectDistanceResponseDto();

        var closestProject = projects.Select(p => p.ToProjectDistanceResponseDto(latitude, longitude))
            .Where(p => p != null)
            .OrderBy(p => p.Distance)
            .FirstOrDefault();

        return closestProject ?? new ProjectDistanceResponseDto();
    }

    /// <summary>
    /// Maps a Project entity to a ProjectDistanceResponseDto
    /// </summary>
    /// <param name="project">The Project entity to map</param>
    /// <param name="latitude">The latitude of the project location</param>
    /// <param name="longitude">The longitude of the project location</param>
    /// <returns>The mapped ProjectDistanceResponseDto</returns>
    public static ProjectDistanceResponseDto ToProjectDistanceResponseDto(
        this Project project,
        double latitude,
        double longitude)
    {
        if (project == null)
            return new ProjectDistanceResponseDto();

        if (project.Latitude == null || project.Longitude == null)
            return new ProjectDistanceResponseDto();

        return new ProjectDistanceResponseDto
        {
            ProjectId = project.ProjectUid,
            ProjectName = project.ProjectName,
            ProjectCode = project.ProjectCode,
            ProjectAddress = project.Address,
            Distance = CalculateDistance(latitude, longitude, project.Latitude.Value, project.Longitude.Value)
        };
    }
    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateProjectRequestDto to a Project entity
    /// </summary>
    /// <param name="dto">The CreateProjectRequestDto to map</param>
    /// <param name="orgUid">The organization ID</param>
    /// <param name="rankings">The rankings of the project</param>
    /// <returns>The mapped Project entity</returns>
    public static Project? ToEntity(
        this CreateProjectRequestDto dto,
        Guid orgUid,
        IEnumerable<Ranking> rankings)
    {
        if (dto == null)
            return null;

        var entity = new Project
        {
            ProjectUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            ProjectCode = dto.ProjectCode,
            ProjectName = dto.ProjectName,
            ProjectTypeUid = Guid.TryParse(dto.ProjectTypeId, out var projectTypeUid)
                ? projectTypeUid
                : null,
            CustomerUid = Guid.TryParse(dto.CustomerId, out var customerUid)
                ? customerUid
                : null,
            ContractorUid = Guid.TryParse(dto.ContractorId, out var contractorUid)
                ? contractorUid
                : null,
            Address = dto.Address,
            Latitude = dto.Latitude,
            Longitude = dto.Longitude,
            IsOffice = dto.IsOffice,
            IsDefault = false,
            IsDeleted = false,
            ExpectedStartDate = DateOnly.TryParse(dto.ExpectedStartDate, out var expectedStartDate)
                ? expectedStartDate
                : null,
            ExpectedEndDate = DateOnly.TryParse(dto.ExpectedEndDate, out var expectedEndDate)
                ? expectedEndDate
                : null,
            ActualStartDate = DateOnly.TryParse(dto.ActualStartDate, out var actualStartDate)
                ? actualStartDate
                : null,
            ActualEndDate = DateOnly.TryParse(dto.ActualEndDate, out var actualEndDate)
                ? actualEndDate
                : null,
            Description = dto.Description,
            StatusCode = dto.StatusCode,
            InitialBudget = dto.InitialBudget,
            ActualBudget = dto.ActualBudget,
            MonthlyReportDate = 1,
        };
        if (dto.WorkShiftIds != null)
        {
            entity.ProjectWorkShifts = dto.WorkShiftIds
            .Select(wsId => ToProjectWorkShiftEntity(wsId, entity.ProjectUid))
            .ToList();
        }
        else
        {
            entity.ProjectWorkShifts = dto.WorkShifts
            .Where(pws => Guid.TryParse(pws.WorkShiftId, out _))
            .Select(pws => ToProjectWorkShiftEntity(pws, entity.ProjectUid))
            .ToList();
        }
        if (dto.ProjectRankingCosts != null)
        {
            entity.ProjectRankingCosts = dto.ProjectRankingCosts.ToProjectRankingCostEntity(rankings, entity.ProjectUid).ToList();
        }
        var primaryManagers = dto.PrimaryManagerEmployeeIds.Select(mId => new ProjectManager
        {
            ProjectUid = entity.ProjectUid,
            EmployeeUid = Guid.Parse(mId),
            IsPrimary = true
        }).ToList();
        var subManagers = dto.SubManagerEmployeeIds?.Select(mId => new ProjectManager
        {
            ProjectUid = entity.ProjectUid,
            EmployeeUid = Guid.Parse(mId),
            IsPrimary = false
        }).ToList() ?? [];
        entity.Managers = primaryManagers.Concat(subManagers).ToList();
        entity.ProjectRankingCosts = rankings.Select(r => new ProjectRankingCost
        {
            ProjectUid = entity.ProjectUid,
            RankingUid = r.RankingUid,
            MinValue = r.MinValue ?? 0,
            MaxValue = r.MaxValue ?? 0
        }).ToList();
        var mainConstruction = new Construction
        {
            ConstructionUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = entity.ProjectUid,
            ConstructionName = entity.ProjectName,
            Description = entity.ProjectName,
            IsPrimary = true,
            IsDeleted = false,
            OrgUid = orgUid,
        };

        var subConstruction = new Construction
        {
            ConstructionUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = entity.ProjectUid,
            ConstructionName = entity.ProjectName,
            Description = entity.ProjectName,
            IsPrimary = false,
            IsDeleted = false,
            OrgUid = orgUid,
        };
        entity.Constructions = [mainConstruction, subConstruction];
        return entity;
    }

    /// <summary>
    /// Maps a ProjectWorkShiftRequestDto to a ProjectWorkShift entity
    /// </summary>
    /// <param name="dto">The ProjectWorkShiftRequestDto to map</param>
    /// <param name="projectUid">The project ID</param>
    /// <returns>The mapped ProjectWorkShift entity</returns>
    public static ProjectWorkShift ToProjectWorkShiftEntity(
        this ProjectWorkShiftRequestDto dto,
        Guid projectUid)
    {
        var entity = new ProjectWorkShift
        {
            ProjectUid = projectUid,
            WorkShiftUid = Guid.Parse(dto.WorkShiftId!),
            IsDefault = dto.IsDefault
        };

        return entity;
    }

    /// <summary>
    /// Maps a ProjectWorkShiftRequestDto to a ProjectWorkShift entity
    /// </summary>
    /// <param name="workShiftId">The work shift ID</param>
    /// <param name="projectUid">The project ID</param>
    /// <returns>The mapped ProjectWorkShift entity</returns>
    public static ProjectWorkShift ToProjectWorkShiftEntity(
        this string workShiftId,
        Guid projectUid)
    {
        var entity = new ProjectWorkShift
        {
            ProjectUid = projectUid,
            WorkShiftUid = Guid.Parse(workShiftId),
            IsDefault = false
        };

        return entity;
    }

    /// <summary>
    /// Maps a ProjectRankingCostRequestDto to a ProjectRankingCost entity
    /// </summary>
    /// <param name="dto">The ProjectRankingCostRequestDto to map</param>
    /// <param name="rankings">The rankings of the project</param>
    /// <param name="projectUid">The project ID</param>
    /// <returns>The mapped ProjectRankingCost entity</returns>
    public static ICollection<ProjectRankingCost> ToProjectRankingCostEntity(
        this IEnumerable<ProjectRankingCostRequestDto> dto,
        IEnumerable<Ranking> rankings,
        Guid projectUid)
    {
        if (dto == null || rankings == null)
        {
            return rankings?.Select(r => new ProjectRankingCost
            {
                ProjectUid = projectUid,
                RankingUid = r.RankingUid,
                MinValue = r.MinValue ?? 0,
                MaxValue = r.MaxValue ?? 0
            }).ToList() ?? [];
        }
        return dto.Select(r => new ProjectRankingCost
        {
            ProjectUid = projectUid,
            RankingUid = Guid.Parse(r.RankingUid!),
            MinValue = r.MinValue,
            MaxValue = r.MaxValue
        }).ToList() ?? [];
    }

    /// <summary>
    /// Maps a manager employee ID to a ProjectManager entity
    /// </summary>
    public static ProjectManager? ToProjectManagerEntity(
        string employeeId,
        Guid projectUid,
        bool isPrimary)
    {
        if (string.IsNullOrEmpty(employeeId))
            return null;

        var entity = new ProjectManager
        {
            ProjectUid = projectUid,
            EmployeeUid = Guid.Parse(employeeId),
            IsPrimary = isPrimary
        };

        return entity;
    }

    /// <summary>
    /// Updates a Project entity from an UpdateProjectRequestDto
    /// </summary>
    public static void UpdateFromDto(this Project entity, UpdateProjectRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.ProjectCode != null)
            entity.ProjectCode = dto.ProjectCode;

        if (dto.ProjectName != null)
            entity.ProjectName = dto.ProjectName;

        if (dto.ProjectTypeId != null)
            entity.ProjectTypeUid = Guid.Parse(dto.ProjectTypeId);

        if (dto.CustomerId != null)
            entity.CustomerUid = Guid.Parse(dto.CustomerId);

        if (dto.ContractorId != null)
            entity.ContractorUid = Guid.Parse(dto.ContractorId);

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.IsOffice.HasValue)
            entity.IsOffice = dto.IsOffice.Value;

        if (dto.ExpectedStartDate != null)
            entity.ExpectedStartDate = DateOnly.Parse(dto.ExpectedStartDate);

        if (dto.ExpectedEndDate != null)
            entity.ExpectedEndDate = DateOnly.Parse(dto.ExpectedEndDate);

        if (dto.ActualStartDate != null)
            entity.ActualStartDate = DateOnly.Parse(dto.ActualStartDate);

        if (dto.ActualEndDate != null)
            entity.ActualEndDate = DateOnly.Parse(dto.ActualEndDate);

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.StatusCode != null)
            entity.StatusCode = dto.StatusCode;

        if (dto.InitialBudget.HasValue)
            entity.InitialBudget = dto.InitialBudget;

        if (dto.ActualBudget.HasValue)
            entity.ActualBudget = dto.ActualBudget;

        if (dto.WorkShiftIds != null && dto.WorkShiftIds.Any())
        {
            entity.ProjectWorkShifts = dto.WorkShiftIds
            .Select(wsId => ToProjectWorkShiftEntity(wsId, entity.ProjectUid))
            .ToList();
        }
        else if (dto.WorkShifts != null && dto.WorkShifts.Any())
        {
            entity.ProjectWorkShifts = dto.WorkShifts
            .Where(pws => Guid.TryParse(pws.WorkShiftId, out _))
            .Select(pws => ToProjectWorkShiftEntity(pws, entity.ProjectUid))
            .ToList();
        }
        if (dto.ProjectRankingCosts != null && dto.ProjectRankingCosts.Any())
        {
            entity.ProjectRankingCosts = dto.ProjectRankingCosts
            .ToProjectRankingCostEntity([], entity.ProjectUid)
            .ToList();
        }
        var projectManagers = new List<ProjectManager>();
        if (dto.PrimaryManagerEmployeeIds is not null && dto.PrimaryManagerEmployeeIds.Any())
        {
            foreach (var id in dto.PrimaryManagerEmployeeIds)
            {
                if (Guid.TryParse(id, out var parsedId))
                {
                    projectManagers.Add(new ProjectManager
                    {
                        EmployeeUid = parsedId,
                        ProjectUid = entity.ProjectUid,
                        IsPrimary = true
                    });
                }
            }
        }

        if (dto.SubManagerEmployeeIds is not null && dto.SubManagerEmployeeIds.Any())
        {
            foreach (var id in dto.SubManagerEmployeeIds)
            {
                if (Guid.TryParse(id, out var parsedId))
                {
                    projectManagers.Add(new ProjectManager
                    {
                        EmployeeUid = parsedId,
                        ProjectUid = entity.ProjectUid,
                        IsPrimary = false
                    });
                }
            }
        }
        if (projectManagers.Count > 0)
        {
            entity.Managers.Clear();
            entity.Managers = projectManagers;
        }
    }

    #endregion

    #region Helper methods
    /// <summary>
    /// Calculates the distance between two GPS coordinates using the Haversine formula
    /// </summary>
    /// <param name="eLatitude">The latitude of the employee's location</param>
    /// <param name="eLongitude">The longitude of the employee's location</param>
    /// <param name="pLatitude">The latitude of the project's location</param>
    /// <param name="pLongitude">The longitude of the project's location</param>
    /// <returns>The distance between the two coordinates in kilometers</returns>
    private static double CalculateDistance(double eLatitude, double eLongitude, double pLatitude, double pLongitude)
    {
        // Haversine formula to calculate distance between two GPS coordinates
        const double earthRadiusKm = 6371.0;

        var dLat = DegreesToRadians(pLatitude - eLatitude);
        var dLon = DegreesToRadians(pLongitude - eLongitude);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(DegreesToRadians(eLatitude)) * Math.Cos(DegreesToRadians(pLatitude)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return earthRadiusKm * c;
    }

    /// <summary>
    /// Converts degrees to radians
    /// </summary>
    /// <param name="degrees">The degrees to convert</param>
    /// <returns>The radians</returns>
    private static double DegreesToRadians(double degrees)
    {
        return degrees * Math.PI / 180.0;
    }
    #endregion
}