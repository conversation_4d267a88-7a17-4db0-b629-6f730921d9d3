using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Dtos.UserInfo.Request;
using Kantoku.Api.Dtos.UserInfo.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
public class UserInfoController : BaseController
{
    private readonly IAuditLogService auditLogService;
    private readonly IUserInfoService userInfoService;
    public UserInfoController(
        ITResponseFactory responseFactory,
        IUserInfoService userInfoService,
        IAuditLogService auditLogService,
        Serilog.ILogger logger)
    : base(responseFactory)
    {
        this.userInfoService = userInfoService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get user information
    /// </summary>
    /// <returns>User information</returns>
    [HttpGet]
    public async Task<GeneralResponse<UserInfoResponseDto>> GetUserInfo()
    {
        try
        {
            var result = await userInfoService.GetUserInfo();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<UserInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update user information
    /// </summary>
    /// <param name="requestDto">User information update data</param>
    /// <returns>Updated user information</returns>
    [HttpPut]
    public async Task<GeneralResponse<UserInfoResponseDto>> UpdateUserInfo([FromBody] UpdateUserInfoRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<UserInfoResponseDto>();

            var result = await userInfoService.UpdateUserInfo(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<UserInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Audit log entries for the user</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<AuditLogResponseDto>();

            var res = await auditLogService.GetAuditLogsByEntity(id, nameof(UserInfo), filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get user's avatar
    /// </summary>
    /// <returns>User's avatar information</returns>
    [HttpGet("avatar")]
    public async Task<GeneralResponse<AvatarResponseDto>> GetAvatar()
    {
        try
        {
            var result = await userInfoService.GetUserAvatar();
            if (result is null || result.Data is null)
                return Fail<AvatarResponseDto>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET);
            result.Data.AvatarByteArr = [];
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AvatarResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update user's avatar
    /// </summary>
    /// <param name="file">New avatar file</param>
    /// <returns>Updated avatar information</returns>
    [HttpPut("avatar")]
    public async Task<GeneralResponse<bool>> UpdateAvatar(IFormFile file)
    {
        try
        {
            if (file is null || file.Length == 0)
                return BadRequest<bool>();

            var result = await userInfoService.UpdateUserAvatar(file);
            if (result is null || result.Data is false)
                return Fail<bool>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete user's avatar
    /// </summary>
    /// <returns>True if deletion was successful</returns>
    [HttpDelete("avatar")]
    public async Task<GeneralResponse<bool>> DeleteAvatar()
    {
        try
        {
            await userInfoService.DeleteUserAvatar();
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }
}