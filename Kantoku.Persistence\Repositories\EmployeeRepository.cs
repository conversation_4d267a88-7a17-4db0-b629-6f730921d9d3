using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IEmployeeRepository
{
    Task<Employee?> GetById(Guid employeeId, EmployeeQueryableOptions options);
    Task<IEnumerable<Employee>> GetByIds(IEnumerable<Guid> employeeIds, EmployeeQueryableOptions options);
    Task<Employee?> GetByAccount(Guid accountId, Guid orgId, EmployeeQueryableOptions options);
    Task<IEnumerable<Employee>> GetByRoleId(IEnumerable<Guid> roleIds, EmployeeQueryableOptions options);
    Task<IEnumerable<Employee>> GetProjectManagers(Guid? projectId, EmployeeQueryableOptions options);
    Task<(IEnumerable<Employee>, int)> GetByFilter(DomainFilter.EmployeeFilter filter, EmployeeQueryableOptions options);
    Task<IEnumerable<EmployeeAvatarQueryResult>> GetEmployeeAvatarUrl();
    Task<bool> IsProjectManager(Guid employeeId);
    Task<(bool level1Approval, bool level2Approval, bool representativeAuthority)> HasApprovalAuthority(Guid employeeId);
    Task<Employee?> Create(Employee employee, EmployeeQueryableOptions options);
    Task<Employee?> Update(Employee employee, EmployeeQueryableOptions options);

    Task<Guid?> Create(Employee employee);
    Task<bool> Update(Employee employee);
}

public class EmployeeRepository : BaseRepository<Employee>, IEmployeeRepository
{
    private readonly IEmployeeQueryable employeeQueryable;

    public EmployeeRepository(
        ApplicationDbContext context,
        IEmployeeQueryable employeeQueryable,
        ITenantContext tenantContext,
        Serilog.ILogger logger
    ) : base(context, logger, tenantContext)
    {
        this.employeeQueryable = employeeQueryable;
    }

    public async Task<IEnumerable<Employee>> GetByIds(IEnumerable<Guid> employeeIds, EmployeeQueryableOptions options)
    {
        try
        {
            var query = employeeQueryable.GetEmployeesQueryIncluded(options)
                    .Where(e => employeeIds.Contains(e.EmployeeUid));
            return await query.ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employees by ids");
            return [];
        }
    }
    public async Task<Employee?> GetById(Guid employeeId, EmployeeQueryableOptions options)
    {
        try
        {
            var query = employeeQueryable.GetEmployeesQueryIncluded(options)
                    .Where(e => e.EmployeeUid == employeeId);

            var employee = await query
                .FirstOrDefaultAsync();
            return employee;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employee by id with employeeId: {EmployeeId}", employeeId);
            return null;
        }
    }

    public async Task<Employee?> GetByAccount(Guid accountId, Guid orgId, EmployeeQueryableOptions options)
    {
        try
        {
            var query = employeeQueryable.GetEmployeesQueryIncluded(options)
                    .Where(e => e.OrgUid == orgId && e.AccountUid == accountId);

            var employee = await query
                    .FirstOrDefaultAsync();

            return employee;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee by user org with accountId: {AccountId} and orgId: {OrgId}", accountId, orgId);
            return null;
        }
    }

    public async Task<(IEnumerable<Employee>, int)> GetByFilter(DomainFilter.EmployeeFilter filter, EmployeeQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = employeeQueryable.GetEmployeesQueryFiltered(filter, options);

            var employees = await query
                .OrderBy(e => e.EmployeeCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var count = await query.CountAsync();

            return (employees, count);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employees");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<Employee>> GetByRoleId(IEnumerable<Guid> roleIds, EmployeeQueryableOptions options)
    {
        try
        {
            var query = employeeQueryable.GetEmployeesQueryIncluded(options)
                    .Where(e => e.EmployeeRoles.Any(r => roleIds.Contains(r.RoleUid)));
            return await query.ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employees by role id");
            return [];
        }
    }

    public async Task<IEnumerable<Employee>> GetProjectManagers(Guid? projectId, EmployeeQueryableOptions options)
    {
        try
        {
            var query = employeeQueryable.GetEmployeesQueryIncluded(options);
            if (projectId.HasValue)
            {
                query = query.Where(e => e.ProjectManagers.Any(pm => pm.ProjectUid == projectId.Value));
                return await query.ToListAsync();
            }
            var officeProjects = await context.Projects
                .Where(p => p.OrgUid == GetCurrentOrgUid())
                .Where(p => p.IsOffice == true && p.IsDeleted == false)
                .Select(p => p.ProjectUid)
                .ToListAsync();
            var currentEmployee = GetCurrentEmployeeUid();
            query = query.Where(e => e.ProjectManagers.Any(pm => officeProjects.Contains(pm.ProjectUid) && pm.EmployeeUid == currentEmployee));
            return await query.ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employees by project id as project manager");
            return [];
        }
    }

    public async Task<IEnumerable<EmployeeAvatarQueryResult>> GetEmployeeAvatarUrl()
    {
        try
        {
            return await context.Employees
                .Where(e => e.IsDeleted == false)
                .Select(e => new EmployeeAvatarQueryResult
                {
                    EmployeeUid = e.EmployeeUid.ToString(),
                    AvatarUrl = e.Account.UserInfo.AvatarUrl
                })
                .ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting employee avatar url");
            return [];
        }
    }

    public async Task<bool> IsProjectManager(Guid employeeUid)
    {
        try
        {
            return await context
                .ProjectManagers
                .Where(pm => pm.EmployeeUid == employeeUid)
                .Where(pm => pm.Employee.IsDeleted == false)
                .Where(pm => pm.Project.IsDeleted == false)
                .AnyAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error checking if employee is project manager with employeeUid: {EmployeeUid}", employeeUid);
            return false;
        }
    }

    public async Task<(bool level1Approval, bool level2Approval, bool representativeAuthority)> HasApprovalAuthority(Guid employeeUid)
    {
        try
        {
            var level1Approval = await context.ProjectManagers
                .Where(pm => pm.EmployeeUid == employeeUid)
                .Where(pm => pm.Project.IsDeleted == false)
                .AnyAsync();
            var level2Approval = await context.ProjectManagers
                .Where(pm => pm.EmployeeUid == employeeUid)
                .Where(pm => pm.Project.IsDeleted == false && pm.Project.IsOffice == true)
                .AnyAsync();
            var representativeAuthority = await context.Employees
                    .Where(r => r.EmployeeUid == employeeUid)
                .Where(r => r.HasApprovalAuthority == true)
                .AnyAsync();
            return (level1Approval, level2Approval, representativeAuthority);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error checking if employee has approval authority with employeeUid: {EmployeeUid}", employeeUid);
            return (false, false, false);
        }
    }

    public async Task<Employee?> Create(Employee employee, EmployeeQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Employees.AddAsync(employee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employee.EmployeeUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employee with employee: {Employee}", employee);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(Employee employee)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Employees.AddAsync(employee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return employee.EmployeeUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employee with employee: {Employee}", employee);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Employee?> Update(Employee employee, EmployeeQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Employees.Update(employee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employee.EmployeeUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee with employee: {Employee}", employee);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Employee employee)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Employees.Update(employee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee with employee: {Employee}", employee);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> Delete(Employee employee)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            employee.IsDeleted = true;
            context.Entry(employee).Property(e => e.IsDeleted).IsModified = true;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting employee with employee: {Employee}", employee);
            await transaction.RollbackAsync();
            return false;
        }
    }
}

public class EmployeeAvatarQueryResult
{
    public string EmployeeUid { get; set; } = null!;
    public string? AvatarUrl { get; set; }
}
