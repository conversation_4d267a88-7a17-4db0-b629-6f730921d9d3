using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.CategorizedCost.Response;
using Kantoku.Api.Dtos.Project.Request;
using Kantoku.Api.Dtos.Project.Response;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ProjectMapperCost
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Project entity to a ProjectSummaryResponseDto
    /// </summary>
    /// <param name="project">The Project entity to map</param>
    /// <param name="constructions">The constructions of the project</param>
    /// <param name="constructionCosts">The construction costs of the project</param>
    /// <param name="employeeShifts">The employee shifts of the project</param>
    /// <param name="leaveRequests">The leave requests of the project</param>
    /// <param name="projectSchedules">The project schedules of the project</param>
    /// <param name="categorizedCosts">The categorized costs of the project</param>
    /// <param name="rootCategories">The root categories of the project</param>
    /// <param name="dailyReports">The daily reports of the project</param>
    /// <param name="employees">The employees of the project</param>
    /// <param name="outSources">The out sources of the project</param>
    /// <param name="startDate">The start date of the project</param>
    /// <param name="endDate">The end date of the project</param>
    /// <param name="languageCode">The language code of the project</param>
    /// <returns>The mapped ProjectSummaryResponseDto</returns>
    public static ProjectSummaryResponseDto ToProjectSummaryResponseDto(
        this Project project,
        IEnumerable<Construction> constructions,
        IEnumerable<ConstructionCost> constructionCosts,
        IEnumerable<EmployeeShift> employeeShifts,
        IEnumerable<Request> leaveRequests,
        IEnumerable<ProjectSchedule> projectSchedules,
        IEnumerable<CategorizedCost> categorizedCosts,
        IEnumerable<Category> rootCategories,
        IEnumerable<ProjectDailyReport> dailyReports,
        IEnumerable<Employee> employees,
        IEnumerable<OutSource> outSources,
        DateOnly startDate,
        DateOnly endDate,
        string languageCode
    )
    {
        if (project == null)
            return new ProjectSummaryResponseDto();

        var summary = new ProjectSummaryResponseDto
        {
            ProjectId = project.ProjectUid.ToString(),
            ProjectCode = project.ProjectCode,
            ProjectName = project.ProjectName,
            ProjectTypeName = project.ProjectType?.TranslatedProjectType
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.ProjectTypeName,
            Address = project.Address,
            CustomerName = project.Customer?.CustomerName,
            ContractorName = project.Contractor?.ContractorName,
            ProjectStatus = project.Status.TranslatedStatus
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.StatusName,
            PrimaryManager = project.Managers.FirstOrDefault()?.Employee.EmployeeName,
            ExpectedStartDate = project.ExpectedStartDate?.ToString("yyyy-MM-dd"),
            ExpectedEndDate = project.ExpectedEndDate?.ToString("yyyy-MM-dd"),
            ActualStartDate = project.ActualStartDate?.ToString("yyyy-MM-dd"),
            ActualEndDate = project.ActualEndDate?.ToString("yyyy-MM-dd"),
            ProjectProgress = constructions.ToProjectProgressResponseDto(constructionCosts),
            ProjectAttendance = employeeShifts.ToProjectAttendanceResponseDto(leaveRequests, projectSchedules),
            ProjectCosts = ToProjectCostsResponseDto(categorizedCosts, dailyReports, rootCategories, employees, outSources, startDate, endDate, languageCode),
        };

        return summary;
    }

    /// <summary>
    /// Maps a Project entity to a ProjectProgressResponseDto
    /// </summary>
    /// <param name="constructions">The constructions of the project</param>
    /// <param name="constructionCosts">The construction costs of the project</param>
    /// <returns>The mapped ProjectProgressResponseDto</returns>
    private static ProjectProgressResponseDto ToProjectProgressResponseDto(
        this IEnumerable<Construction> constructions,
        IEnumerable<ConstructionCost> constructionCosts)
    {
        var constructionsProgress = new List<ConstructionProgressResponseDto>();
        foreach (var construction in constructions)
        {
            var initalCost = construction.InitialContractualCosts;
            var modifiedCost = construction.ModifiedContractualCosts;
            var estimatedCost = construction.InitialEstimatedCosts;

            var constructionCost = constructionCosts
                .Where(cc => cc.ConstructionUid == construction.ConstructionUid);

            var totalCostAmount = constructionCost.Sum(cc => cc.TotalCostAmount);
            var totalClaimedAmount = constructionCost.Sum(cc => cc.ConstructionPaymentRequest.RequestAmount);
            var totalContractualCostAmount = (initalCost?.Sum(ic => ic.Amount) ?? 0) + (modifiedCost?.Sum(mc => mc.Amount) ?? 0);

            constructionsProgress.Add(new ConstructionProgressResponseDto
            {
                ConstructionId = construction.ConstructionUid.ToString(),
                IsPrimary = construction.IsPrimary,
                TotalClaimedAmount = totalClaimedAmount,
                DisbursementProgressRatio = totalContractualCostAmount > 0 ? totalClaimedAmount / totalContractualCostAmount : 0,
                RemainingClaimableAmount = totalContractualCostAmount - totalClaimedAmount
            });
        }

        return new ProjectProgressResponseDto
        {
            ConstructionsProgress = constructionsProgress
        };
    }

    /// <summary>
    /// Maps a Project entity to a ProjectAttendanceResponseDto
    /// </summary>
    /// <param name="employeeShifts">The employee shifts of the project</param>
    /// <param name="leaveRequests">The leave requests of the project</param>
    /// <param name="projectSchedules">The project schedules of the project</param>
    /// <returns>The mapped ProjectAttendanceResponseDto</returns>
    private static ProjectAttendanceResponseDto ToProjectAttendanceResponseDto(
        this IEnumerable<EmployeeShift> employeeShifts,
        IEnumerable<Request> leaveRequests,
        IEnumerable<ProjectSchedule> projectSchedules
    )
    {
        var attendance = employeeShifts.Select(e =>
        {
            var IsRequestedOff = leaveRequests.Any(
                r => r.AuthorUid == e.EmployeeUid);
            var employeeAttendance = new EmployeeAttendanceStatusResponseDto
            {
                EmployeeName = e.Employee.EmployeeName,
                IsCheckedIn = e.CheckInTime is not null,
                IsRequestedOff = IsRequestedOff,
            };
            return employeeAttendance;
        });
        var presignedWorkload = projectSchedules.Sum(s => s.PresignedWorkload);
        var actualWorkload = employeeShifts.Where(e => e.TotalScheduledWorkTime > 0)
            .Sum(e => e.TotalWorkTime / e.TotalScheduledWorkTime);

        return new ProjectAttendanceResponseDto
        {
            PresignedWorkload = presignedWorkload,
            ActualWorkload = actualWorkload,
            EmployeeAttendances = attendance
        };
    }

    /// <summary>
    /// Maps a Project entity to a ProjectCostResponseDto
    /// </summary>
    /// <param name="categorizedCosts">The categorized costs of the project</param>
    /// <param name="dailyReports">The daily reports of the project</param>
    /// <param name="rootCategories">The root categories of the project</param>
    /// <param name="employees">The employees of the project</param>
    /// <param name="outSources">The out sources of the project</param>
    /// <param name="startDate">The start date of the project</param>
    /// <param name="endDate">The end date of the project</param>
    /// <param name="languageCode">The language code of the project</param>
    /// <returns>The mapped ProjectCostResponseDto</returns>
    private static ProjectCostResponseDto ToProjectCostsResponseDto(
        IEnumerable<CategorizedCost> categorizedCosts,
        IEnumerable<ProjectDailyReport> dailyReports,
        IEnumerable<Category> rootCategories,
        IEnumerable<Employee> employees,
        IEnumerable<OutSource> outSources,
        DateOnly startDate,
        DateOnly endDate,
        string languageCode
    )
    {
        var itemCategorizedCosts = new List<CategorizedCostResponseDto>();
        foreach (var c in categorizedCosts)
        {
            var matchedCategory = rootCategories
                .Where(r => r.CategoryUid == c.CategoryUid)
                .FirstOrDefault();
            if (matchedCategory is null)
            {
                continue;
            }
            var totalAmount = categorizedCosts
                .Where(i => i.CategoryUid == c.CategoryUid)
                .Sum(i => i.TotalAmount);

            itemCategorizedCosts.Add(new CategorizedCostResponseDto
            {
                CategoryId = c.CategoryUid.ToString(),
                CategoryCode = matchedCategory.CategoryCode,
                CategoryName = matchedCategory.IsDefault
                    ? matchedCategory.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName
                    : matchedCategory.CategoryName,
                TotalAmount = totalAmount,
            });
        }

        var totalEmployeeCost = 0.0;
        foreach (var workload in dailyReports.SelectMany(d => d.EmployeeWorkload ?? []))
        {
            var employee = employees.FirstOrDefault(e => e.EmployeeUid == workload.EmployeeUid);
            if (employee is null)
            {
                continue;
            }
            var employeeCost = employee.EmployeeCosts
                .Where(e => e.EffectiveDate >= startDate && e.EffectiveDate <= endDate)
                .FirstOrDefault();
            totalEmployeeCost += (employeeCost?.DailyCostAmount ?? 0) * (workload.MainConsWorkload + workload.SubConsWorkload);
        }
        var employeeCategory = rootCategories
            .Where(r => string.Equals(r.CategoryCode, CategoryConstant.EMPLOYEE, StringComparison.OrdinalIgnoreCase))
            .FirstOrDefault();
        if (employeeCategory is not null)
        {
            itemCategorizedCosts.Add(new CategorizedCostResponseDto
            {
                CategoryId = employeeCategory.CategoryUid.ToString(),
                CategoryCode = employeeCategory.CategoryCode,
                CategoryName = employeeCategory.IsDefault
                    ? employeeCategory.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName
                    : employeeCategory.CategoryName,
                TotalAmount = (long)totalEmployeeCost,
            });
        }

        var totalOutSourceCost = 0.0;
        foreach (var c in dailyReports.SelectMany(d => d.OutSourceWorkload ?? []))
        {
            var outSource = outSources.FirstOrDefault(o => o.OutSourceUid == c.OutSourceUid);
            if (outSource is null)
            {
                continue;
            }
            var outSourceCost = outSource.OutSourcePrices
                .Where(o => o.EffectiveDate >= startDate && o.EffectiveDate <= endDate)
                .FirstOrDefault();
            totalOutSourceCost += (outSourceCost?.PricePerDay ?? 0) * (c.MainConsWorkload + c.SubConsWorkload);
        }
        var outSourceCategory = rootCategories
            .Where(r => string.Equals(r.CategoryCode, CategoryConstant.OUTSOURCE_DAILY, StringComparison.OrdinalIgnoreCase))
            .FirstOrDefault();
        if (outSourceCategory is not null)
        {
            itemCategorizedCosts.Add(new CategorizedCostResponseDto
            {
                CategoryId = outSourceCategory.CategoryUid.ToString(),
                CategoryCode = outSourceCategory.CategoryCode,
                CategoryName = outSourceCategory.IsDefault
                    ? outSourceCategory.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName
                    : outSourceCategory.CategoryName,
                TotalAmount = (long)totalOutSourceCost,
            });
        }

        return new ProjectCostResponseDto
        {
            TotalCost = itemCategorizedCosts.Sum(i => i.TotalAmount ?? 0),
            RootCategorizedCosts = itemCategorizedCosts
        };
    }

    /// <summary>
    /// Maps a ProjectDailyReport entity to a ProjectDailyReportResponseDto
    /// </summary>
    /// <param name="report">The project daily report to map</param>
    /// <param name="employees">The employees of the project</param>
    /// <param name="outSources">The out sources of the project</param>
    /// <returns>The mapped ProjectDailyReportResponseDto</returns>
    public static ProjectDailyReportResponseDto ToProjectDailyReportResponseDto(
        this ProjectDailyReport report,
        IEnumerable<Employee> employees,
        IEnumerable<OutSource> outSources
    )
    {
        return new ProjectDailyReportResponseDto
        {
            ReportId = report.ProjectDailyReportUid.ToString(),
            ProjectId = report.ProjectUid.ToString(),
            ProjectCode = report.Project.ProjectCode,
            ProjectName = report.Project.ProjectName,
            Address = report.Project.Address,
            ReportDate = report.ReportDate.ToString("yyyy-MM-dd"),
            Description = report.Description,
            EmployeeWorkloads = report.EmployeeWorkload.ToEmployeeWorkloadResponseDto(employees),
            OutSourceWorkloads = report.OutSourceWorkload.ToOutSourceWorkloadResponseDto(outSources),
        };
    }

    /// <summary>
    /// Maps a ProjectDailyReport entity to a ProjectDailyReportResponseDto
    /// </summary>
    /// <param name="employeeWorkloads">The employee workloads of the project</param>
    /// <param name="employees">The employees of the project</param>
    /// <returns>The mapped ProjectDailyReportResponseDto</returns>
    private static IEnumerable<EmployeeWorkloadResponseDto> ToEmployeeWorkloadResponseDto(
        this IEnumerable<EmployeeWorkload>? employeeWorkloads,
        IEnumerable<Employee> employees
    )
    {
        if (employeeWorkloads is null || !employeeWorkloads.Any())
        {
            yield break;
        }
        foreach (var e in employeeWorkloads)
        {
            var employee = employees.FirstOrDefault(r => r.EmployeeUid == e.EmployeeUid);
            if (employee is null)
            {
                continue;
            }

            yield return new EmployeeWorkloadResponseDto
            {
                EmployeeId = e.EmployeeUid.ToString(),
                EmployeeName = employee?.EmployeeName,
                RankingName = employee?.Ranking?.RankingName,
                WorkloadOnMainConstruction = e.MainConsWorkload,
                WorkloadOnSubConstruction = e.SubConsWorkload,
            };
        }
    }

    /// <summary>
    /// Maps a ProjectDailyReport entity to a ProjectDailyReportResponseDto
    /// </summary>
    /// <param name="outSourceWorkloads">The out source workloads of the project</param>
    /// <param name="outSources">The out sources of the project</param>
    /// <returns>The mapped ProjectDailyReportResponseDto</returns>
    private static IEnumerable<OutSourceWorkloadResponseDto> ToOutSourceWorkloadResponseDto(
        this IEnumerable<OutSourceWorkload>? outSourceWorkloads,
        IEnumerable<OutSource> outSources
    )
    {
        if (outSourceWorkloads is null || !outSourceWorkloads.Any() || !outSources.Any())
        {
            return [];
        }
        var res = outSourceWorkloads.Select(e => new OutSourceWorkloadResponseDto
        {
            OutSourceId = e.OutSourceUid.ToString(),
            OutSourceName = outSources.FirstOrDefault(o => o.OutSourceUid == e.OutSourceUid)?.OutSourceName,
            WorkloadOnMainConstruction = e.MainConsWorkload,
            WorkloadOnSubConstruction = e.SubConsWorkload,
        });
        return res;
    }
    #endregion


    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateDailyReportRequestDto to a ProjectDailyReport entity
    /// </summary>
    /// <param name="dto">The create daily report request dto to map</param>
    /// <param name="projectUid">The uid of the project</param>
    /// <param name="orgUid">The uid of the organization</param>
    /// <returns>The mapped ProjectDailyReport entity</returns>
    public static ProjectDailyReport? ToEntity(this CreateDailyReportRequestDto dto, Guid projectUid, Guid orgUid)
    {
        if (dto is null)
            return null;

        var newReport = new ProjectDailyReport
        {
            ProjectUid = projectUid,
            ReportDate = DateOnly.Parse(dto.ReportDate!),
            Description = dto.Description,
            OutSourceWorkload = dto.OutSourceWorkload?.Select(o => new OutSourceWorkload
            {
                OutSourceUid = o.OutSourceId,
                MainConsWorkload = o.WorkloadOnMainConstruction,
                SubConsWorkload = o.WorkloadOnSubConstruction,
            }).ToList(),
            EmployeeWorkload = dto.EmployeeWorkload?.Select(e => new EmployeeWorkload
            {
                EmployeeUid = e.EmployeeId,
                MainConsWorkload = e.WorkloadOnMainConstruction,
                SubConsWorkload = e.WorkloadOnSubConstruction,
            }).ToList(),
            OrgUid = orgUid,
        };
        return newReport;
    }

    /// <summary>
    /// Maps a UpdateDailyReportRequestDto to a ProjectDailyReport entity
    /// </summary>
    /// <param name="dto">The update daily report request dto to map</param>
    /// <param name="entity">The project daily report to update</param>
    /// <returns>The mapped ProjectDailyReport entity</returns> 
    public static void UpdateFromDto(this ProjectDailyReport entity, UpdateDailyReportRequestDto dto)
    {
        if (dto is null)
            return;

        entity.EmployeeWorkload = dto.EmployeeWorkload?
             .Select(e => new EmployeeWorkload
             {
                 EmployeeUid = e.EmployeeId,
                 MainConsWorkload = e.WorkloadOnMainConstruction,
                 SubConsWorkload = e.WorkloadOnSubConstruction,
             }).ToList() ?? entity.EmployeeWorkload;

        entity.OutSourceWorkload = dto.OutSourceWorkload?
            .Select(o => new OutSourceWorkload
            {
                OutSourceUid = o.OutSourceId,
                MainConsWorkload = o.WorkloadOnMainConstruction,
                SubConsWorkload = o.WorkloadOnSubConstruction,
            }).ToList() ?? entity.OutSourceWorkload;
        entity.Description = dto.Description ?? entity.Description;
    }

    #endregion
}