using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class ProjectDailyReportQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedApprover { get; set; } = false;
}

public interface IProjectDailyReportQueryable
{
    IQueryable<ProjectDailyReport> GetProjectDailyReportQueryable(
        ProjectDailyReportQueryableOptions options
    );
    IQueryable<ProjectDailyReport> GetProjectDailyReportQueryableIncluded(
        ProjectDailyReportQueryableOptions options,
        IQueryable<ProjectDailyReport>? query = null
    );

    IQueryable<ProjectDailyReport> GetProjectDailyReportQueryFiltered(
        ProjectDailyReportFilter filter,
        ProjectDailyReportQueryableOptions options,
        IQueryable<ProjectDailyReport>? query = null
    );
}


public class ProjectDailyReportQueryable(ApplicationDbContext context) :
    BaseQueryable<ProjectDailyReport>(context), IProjectDailyReportQueryable
{
    public IQueryable<ProjectDailyReport> GetProjectDailyReportQueryable(
        ProjectDailyReportQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(r => r.IsDeleted == false);
        return query;
    }

    public IQueryable<ProjectDailyReport> GetProjectDailyReportQueryableIncluded(
        ProjectDailyReportQueryableOptions options,
        IQueryable<ProjectDailyReport>? query = null
    )
    {
        query ??= GetProjectDailyReportQueryable(options);
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(r => r.Project);
        }
        if (options.IncludedApprover || options.IncludedAll)
        {
            query = query.Include(r => r.Approver);
        }
        return query;
    }

    public IQueryable<ProjectDailyReport> GetProjectDailyReportQueryFiltered(
        ProjectDailyReportFilter filter,
        ProjectDailyReportQueryableOptions options,
        IQueryable<ProjectDailyReport>? query = null
    )
    {
        query ??= GetProjectDailyReportQueryableIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(r => r.Project.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(r => r.Project.ProjectName.Contains(filter.Keyword)
                || r.Project.ProjectCode.Contains(filter.Keyword));
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(r => r.ProjectUid == filter.ProjectId);
        }
        if (DateOnly.TryParse(filter.ReportFrom, out var reportFrom))
        {
            query = query.Where(r => r.ReportDate >= reportFrom);
        }
        if (DateOnly.TryParse(filter.ReportTo, out var reportTo))
        {
            query = query.Where(r => r.ReportDate <= reportTo);
        }
        if (DateOnly.TryParse(filter.ReportDate, out var reportDate))
        {
            query = query.Where(r => r.ReportDate == reportDate);
        }
        return query;
    }
}