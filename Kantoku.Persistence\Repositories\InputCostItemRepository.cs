﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IInputCostItemRepository
{
    Task<(int, IEnumerable<InputCostItem>)> GetByFilter(DomainFilter.InputCostItemCategoryFilter filter, InputCostItemQueryableOptions options);
    Task<InputCostItem?> GetById(Guid inputCostItemId, InputCostItemQueryableOptions options);
    Task<IEnumerable<InputCostItem>> GetByIds(IEnumerable<Guid> inputCostItemIds, InputCostItemQueryableOptions options);
    Task<InputCostItem?> Create(InputCostItem inputCostItem);
    Task<bool> CreateMultiple(IEnumerable<InputCostItem> inputCostItems);
    Task<InputCostItem?> Update(InputCostItem inputCostItem);
    Task<bool> UpdateMultiple(IEnumerable<InputCostItem> inputCostItems);
}

public class InputCostItemRepository : BaseRepository<InputCostItemRepository>, IInputCostItemRepository
{
    private readonly IInputCostItemQueryable queryable;
    public InputCostItemRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IInputCostItemQueryable queryable
    ) : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(int, IEnumerable<InputCostItem>)> GetByFilter(DomainFilter.InputCostItemCategoryFilter filter, InputCostItemQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetInputCostItemsQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .OrderByDescending(st => st.TransactionDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting input cost items by filter");
            return (0, []);
        }
    }

    public async Task<InputCostItem?> GetById(Guid inputCostItemId, InputCostItemQueryableOptions options)
    {
        try
        {
            var query = queryable.GetInputCostItemsQueryIncluded(options)
                .Where(st => st.InputCostItemUid == inputCostItemId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting input cost item by id");
            return null;
        }
    }

    public async Task<IEnumerable<InputCostItem>> GetByIds(IEnumerable<Guid> inputCostItemIds, InputCostItemQueryableOptions options)
    {
        try
        {
            var query = queryable.GetInputCostItemsQueryIncluded(options)
                .Where(st => inputCostItemIds.Contains(st.InputCostItemUid));
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting input cost items by ids");
            return [];
        }
    }

    public async Task<InputCostItem?> Create(InputCostItem inputCostItem)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.InputCostItems.AddAsync(inputCostItem);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(inputCostItem.InputCostItemUid, new InputCostItemQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error create new input cost item");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> CreateMultiple(IEnumerable<InputCostItem> inputCostItems)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.InputCostItems.AddRangeAsync(inputCostItems);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error create multiple input cost items");
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<InputCostItem?> Update(InputCostItem inputCostItem)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Entry(inputCostItem).State = EntityState.Modified;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(inputCostItem.InputCostItemUid, new InputCostItemQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error update input cost item");
            return null;
        }
    }

    public async Task<bool> UpdateMultiple(IEnumerable<InputCostItem> inputCostItems)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.InputCostItems.UpdateRange(inputCostItems);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error update multiple input cost items");
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task UpdateCategorizedCost(InputCostItem inputCostItem)
    {
        var construction = await context.Constructions.FindAsync(inputCostItem.ConstructionUid);
        if (construction is null)
        {
            logger.Error("Construction not found");
            return;
        }

        var project = await context.Projects.FindAsync(construction.ProjectUid);
        if (project is null)
        {
            logger.Error("Project not found");
            return;
        }
        var reportFrom = new DateOnly(inputCostItem.TransactionDate.Year, inputCostItem.TransactionDate.Month, project.MonthlyReportDate);
        var reportTo = reportFrom.AddMonths(1).AddDays(-1);

        var constructionCost = await context.ConstructionCosts
            .Where(c => c.ConstructionUid == inputCostItem.ConstructionUid && c.StartDate <= reportFrom && c.EndDate >= reportTo)
            .FirstOrDefaultAsync();

        if (constructionCost is null)
        {
            constructionCost = new ConstructionCost
            {
                ConstructionCostUid = Guid.NewGuid(),
                ConstructionUid = inputCostItem.ConstructionUid,
                OrgUid = project.OrgUid,
                StartDate = reportFrom,
                EndDate = reportTo,
                RiskModifiedAmount = 0,
                IsDeleted = false,
            };
            await context.ConstructionCosts.AddAsync(constructionCost);
        }

        var currentCategory = await context.Items.Where(i => i.ItemUid == inputCostItem.ItemUid)
            .Select(i => i.Category)
            .FirstOrDefaultAsync();
        if (currentCategory is null)
        {
            logger.Error("Category not found");
            return;
        }

        var inputCostItems = await context.InputCostItems
            .Include(i => i.Item)
            .ThenInclude(i => i.Category)
            .Where(i => i.ConstructionUid == inputCostItem.ConstructionUid
                && i.IsDeleted == false
                && i.TransactionDate >= reportFrom && i.TransactionDate <= reportTo
                && i.Item.CategoryUid == currentCategory.CategoryUid)
            .ToListAsync();

        var totalAmount = inputCostItems
            .Sum(i => i.TotalTaxed ?? i.TotalNonTaxed ?? (long)(i.Quantity * i.Price * (1 + (i.TaxRate ?? 0))));

        var categorizedCost = new CategorizedCost
        {
            CategorizedCostUid = Guid.NewGuid(),
            ConstructionCostUid = constructionCost.ConstructionCostUid,
            CategoryUid = currentCategory.CategoryUid,
            Quantity = inputCostItems.Sum(i => i.Quantity),
            TotalAmount = totalAmount,
        };

        constructionCost.TotalCostAmount = totalAmount;
        constructionCost.CategorizedCosts.Add(categorizedCost);

        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error update categorized cost");
            await transaction.RollbackAsync();
        }
    }

    public async Task UpdateCategorizedCosts(IEnumerable<InputCostItem> inputCostItems)
    {
        var inputCostItemsGroupByConstruction = inputCostItems.GroupBy(i => i.ConstructionUid);
        foreach (var group in inputCostItemsGroupByConstruction)
        {
            var inputCostItemsInConstruction = group.ToList();
            foreach (var inputCostItem in inputCostItemsInConstruction)
            {
                await UpdateCategorizedCost(inputCostItem);
            }
        }
    }
}
