﻿using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Item;
using Kantoku.Api.Dtos.Item.Request;
using Kantoku.Api.Dtos.Price.Request;
using Kantoku.Api.Dtos.Price.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ItemController : BaseController
{
    private readonly IItemService itemService;
    private readonly IAuditLogService auditLogService;
    public ItemController(IItemService itemService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService)
        : base(responseFactory)
    {
        this.itemService = itemService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get items by filter criteria
    /// </summary>
    /// <param name="filter">Filter parameters for items</param>
    /// <returns>List of filtered items</returns>
    [HttpGet]
    public async Task<GeneralResponse<ItemsResponseDto>> GetByFilter([FromQuery] ItemFilter filter)
    {
        try
        {
            var res = await itemService.GetByFilter(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ItemsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get item by ID
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <returns>Item details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ItemResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ItemResponseDto>();

            var result = await itemService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get item by ID
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>Item details</returns>
    [AllowAnonymous]
    [HttpGet("{id}/image")]
    public async Task<dynamic> GetImage([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await itemService.GetItemImage(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.ITEM_ILLUSTRATION_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get price history for an item
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="filter">Filter parameters for price history</param>
    /// <returns>Price history details</returns>
    [HttpGet("{id}/price")]
    public async Task<GeneralResponse<ItemPricesResponseDto>> GetPrice([FromRoute] Guid id, [FromQuery] ItemPriceFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ItemPricesResponseDto>();

            var result = await itemService.GetPriceByItemId(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ItemPricesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new item
    /// </summary>
    /// <param name="requestDto">Item creation details</param>
    /// <returns>Created item details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<GeneralResponse<ItemResponseDto>> Create([FromForm] CreateItemRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ItemResponseDto>();

            var result = await itemService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new price for an item
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="requestDto">Price creation details</param>
    /// <returns>Created price details</returns>
    [HttpPost("{id}/price")]
    public async Task<GeneralResponse<ItemPriceResponseDto>> CreatePrice([FromRoute] Guid id, [FromBody] CreateItemPriceRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ItemPriceResponseDto>();

            var result = await itemService.CreatePrice(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ItemPriceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing item
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="requestDto">Item update details</param>
    /// <returns>Updated item details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    public async Task<GeneralResponse<ItemResponseDto>> Update([FromRoute] Guid id, [FromForm] UpdateItemRequestDto requestDto)
    {
        try
        {
            var result = await itemService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an item
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await itemService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for an item
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Audit log details</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Item>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
