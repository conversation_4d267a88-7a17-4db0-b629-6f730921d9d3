using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class ConstructionQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedInputCosts { get; set; } = false;
    public bool IncludedConstructionCosts { get; set; } = false;
}
public interface IConstructionQueryable
{
    IQueryable<Construction> GetConstructionQuery(
        ConstructionQueryableOptions options
    );

    IQueryable<Construction> GetConstructionQueryIncluded(
        ConstructionQueryableOptions options,
        IQueryable<Construction>? query = null
    );

    IQueryable<Construction> GetConstructionQueryFilter(
        ConstructionFilter filter,
        ConstructionQueryableOptions options,
        IQueryable<Construction>? query = null
    );
}

public class ConstructionQueryable(ApplicationDbContext context) :
    BaseQueryable<Construction>(context), IConstructionQueryable
{
    public IQueryable<Construction> GetConstructionQuery(
        ConstructionQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Construction> GetConstructionQueryIncluded(
        ConstructionQueryableOptions options,
        IQueryable<Construction>? query = null

    )
    {
        query ??= GetConstructionQuery(options);
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(c => c.Project)
                .ThenInclude(p => p.Contractor);
            query = query.Include(c => c.Project)
                .ThenInclude(p => p.Customer);
        }
        if (options.IncludedInputCosts || options.IncludedAll)
        {
            query = query.Include(c => c.InputCosts);
        }
        if (options.IncludedConstructionCosts || options.IncludedAll)
        {
            query = query.Include(c => c.ConstructionCosts)
                .ThenInclude(cc => cc.ConstructionPaymentRequest);
            query = query.Include(c => c.ConstructionCosts)
                .ThenInclude(cc => cc.CategorizedCosts);
        }
        return query;
    }

    public IQueryable<Construction> GetConstructionQueryFilter(
        ConstructionFilter filter,
        ConstructionQueryableOptions options,
        IQueryable<Construction>? query = null
    )
    {
        query ??= GetConstructionQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(c => c.OrgUid == filter.OrgId);
        }
        if (filter.IsPrimary is not null)
        {
            query = query.Where(c => c.IsPrimary == filter.IsPrimary);
        }
        return query;
    }
}

