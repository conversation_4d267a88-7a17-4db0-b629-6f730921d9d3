using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class EmployeeLeave : AuditableEntity
{
    public Guid EmployeeLeaveUid { get; set; }
    public Guid EmployeeUid { get; set; }

    [AuditProperty]
    public float BaseLeave { get; set; }
    [AuditProperty]
    public DateOnly BaseLeaveExpire { get; set; }

    [AuditProperty]
    public float? LastRemainLeave { get; set; }

    [AuditProperty]
    public DateOnly? LastRemainLeaveExpire { get; set; }

    [AuditProperty]
    public float SelfTakenLeave { get; set; } = 0.0f;

    [AuditProperty]
    public float OrgTakenLeave { get; set; } = 0.0f;

    public bool IsDeleted { get; set; } = false;

    public virtual Employee Employee { get; set; } = null!;
}
