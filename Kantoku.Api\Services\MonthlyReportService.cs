using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.MonthlyAttReport;
using Kantoku.Api.Dtos.MonthlyAttReport.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.SharedKernel.Helpers;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;
using Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IMonthlyReportService
{
    Task<ResultDto<MonthlyReportsResponseDto>> GetMonthlyAttendance(ApiFilter.MonthlyReportFilter filter);
    Task<ResultDto<EmployeeMonthlyDetailsResponseDto>> GetMonthlyAttendanceDetails(Guid employeeId, ApiFilter.MonthlyReportFilter filter);
    Task<ResultDto<MonthlyReportResponseDto>> UpdateMonthlyReport(Guid monthlyReportId, UpdateMonthlyReportRequestDto dto);
    Task<ResultDto<MonthlyReportResponseDto>> RequestApproval(RequestApprovalRequestDto dto);
}

[Service(ServiceLifetime.Scoped)]
public class MonthlyReportService : BaseService<MonthlyReportService>, IMonthlyReportService
{
    private readonly IEmployeeRepository employeeRepository;
    private readonly IMonthlyReportRepository monthlyReportRepository;
    private readonly IEmployeeShiftRepository employeeShiftRepository;
    private readonly IEmployeeLeaveRepository employeeLeaveRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IFilterMapper<ApiFilter.MonthlyReportFilter, DomainFilter.MonthlyReportFilter> monthlyReportFilterMapper;
    public MonthlyReportService(
        IEmployeeRepository employeeRepository,
        IMonthlyReportRepository monthlyReportRepository,
        IEmployeeShiftRepository employeeShiftRepository,
        IEmployeeLeaveRepository employeeLeaveRepository,
        IRequestRepository requestRepository,
        IFilterMapper<ApiFilter.MonthlyReportFilter, DomainFilter.MonthlyReportFilter> monthlyReportFilterMapper,
        IHttpContextAccessor httpContextAccessor,
        Serilog.ILogger logger) : base(logger, httpContextAccessor)
    {
        this.employeeRepository = employeeRepository;
        this.monthlyReportRepository = monthlyReportRepository;
        this.employeeShiftRepository = employeeShiftRepository;
        this.employeeLeaveRepository = employeeLeaveRepository;
        this.requestRepository = requestRepository;
        this.monthlyReportFilterMapper = monthlyReportFilterMapper;
    }

    public async Task<ResultDto<MonthlyReportsResponseDto>> GetMonthlyAttendance(ApiFilter.MonthlyReportFilter filter)
    {
        var domainFilter = monthlyReportFilterMapper.MapToDomain(filter);
        var options = new MonthlyReportQueryableOptions
        {
            IncludedEmployee = true,
            IncludedApprover = true,
            IncludedEmployeeShifts = true,
        };
        var (reports, total) = await monthlyReportRepository.GetByFilter(domainFilter, options);
        if (reports is null || !reports.Any() || total == 0)
        {
            logger.Error("Monthly report not found");
            return new ErrorResultDto<MonthlyReportsResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_NOT_EXIST);
        }
        var leaveFilter = new DomainFilter.EmployeeLeaveFilter
        {
            QueryFrom = filter.ReportFrom,
            QueryTo = filter.ReportTo
        };
        var (leaves, _) = await employeeLeaveRepository.GetByFilter(
            leaveFilter,
            new EmployeeLeaveQueryableOptions()
        );
        var items = reports.ToMonthlyReportsResponseDto(leaves);
        var res = new MonthlyReportsResponseDto
        {
            Items = items,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            TotalRecords = total
        };
        return new SuccessResultDto<MonthlyReportsResponseDto>(res);
    }

    public async Task<ResultDto<EmployeeMonthlyDetailsResponseDto>> GetMonthlyAttendanceDetails(
        Guid employeeId, ApiFilter.MonthlyReportFilter filter)
    {
        var domainFilter = monthlyReportFilterMapper.MapToDomain(filter);
        var shifts = await employeeShiftRepository.GetByFilter(
            new DomainFilter.EmployeeShiftFilter
            {
                EmployeeId = employeeId,
                TimeFrom = filter.ReportFrom,
                TimeTo = filter.ReportTo,
            },
            new EmployeeShiftQueryableOptions
            {
                IncludedEmployee = true,
                IncludedProject = true,
            }
        );
        var (requests, _) = await requestRepository.GetByAuthor(
            employeeId,
            new DomainFilter.RequestFilter
            {
                FromDate = filter.ReportFrom,
                ToDate = filter.ReportTo,
                RequestTypeCode = Domain.Models.RequestTypeConstants.LEAVE
            },
            new RequestQueryableOptions
            {
                IncludedAuthor = true,
                IncludedProject = true,
            }
        );

        var attendanceMap = new Dictionary<DateOnly, AttendanceDetailDto>();

        for (var date = DateOnly.Parse(filter.ReportFrom); date <= DateOnly.Parse(filter.ReportTo); date = date.AddDays(1))
        {
            attendanceMap[date] = new AttendanceDetailDto
            {
                Date = date.ToString("yyyy-MM-dd"),
                ShiftInfos = new List<ShiftInfoDto>(),
                LeaveInfos = new List<RequestInfoDto>()
            };
        }

        // Map shifts
        foreach (var shift in shifts)
        {
            if (shift.CheckInTime == null)
            {
                continue;
            }
            var shiftDate = DateOnly.FromDateTime(shift.CheckInTime.Value);
            if (attendanceMap.ContainsKey(shiftDate))
            {
                // Check if shift overlaps with the day
                var dayStart = shiftDate.ToDateTime(TimeOnly.MinValue);
                var dayEnd = shiftDate.ToDateTime(TimeOnly.MaxValue);

                if ((shift.CheckInTime >= dayStart && shift.CheckInTime <= dayEnd) ||
                    (shift.CheckOutTime >= dayStart && shift.CheckOutTime <= dayEnd))
                {
                    ((List<ShiftInfoDto>)attendanceMap[shiftDate].ShiftInfos).Add(new ShiftInfoDto
                    {
                        ProjectName = shift.Project?.ProjectName,
                        WorkingLocation = shift.WorkingLocation,
                        CheckInTime = shift.CheckInTime?.ToString("HH:mm:ss"),
                        CheckOutTime = shift.CheckOutTime?.ToString("HH:mm:ss"),
                        WorkHours = (float?)Math.Round(shift.TotalWorkTime, 2),
                        Overtime = (float?)Math.Round(shift.TotalOverTime, 2),
                        Description = shift.Description,
                        IsRequested = shift.IsRequested,
                        IsApproved = shift.IsApproved
                    });
                }
            }
        }

        var languageCode = GetCurrentLanguageCode();
        // Map requests
        foreach (var request in requests)
        {
            var requestStartDate = DateOnly.FromDateTime(request.RequestFrom);
            var requestEndDate = DateOnly.FromDateTime(request.RequestTo);

            // Iterate through all dates in the request range
            for (var date = requestStartDate; date <= requestEndDate; date = date.AddDays(1))
            {
                if (attendanceMap.ContainsKey(date))
                {
                    var dayStart = date.ToDateTime(TimeOnly.MinValue);
                    var dayEnd = date.ToDateTime(TimeOnly.MaxValue);

                    // Check if request period overlaps with the day
                    if (request.RequestFrom <= dayEnd && request.RequestTo >= dayStart)
                    {
                        ((List<RequestInfoDto>)attendanceMap[date].LeaveInfos).Add(new RequestInfoDto
                        {
                            RequestType = request.RequestType.TranslatedRequestType
                                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.RequestTypeName,
                            RequestFrom = request.RequestFrom.ToString("HH:mm:ss"),
                            RequestTo = DateTimeHelper.AdjustBeyondTime(request.RequestFrom, request.RequestTo),
                            Status = request.Status.TranslatedStatus
                                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.StatusName,
                            Description = request.Description
                        });
                    }
                }
            }
        }

        var employee = await employeeRepository.GetById(employeeId, new EmployeeQueryableOptions());
        if (employee is null)
        {
            logger.Error("Employee {EmployeeId} not exist", employeeId);
            return new ErrorResultDto<EmployeeMonthlyDetailsResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }

        var result = new EmployeeMonthlyDetailsResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            EmployeeName = employee.EmployeeName,
            Code = employee.EmployeeCode,
            Calendar = attendanceMap.Values
        };
        return new SuccessResultDto<EmployeeMonthlyDetailsResponseDto>(result);
    }

    public async Task<ResultDto<MonthlyReportResponseDto>> UpdateMonthlyReport(Guid monthlyReportId, UpdateMonthlyReportRequestDto dto)
    {
        var currentReport = await monthlyReportRepository.GetById(monthlyReportId, new MonthlyReportQueryableOptions());
        if (currentReport is null)
        {
            logger.Error("Monthly report {MonthlyReportId} not exist", monthlyReportId);
            return new ErrorResultDto<MonthlyReportResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_NOT_EXIST);
        }
        if (currentReport.IsApproved.HasValue && currentReport.IsApproved.Value == true)
        {
            logger.Error("Monthly report {MonthlyReportId} is already approved", monthlyReportId);
            return new ErrorResultDto<MonthlyReportResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_ALREADY_APPROVED);
        }
        var leaves = await employeeLeaveRepository.GetByEmployeeIdAndDateRange(
            currentReport.EmployeeUid,
            new DateOnly(currentReport.ReportFrom.Year, 1, 1),
            new DateOnly(currentReport.ReportTo.Year, 12, 31),
            new EmployeeLeaveQueryableOptions()
        );
        var currentLeave = leaves.FirstOrDefault();
        currentReport.Description = dto.Description ?? currentReport.Description;
        if (dto.IsApproved.HasValue)
        {
            currentReport.IsApproved = dto.IsApproved.Value;
            currentReport.ApproverUid = GetCurrentEmployeeUid();
            currentReport.ApprovedTime = DateTime.Now;
        }

        var updatedReport = await monthlyReportRepository.Update(currentReport);
        if (updatedReport is null)
        {
            logger.Error("Failed to update monthly report for employee {EmployeeId}", currentReport.EmployeeUid);
            return new ErrorResultDto<MonthlyReportResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_UPDATE_FAILED);
        }
        var result = updatedReport.ToMonthlyReportResponseDto(currentLeave);
        return new SuccessResultDto<MonthlyReportResponseDto>(result);
    }

    public async Task<ResultDto<MonthlyReportResponseDto>> RequestApproval(RequestApprovalRequestDto dto)
    {
        var currentReport = await monthlyReportRepository.GetByEmployeeAndDate(
            GetCurrentEmployeeUid(),
            DateOnly.Parse(dto.ReportFrom),
            DateOnly.Parse(dto.ReportTo),
            new MonthlyReportQueryableOptions()
        );
        if (currentReport is null)
        {
            var newReport = new MonthlyReport
            {
                EmployeeUid = GetCurrentEmployeeUid(),
                ReportFrom = DateOnly.Parse(dto.ReportFrom),
                ReportTo = DateOnly.Parse(dto.ReportTo),
                Description = dto.Description,
            };
            currentReport = await monthlyReportRepository.Create(newReport);
            if (currentReport is null)
            {
                logger.Error("Failed to create monthly report for employee {EmployeeUid}", GetCurrentEmployeeUid());
                return new ErrorResultDto<MonthlyReportResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_NOT_EXIST);
            }
        }
        currentReport.IsRequested = true;
        var updatedReport = await monthlyReportRepository.Update(currentReport);
        if (updatedReport is null)
        {
            logger.Error("Failed to request approval for monthly report {ReportFrom} to {ReportTo}", dto.ReportFrom, dto.ReportTo);
            return new ErrorResultDto<MonthlyReportResponseDto>(ResponseCodeConstant.ATTENDANCE_REPORT_UPDATE_FAILED);
        }
        var result = updatedReport.ToMonthlyReportResponseDto();
        return new SuccessResultDto<MonthlyReportResponseDto>(result);
    }
}