using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class RankingConfiguration(string schema) : IEntityTypeConfiguration<Ranking>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Ranking> builder)
    {
        builder.HasKey(e => e.RankingUid).HasName("ranking_pkey");

        builder.ToTable("ranking", Schema, tb => tb.HasComment("Có 4 bậc theo thực tế: A, B, C, C'"));

        builder.Property(e => e.RankingUid)
            .HasColumnName("ranking_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.RankingName)
            .HasColumnName("ranking_name");
        builder.Property(e => e.MinValue)
            .HasColumnName("min_value");
        builder.Property(e => e.MaxValue)
            .HasColumnName("max_value");
        builder.Property(e => e.AverageValue)
            .HasColumnName("average_value");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Org).WithMany(p => p.Rankings)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("ranking_org_id_fkey");
    }
}

