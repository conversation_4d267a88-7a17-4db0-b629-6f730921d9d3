namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for construction cost queries - contains only business logic, no HTTP concerns
/// </summary>
public class ConstructionCostFilter : BaseFilter
{
    /// <summary>
    /// Filter construction costs from this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateFrom { get; set; }

    /// <summary>
    /// Filter construction costs to this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateTo { get; set; }

    /// <summary>
    /// Filter by primary construction costs only
    /// </summary>
    public bool? IsPrimary { get; set; }

    /// <summary>
    /// Filter by construction ID
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// Filter by project ID
    /// </summary>
    public Guid? ProjectId { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Validate date format (basic validation)
        if (!string.IsNullOrWhiteSpace(DateFrom) && !DateTime.TryParse(DateFrom, out _))
        {
            DateFrom = null; // Invalid date, ignore
        }

        if (!string.IsNullOrWhiteSpace(DateTo) && !DateTime.TryParse(DateTo, out _))
        {
            DateTo = null; // Invalid date, ignore
        }
    }
}

