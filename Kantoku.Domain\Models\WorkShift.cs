﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class WorkShift : AuditableEntity
{
    public Guid WorkShiftUid { get; set; }

    [AuditProperty]
    public string WorkShiftCode { get; set; } = null!;

    [AuditProperty]
    public string WorkShiftName { get; set; } = null!;

    [AuditProperty]
    public TimeOnly CheckInTime { get; set; }

    [AuditProperty]
    public TimeOnly CheckOutTime { get; set; }

    [AuditProperty]
    public IEnumerable<WorkShiftBreakTime> WorkShiftBreakTimes { get; set; } = [];

    [AuditProperty]
    public string? Description { get; set; }

    public float TotalRequiredTime { get; set; }
    public Guid OrgUid { get; set; }
    public bool IsDeleted { get; set; } = false;

    public virtual Org Org { get; set; } = null!;
    public virtual ICollection<ProjectWorkShift> ProjectWorkShifts { get; set; } = [];
}

public class WorkShiftBreakTime
{
    public TimeOnly BreakInTime { get; set; }

    public TimeOnly BreakOutTime { get; set; }
}
