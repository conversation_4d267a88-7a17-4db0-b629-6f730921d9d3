using System.ComponentModel.DataAnnotations;

namespace Kantoku.Processor.Dtos;

/// <summary>
/// Request model for updating cron expression
/// </summary>
public class UpdateCronRequest
{
    /// <summary>
    /// The new cron expression (e.g., "0 */2 * * *" for every 2 hours)
    /// </summary>
    [Required]
    [StringLength(50, MinimumLength = 9)]
    public string CronExpression { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the change
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// Request model for updating job status (enable/disable)
/// </summary>
public class UpdateJobStatusRequest
{
    /// <summary>
    /// Whether the job should be enabled or disabled
    /// </summary>
    [Required]
    public bool Enabled { get; set; }

    /// <summary>
    /// Optional reason for the status change
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// Response model for job information
/// </summary>
public class JobInfoResponse
{
    public string Id { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime? NextExecution { get; set; }
    public DateTime? LastExecution { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? LastJobId { get; set; }
    public string? LastJobState { get; set; }
    public string? Error { get; set; }
    public bool IsEnabled { get; set; }
}

/// <summary>
/// Cron expression template
/// </summary>
public class CronTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Expression { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}
