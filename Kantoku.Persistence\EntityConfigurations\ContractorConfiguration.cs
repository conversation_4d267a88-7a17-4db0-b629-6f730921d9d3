using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class ContractorConfiguration(string schema) : IEntityTypeConfiguration<Contractor>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Contractor> builder)
    {
        builder.HasKey(e => e.ContractorUid).HasName("contractor_pkey");

        builder.ToTable("contractor", Schema);

        builder.Property(e => e.ContractorUid)
            .HasColumnName("contractor_uid");
        builder.Property(e => e.ContractorCode)
            .HasColumnName("contractor_code");
        builder.Property(e => e.ContractorName)
            .HasColumnName("contractor_name");
        builder.Property(e => e.ContractorSubName)
            .HasColumnName("contractor_sub_name");
        builder.Property(e => e.CorporateNumber)
            .HasColumnName("corporate_number");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.ContactPerson)
            .HasColumnName("contact_person")
            .HasColumnType("jsonb")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ContactPerson>(v)
            );
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Contractors)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("contractor_org_id_fkey");
    }
} 