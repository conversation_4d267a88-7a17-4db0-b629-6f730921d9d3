using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IOutSourceRepository
{
    Task<(IEnumerable<OutSource>, int)> GetByFilter(DomainFilter.OutSourceFilter filter, OutSourceQueryableOptions options);
    Task<OutSource?> GetById(Guid outSourceId, OutSourceQueryableOptions options);
    Task<IEnumerable<OutSource>> GetByIds(IEnumerable<Guid> outSourceIds, OutSourceQueryableOptions options);
    Task<OutSource?> Create(OutSource outSource);
    Task<OutSource?> Update(OutSource outSource);
}

public class OutSourceRepository : BaseRepository<OutSource>, IOutSourceRepository
{
    private readonly IOutSourceQueryable outSourceQueryable;

    public OutSourceRepository(
        ApplicationDbContext context,
        IOutSourceQueryable outSourceQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.outSourceQueryable = outSourceQueryable;
    }

    public async Task<(IEnumerable<OutSource>, int)> GetByFilter(DomainFilter.OutSourceFilter filter, OutSourceQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = outSourceQueryable.GetOutSourceQueryFiltered(filter, options);

            var outSources = await query
                .OrderBy(p => p.OutSourceCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();
            return (outSources, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all outSources");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<OutSource>> GetByIds(IEnumerable<Guid> outSourceIds, OutSourceQueryableOptions options)
    {
        try
        {
            var query = outSourceQueryable.GetOutSourceQueryIncluded(options)
                .Where(p => outSourceIds.Contains(p.OutSourceUid));

            var outSources = await query
                .OrderBy(p => p.OutSourceCode)
                .ToListAsync();
            return outSources;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all outSources");
            return [];
        }
    }

    public async Task<OutSource?> GetById(Guid outSourceId, OutSourceQueryableOptions options)
    {
        try
        {
            var query = outSourceQueryable.GetOutSourceQueryIncluded(options)
                .Where(p => p.OutSourceUid == outSourceId);

            var outSource = await query
                .FirstOrDefaultAsync();
            return outSource;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSource by id {OutSourceId}", outSourceId);
            return null;
        }
    }

    public async Task<OutSource?> Create(OutSource outSource)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.OutSources.AddAsync(outSource);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSource.OutSourceUid, new OutSourceQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating outSource {OutSource}", outSource);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<OutSource?> Update(OutSource outSource)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.OutSources.Update(outSource);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSource.OutSourceUid, new OutSourceQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating outSource {OutSource}", outSource);
            await transaction.RollbackAsync();
            return null;
        }
    }
}