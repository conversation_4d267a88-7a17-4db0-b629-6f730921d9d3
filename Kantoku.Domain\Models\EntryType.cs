﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class EntryType : AuditableEntity
{
    public Guid EntryTypeUid { get; set; }

    [AuditProperty]
    public string EntryTypeName { get; set; } = null!;

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<InputCost> InputCosts { get; set; } = [];
}
