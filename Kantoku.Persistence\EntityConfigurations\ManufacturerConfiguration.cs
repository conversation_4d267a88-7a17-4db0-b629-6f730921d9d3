using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Persistence.EntityConfigurations;

public class ManufacturerConfiguration(string schema) : IEntityTypeConfiguration<Manufacturer>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Manufacturer> builder)
    {
        builder.HasKey(e => e.ManufacturerUid).HasName("manufacturer_pkey");

        builder.ToTable("manufacturer", Schema);

        builder.Property(e => e.ManufacturerUid)
            .HasColumnName("manufacturer_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.ManufacturerCode)
            .HasColumnName("manufacturer_code");
        builder.Property(e => e.ManufacturerName)
            .HasColumnName("manufacturer_name");
        builder.Property(e => e.ManufacturerSubName)
            .HasColumnName("manufacturer_sub_name");
        builder.Property(e => e.CorporateNumber)
            .HasColumnName("corporate_number");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.ContactPerson)
            .HasColumnName("contact_person")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ContactPerson>(v)
            );
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");

        builder.HasOne(d => d.Org).WithMany(p => p.Manufacturers)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("manufacturer_org_id_fkey");
    }
}
