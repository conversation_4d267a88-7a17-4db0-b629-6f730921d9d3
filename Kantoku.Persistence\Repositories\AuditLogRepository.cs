using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IAuditLogRepository
{
    Task<(IEnumerable<AuditLog>, int)> GetByEntity<T>(string entityUid, AuditLogFilter filter, AuditLogQueryableOptions options);
    Task<(IEnumerable<AuditLog>, int)> GetByEntity(string entityUid, string entityName, AuditLogFilter filter, AuditLogQueryableOptions options);
    Task<(IEnumerable<AuditLog>, int)> GetByAccount(string accountId, AuditLogFilter filter, AuditLogQueryableOptions options);
}

public class AuditLogRepository : BaseRepository<AuditLogRepository>, IAuditLogRepository
{
    private readonly IAuditLogQueryable auditLogQueryable;
    public AuditLogRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IAuditLogQueryable auditLogQueryable
    ) : base(context, logger, tenantContext)
    {
        this.auditLogQueryable = auditLogQueryable;
    }

    public async Task<(IEnumerable<AuditLog>, int)> GetByEntity<T>(string entityUid, AuditLogFilter filter, AuditLogQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = auditLogQueryable.GetAuditLogQueryFiltered(filter, options, null)
                            .Where(x => x.EntityId.Equals(entityUid) && x.EntityName.Equals(typeof(T).Name));

            var auditLogs = await query
            .Skip((filter.PageNum - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

            var totalRecords = await query.CountAsync();

            return (auditLogs, totalRecords);
        }
        catch (Exception e)
        {
            logger.Error(e, "Error getting audit logs");
            return (new List<AuditLog>(), 0);
        }
    }

    public async Task<(IEnumerable<AuditLog>, int)> GetByEntity(string entityUid, string entityName, AuditLogFilter filter, AuditLogQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = auditLogQueryable.GetAuditLogQueryFiltered(filter, options)
                            .Where(x => x.EntityId.Equals(entityUid) && x.EntityName.Equals(entityName));

            var auditLogs = await query
            .Skip((filter.PageNum - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

            var totalRecords = await query.CountAsync();

            return (auditLogs, totalRecords);
        }
        catch (Exception e)
        {
            logger.Error(e, "Error getting audit logs");
            return (new List<AuditLog>(), 0);
        }
    }

    public async Task<(IEnumerable<AuditLog>, int)> GetByAccount(string accountId, AuditLogFilter filter, AuditLogQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = auditLogQueryable.GetAuditLogQueryFiltered(filter, options);

            query = query.Where(x => x.AccountUid == Guid.Parse(accountId));

            var auditLogs = await query
            .Skip((filter.PageNum - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

            var totalRecords = await query.CountAsync();

            return (auditLogs, totalRecords);
        }
        catch (Exception e)
        {
            logger.Error(e, "Error getting audit logs");
            return (new List<AuditLog>(), 0);
        }
    }
}

