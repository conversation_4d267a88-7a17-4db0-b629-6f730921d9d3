using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;


namespace Kantoku.Persistence.Repositories;

public interface ILeaveTypeRepository
{
    Task<LeaveType?> GetByCode(string code);
    Task<IEnumerable<LeaveType>> GetAll();
}

public class LeaveTypeRepository : BaseRepository<LeaveType>, ILeaveTypeRepository
{
    public LeaveTypeRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<LeaveType?> GetByCode(string code)
    {
        try
        {
            return await context.LeaveTypes
                .Where(l => l.LeaveTypeCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting leave type by code {Code}", code);
            return null;
        }
    }

    public async Task<IEnumerable<LeaveType>> GetAll()
    {
        try
        {
            return await context.LeaveTypes
               .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all leave types");
            return [];
        }
    }
}