using Kantoku.Domain.Models;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Services;

public interface IGlobalConfigService
{
    Task<ResultDto<GlobalConfig>> GetConfig(string key);
    Task<ResultDto<string>> GetConfigAsString(string key);
    Task<ResultDto<bool>> GetConfigAsBool(string key);
    Task<ResultDto<int?>> GetConfigAsInteger(string key);
    Task<ResultDto<float?>> GetConfigAsFloat(string key);
}

[Service(ServiceLifetime.Scoped)]
public class GlobalConfigService : BaseService<GlobalConfigService>, IGlobalConfigService
{
    private readonly IGlobalConfigRepository globalConfigRepository;

    public GlobalConfigService(
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IGlobalConfigRepository globalConfigRepository)
        : base(logger, httpContextAccessor)
    {
        this.globalConfigRepository = globalConfigRepository;
    }

    public async Task<ResultDto<GlobalConfig>> GetConfig(string key)
    {
        var config = await globalConfigRepository.GetByKey(key);
        if (config is null)
        {
            logger.Error("Global config not found for key {Key}", key);
            return new ErrorResultDto<GlobalConfig>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
        }
        return new SuccessResultDto<GlobalConfig>(config);
    }

    public async Task<ResultDto<bool>> GetConfigAsBool(string key)
    {
        try
        {
            var result = await GetConfig(key);
            if (result is null || result.Data is null)
            {
                return new ErrorResultDto<bool>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
            }
            return new SuccessResultDto<bool>(bool.Parse(result.Data.Value));
        }
        catch (Exception)
        {
            return new ErrorResultDto<bool>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
        }
    }

    public async Task<ResultDto<float?>> GetConfigAsFloat(string key)
    {
        try
        {
            var result = await GetConfig(key);
            if (result is null || result.Data is null)
            {
                return new ErrorResultDto<float?>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
            }
            return new SuccessResultDto<float?>(float.Parse(result.Data.Value));
        }
        catch (Exception)
        {
            return new ErrorResultDto<float?>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
        }
    }

    public async Task<ResultDto<int?>> GetConfigAsInteger(string key)
    {
        try
        {
            var result = await GetConfig(key);
            if (result is null || result.Data is null)
            {
                return new ErrorResultDto<int?>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
            }
            return new SuccessResultDto<int?>(int.Parse(result.Data.Value));
        }
        catch (Exception)
        {
            return new ErrorResultDto<int?>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
        }
    }

    public async Task<ResultDto<string>> GetConfigAsString(string key)
    {
        try
        {
            var result = await GetConfig(key);
            if (result is null || result.Data is null)
            {
                return new ErrorResultDto<string>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
            }
            return new SuccessResultDto<string>(result.Data.Value);
        }
        catch (Exception)
        {
            return new ErrorResultDto<string>(ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST);
        }
    }
}