namespace Kantoku.Persistence.Filters;

public class ItemFilter : BaseFilter
{
    /// <summary>
    /// The keyword
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// The category ID
    /// </summary>
    public Guid? CategoryId { get; set; }

    /// <summary>
    /// The manufacturer ID
    /// </summary>
    public Guid? ManufacturerId { get; set; }

    /// <summary>
    /// The manufacturer IDs
    /// </summary>
    public IEnumerable<Guid>? ManufacturerIds { get; set; }

    /// <summary>
    /// The size
    /// </summary>
    public string? Size { get; set; }

    /// <summary>
    /// The serial number
    /// </summary>
    public string? SerialNumber { get; set; }
}

