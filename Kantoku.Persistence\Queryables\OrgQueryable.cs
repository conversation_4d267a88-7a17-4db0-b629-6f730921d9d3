using Kantoku.Persistence.Contexts;
using Orgz = Kantoku.Domain.Models.Org;

namespace Kantoku.Persistence.Queryables;

public class OrgQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IOrgQueryable
{
    IQueryable<Orgz> GetOrgQuery(
        OrgQueryableOptions options
    );
}

public class OrgQueryable(ApplicationDbContext context) :
    BaseQueryable<Orgz>(context), IOrgQueryable
{

    public IQueryable<Orgz> GetOrgQuery(
        OrgQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }
}
