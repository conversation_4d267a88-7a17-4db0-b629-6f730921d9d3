using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Persistence.Filters;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Repositories;
using Kantoku.SharedKernel.Attributes.Properties;
using Kantoku.SharedKernel.Helpers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IAuditLogService
{
    Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByEntity(string entityUid, string entityName, ApiFilter.AuditLogFilter filter);
    Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByEntity<T>(string entityUid, ApiFilter.AuditLogFilter filter);
    Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByAccount(string accountUid, ApiFilter.AuditLogFilter filter);
}

[Service(ServiceLifetime.Scoped)]
public class AuditLogService : BaseService<AuditLogService>, IAuditLogService
{
    private readonly IAuditLogRepository auditLogRepository;
    private readonly IFilterMapper<ApiFilter.AuditLogFilter, DomainFilter.AuditLogFilter> filterMapper;
    private readonly JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings
    {
        TypeNameHandling = TypeNameHandling.Auto,
        NullValueHandling = NullValueHandling.Ignore,
        ContractResolver = new DefaultContractResolver
        {
            NamingStrategy = new CamelCaseNamingStrategy
            {
                ProcessDictionaryKeys = true,
            }
        }
    };

    public AuditLogService(
        IFilterMapper<ApiFilter.AuditLogFilter, DomainFilter.AuditLogFilter> filterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IAuditLogRepository auditLogRepository
    ) : base(logger, httpContextAccessor)
    {
        this.auditLogRepository = auditLogRepository;
        this.filterMapper = filterMapper;
    }

    public async Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByEntity(string entityUid, string entityName, ApiFilter.AuditLogFilter filter)
    {
        var options = new AuditLogQueryableOptions
        {
            IncludedAll = true,
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (auditLogs, totalRecords) = await auditLogRepository.GetByEntity(entityUid, entityName, domainFilter, options);
        if (auditLogs is null || !auditLogs.Any() || totalRecords == 0)
        {
            return new ErrorResultDto<AuditLogResponseDto>(ResponseCodeConstant.NO_AUDIT_LOG);
        }

        var result = AuditLogResponseParser(auditLogs, filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<AuditLogResponseDto>(result);
    }

    public async Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByEntity<T>(string entityUid, ApiFilter.AuditLogFilter filter)
    {
        var options = new AuditLogQueryableOptions
        {
            IncludedAccount = true,
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (auditLogs, totalRecords) = await auditLogRepository.GetByEntity<T>(entityUid, domainFilter, options);
        if (auditLogs is null || !auditLogs.Any() || totalRecords == 0)
        {
            return new ErrorResultDto<AuditLogResponseDto>(ResponseCodeConstant.NO_AUDIT_LOG);
        }

        var result = AuditLogResponseParser<T>(auditLogs, filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<AuditLogResponseDto>(result);
    }

    public async Task<ResultDto<AuditLogResponseDto>> GetAuditLogsByAccount(string accountUid, ApiFilter.AuditLogFilter filter)
    {
        var options = new AuditLogQueryableOptions
        {
            IncludedAccount = true,
        };
        var domainFilter = filterMapper.MapToDomain(filter);
        var (auditLogs, totalRecords) = await auditLogRepository.GetByAccount(accountUid, domainFilter, options);
        if (auditLogs is null || !auditLogs.Any() || totalRecords == 0)
        {
            return new ErrorResultDto<AuditLogResponseDto>(ResponseCodeConstant.NO_AUDIT_LOG);
        }

        var result = AuditLogResponseParser(auditLogs, filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<AuditLogResponseDto>(result);
    }

    private AuditLogResponseDto AuditLogResponseParser<T>(IEnumerable<AuditLog> auditLogs, int pageNum, int pageSize, int totalRecords)
    {
        try
        {
            var auditLogInfos = auditLogs.Select(log => new EntityChangeResponseDto
            {
                AuditLogId = log.AuditLogUid.ToString(),
                EntityId = log.EntityId,
                Action = log.Action.ToUpper(),
                ChangesList = GetValueChanges<T>(log),
                ModifiedTime = log.Timestamp,
                ModifiedAccountId = log.AccountUid.ToString(),
                ModifiedAccountName = log.Account?.UserInfo?.Name
            });

            return new AuditLogResponseDto
            {
                EntityChanges = auditLogInfos,
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = totalRecords
            };
        }
        catch (BusinessException e)
        {
            logger.Error(e, "Error parsing audit logs");
            return new AuditLogResponseDto();
        }
    }

    private AuditLogResponseDto AuditLogResponseParser(IEnumerable<AuditLog> auditLogs, int pageNum, int pageSize, int totalRecords)
    {
        try
        {
            var auditLogInfos = auditLogs.Select(log => new EntityChangeResponseDto
            {
                AuditLogId = log.AuditLogUid.ToString(),
                EntityId = log.EntityId,
                Action = log.Action.ToUpper(),
                ChangesList = GetValueChanges(log),
                ModifiedTime = log.Timestamp,
                ModifiedAccountId = log.AccountUid.ToString(),
                ModifiedAccountName = log.Account?.UserInfo?.Name
            });

            return new AuditLogResponseDto
            {
                EntityChanges = auditLogInfos,
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = totalRecords
            };
        }
        catch (BusinessException e)
        {
            logger.Error(e, "Error parsing audit logs");
            return new AuditLogResponseDto();
        }
    }

    private IEnumerable<ValueChanges> GetValueChanges<T>(AuditLog log)
    {
        try
        {
            var oldObject = JsonConvert.DeserializeObject<T>(log.OldValues, jsonSerializerSettings);
            var newObject = JsonConvert.DeserializeObject<T>(log.NewValues, jsonSerializerSettings);

            var nonVirtualProperties = typeof(T).GetProperties()
                .Where(p => p.GetCustomAttributes(typeof(AuditPropertyAttribute), true).Any())
                .Select(p => p.Name)
                .ToList();

            var oldValuesDict = oldObject?.GetType().GetProperties().ToDictionary(p => p.Name, p => p.GetValue(oldObject)) ?? [];
            var newValuesDict = newObject?.GetType().GetProperties().ToDictionary(p => p.Name, p => p.GetValue(newObject)) ?? [];

            var changes = nonVirtualProperties
            .Where(propertyName => !IsBothNull(oldValuesDict.GetValueOrDefault(propertyName), newValuesDict.GetValueOrDefault(propertyName)))
            .Where(propertyName => !ObjectHelper.CompareObjects(oldValuesDict.GetValueOrDefault(propertyName), newValuesDict.GetValueOrDefault(propertyName)))
            .Select(propertyName =>
            {
                var oldValue = oldValuesDict.GetValueOrDefault(propertyName);
                var newValue = newValuesDict.GetValueOrDefault(propertyName);

                return new ValueChanges
                {
                    FieldName = propertyName,
                    ValueBefore = oldValue,
                    ValueAfter = newValue
                };
            }).ToList();

            return changes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting value changes");
            return [];
        }
    }

    private bool IsBothNull(object? value1, object? value2)
    {
        return value1 is null && value2 is null;
    }

    private IEnumerable<ValueChanges> GetValueChanges(AuditLog log)
    {
        try
        {
            var oldValues = JsonConvert.DeserializeObject<Dictionary<string, object?>>(log.OldValues) ?? [];
            var newValues = JsonConvert.DeserializeObject<Dictionary<string, object?>>(log.NewValues) ?? [];

            var allProperties = oldValues.Keys.Union(newValues.Keys).Distinct();

            var changes = allProperties
            .Where(propertyName => !propertyName.StartsWith("Created") && !propertyName.StartsWith("LastModified"))
            .Where(propertyName => oldValues.GetValueOrDefault(propertyName) != newValues.GetValueOrDefault(propertyName))
            .Where(propertyName => oldValues.GetValueOrDefault(propertyName) is not null && newValues.GetValueOrDefault(propertyName) is not null)
            .Select(propertyName => new ValueChanges
            {
                FieldName = propertyName,
                ValueBefore = oldValues.GetValueOrDefault(propertyName),
                ValueAfter = newValues.GetValueOrDefault(propertyName)
            });

            return changes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting value changes");
            return [];
        }
    }
}