namespace Kantoku.Persistence.Config
{
    public class ApplicationDbConfig
    {
        public required string DbHost { get; set; }
        public required string DbPort { get; set; }
        public required string DbName { get; set; }
        public required string Schema { get; set; }
        public required string Username { get; set; }
        public required string Password { get; set; }

        public string BuildConnectionString()
        {
            return $"Server={DbHost};Database={DbName};Port={DbPort};User Id={Username};Password={Password};Include Error Detail=true";
        }
    }
}
