using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class RoleConfiguration(string schema) : IEntityTypeConfiguration<Role>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("role", Schema);

        builder.HasKey(e => e.RoleUid).HasName("role_pkey");

        builder.Property(e => e.RoleUid)
            .HasColumnName("role_uid");
        builder.Property(e => e.RoleName)
            .HasColumnName("role_name");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.StructureUid)
            .HasColumnName("structure_uid");
        builder.Property(e => e.<PERSON>)
            .HasDefaultValue(false)
            .HasColumnName("is_hidden");
        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false)
            .HasColumnName("is_default");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.Description)
            .HasColumnName("description");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Roles)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("role_org_id_fkey");

        builder.HasOne(d => d.Structure)
            .WithMany(p => p.Roles)
            .HasForeignKey(d => d.StructureUid)
            .HasConstraintName("role_structure_id_fkey");
    }
} 