namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for payment type queries - contains only business logic, no HTTP concerns
/// </summary>
public class PaymentTypeFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in payment type name or code
    /// </summary>
    public string? Keyword { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}
