using Hangfire;
using Hangfire.Storage;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Jobs;
using Microsoft.Extensions.Logging;

namespace Kantoku.Processor.Controllers;

/// <summary>
/// Controller for managing Hangfire jobs dynamically
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class JobManagementController : ControllerBase
{
    private readonly ILogger<JobManagementController> _logger;

    public JobManagementController(ILogger<JobManagementController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get all recurring jobs
    /// </summary>
    [HttpGet("recurring-jobs")]
    public IActionResult GetRecurringJobs()
    {
        try
        {
            using var connection = JobStorage.Current.GetConnection();
            var recurringJobs = connection.GetRecurringJobs();
            var jobList = recurringJobs.Select(job => new
            {
                Id = job.Id,
                Cron = job.Cron,
                NextExecution = job.NextExecution,
                LastExecution = job.LastExecution,
                CreatedAt = job.CreatedAt,
                LastJobId = job.LastJobId,
                LastJobState = job.LastJobState,
                job.Error
            });

            return Ok(jobList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving recurring jobs");
            return StatusCode(500, "Error retrieving recurring jobs");
        }
    }

    /// <summary>
    /// Update a recurring job's cron expression
    /// </summary>
    [HttpPut("recurring-jobs/{jobId}/cron")]
    public IActionResult UpdateJobCron(string jobId, [FromBody] UpdateCronRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.CronExpression))
            {
                return BadRequest("Cron expression is required");
            }

            // Validate cron expression
            if (!IsValidCronExpression(request.CronExpression))
            {
                return BadRequest("Invalid cron expression");
            }

            // Update the job based on jobId
            switch (jobId.ToLower())
            {
                case "auto-checkout-job":
                    RecurringJob.AddOrUpdate<AutoCheckoutHangfireJob>(
                        jobId,
                        job => job.ExecuteHangfireJob(null!, CancellationToken.None),
                        request.CronExpression);
                    break;

                case "checkout-reminder-job":
                    RecurringJob.AddOrUpdate<CheckOutReminderHangfireJob>(
                        jobId,
                        job => job.ExecuteHangfireJob(null!, CancellationToken.None),
                        request.CronExpression);
                    break;

                case "employee-cost-calculate-job":
                    RecurringJob.AddOrUpdate<EmployeeCostCalculateHangfireJob>(
                        jobId,
                        job => job.ExecuteHangfireJob(null!, CancellationToken.None),
                        request.CronExpression);
                    break;

                default:
                    return NotFound($"Job with ID '{jobId}' not found");
            }

            _logger.LogInformation("Updated cron expression for job {JobId} to {CronExpression}", jobId, request.CronExpression);
            return Ok(new { Message = $"Job '{jobId}' cron expression updated successfully", request.CronExpression });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cron expression for job {JobId}", jobId);
            return StatusCode(500, "Error updating job cron expression");
        }
    }

    /// <summary>
    /// Trigger a job immediately
    /// </summary>
    [HttpPost("recurring-jobs/{jobId}/trigger")]
    public IActionResult TriggerJob(string jobId)
    {
        try
        {
            switch (jobId.ToLower())
            {
                case "auto-checkout-job":
                    BackgroundJob.Enqueue<AutoCheckoutHangfireJob>(job => job.ExecuteHangfireJob(null!, CancellationToken.None));
                    break;

                case "checkout-reminder-job":
                    BackgroundJob.Enqueue<CheckOutReminderHangfireJob>(job => job.ExecuteHangfireJob(null!, CancellationToken.None));
                    break;

                case "employee-cost-calculate-job":
                    BackgroundJob.Enqueue<EmployeeCostCalculateHangfireJob>(job => job.ExecuteHangfireJob(null!, CancellationToken.None));
                    break;

                default:
                    return NotFound($"Job with ID '{jobId}' not found");
            }

            _logger.LogInformation("Triggered job {JobId} manually", jobId);
            return Ok(new { Message = $"Job '{jobId}' triggered successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering job {JobId}", jobId);
            return StatusCode(500, "Error triggering job");
        }
    }

    /// <summary>
    /// Enable or disable a recurring job
    /// </summary>
    [HttpPut("recurring-jobs/{jobId}/status")]
    public IActionResult UpdateJobStatus(string jobId, [FromBody] UpdateJobStatusRequest request)
    {
        try
        {
            if (request.Enabled)
            {
                // Re-enable the job with its current cron expression
                using var connection = JobStorage.Current.GetConnection();
                var recurringJobs = connection.GetRecurringJobs();
                var job = recurringJobs.FirstOrDefault(j => j.Id.Equals(jobId, StringComparison.OrdinalIgnoreCase));

                if (job == null)
                {
                    return NotFound($"Job with ID '{jobId}' not found");
                }

                // Re-add the job to enable it
                UpdateJobCron(jobId, new UpdateCronRequest { CronExpression = job.Cron });
            }
            else
            {
                // Remove the job to disable it
                RecurringJob.RemoveIfExists(jobId);
            }

            _logger.LogInformation("Updated status for job {JobId} to {Status}", jobId, request.Enabled ? "Enabled" : "Disabled");
            return Ok(new { Message = $"Job '{jobId}' {(request.Enabled ? "enabled" : "disabled")} successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status for job {JobId}", jobId);
            return StatusCode(500, "Error updating job status");
        }
    }

    /// <summary>
    /// Get predefined cron expression templates
    /// </summary>
    [HttpGet("cron-templates")]
    public IActionResult GetCronTemplates()
    {
        var templates = new[]
        {
            new { Name = "Every minute", Expression = "* * * * *", Description = "Runs every minute" },
            new { Name = "Every 5 minutes", Expression = "*/5 * * * *", Description = "Runs every 5 minutes" },
            new { Name = "Every 15 minutes", Expression = "*/15 * * * *", Description = "Runs every 15 minutes" },
            new { Name = "Every 30 minutes", Expression = "*/30 * * * *", Description = "Runs every 30 minutes" },
            new { Name = "Every hour", Expression = "0 * * * *", Description = "Runs every hour at minute 0" },
            new { Name = "Every 2 hours", Expression = "0 */2 * * *", Description = "Runs every 2 hours" },
            new { Name = "Every 6 hours", Expression = "0 */6 * * *", Description = "Runs every 6 hours" },
            new { Name = "Every 12 hours", Expression = "0 */12 * * *", Description = "Runs every 12 hours" },
            new { Name = "Daily at midnight", Expression = "0 0 * * *", Description = "Runs daily at 00:00" },
            new { Name = "Daily at 2 AM", Expression = "0 2 * * *", Description = "Runs daily at 02:00" },
            new { Name = "Weekly (Sunday)", Expression = "0 0 * * 0", Description = "Runs every Sunday at midnight" },
            new { Name = "Monthly (1st)", Expression = "0 0 1 * *", Description = "Runs on the 1st of every month" },
            new { Name = "Weekdays only", Expression = "0 9 * * 1-5", Description = "Runs at 9 AM on weekdays" },
            new { Name = "Business hours", Expression = "0 9-17 * * 1-5", Description = "Runs every hour from 9 AM to 5 PM on weekdays" }
        };

        return Ok(templates);
    }

    private static bool IsValidCronExpression(string cronExpression)
    {
        try
        {
            // Use Hangfire's cron validation
            Cron.Never(); // This will throw if Hangfire cron parsing is not available

            // Basic validation - cron should have 5 parts (minute hour day month dayofweek)
            var parts = cronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            return parts.Length == 5;
        }
        catch
        {
            return false;
        }
    }
}
