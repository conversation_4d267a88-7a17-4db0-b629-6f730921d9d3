namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for employee leave queries - contains only business logic, no HTTP concerns
/// </summary>
public class EmployeeLeaveFilter : BaseFilter
{
    /// <summary>
    /// Query from date (yyyy-MM-dd format)
    /// </summary>
    public string? QueryFrom { get; set; }

    /// <summary>
    /// Query to date (yyyy-MM-dd format)
    /// </summary>
    public string? QueryTo { get; set; }

    /// <summary>
    /// User information to search for
    /// </summary>
    public string? UserInfo { get; set; }

    /// <summary>
    /// Minimum base leave days
    /// </summary>
    public float? BaseLeaveMin { get; set; }

    /// <summary>
    /// Maximum base leave days
    /// </summary>
    public float? BaseLeaveMax { get; set; }

    /// <summary>
    /// Base leave valid from date (yyyy-MM-dd format)
    /// </summary>
    public string? BaseLeaveValidFrom { get; set; }

    /// <summary>
    /// Base leave valid to date (yyyy-MM-dd format)
    /// </summary>
    public string? BaseLeaveValidTo { get; set; }

    /// <summary>
    /// Minimum last leave remaining days
    /// </summary>
    public float? LastLeaveRemainingMin { get; set; }

    /// <summary>
    /// Maximum last leave remaining days
    /// </summary>
    public float? LastLeaveRemainingMax { get; set; }

    /// <summary>
    /// Last leave remaining valid from date (yyyy-MM-dd format)
    /// </summary>
    public string? LastLeaveRemainingValidFrom { get; set; }

    /// <summary>
    /// Last leave remaining valid to date (yyyy-MM-dd format)
    /// </summary>
    public string? LastLeaveRemainingValidTo { get; set; }

    /// <summary>
    /// Minimum used leave days
    /// </summary>
    public float? UsedMin { get; set; }

    /// <summary>
    /// Maximum used leave days
    /// </summary>
    public float? UsedMax { get; set; }

    /// <summary>
    /// Minimum personal used leave days
    /// </summary>
    public float? PersonalUsedMin { get; set; }

    /// <summary>
    /// Maximum personal used leave days
    /// </summary>
    public float? PersonalUsedMax { get; set; }

    /// <summary>
    /// Minimum organization used leave days
    /// </summary>
    public float? OrgUsedMin { get; set; }

    /// <summary>
    /// Maximum organization used leave days
    /// </summary>
    public float? OrgUsedMax { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize user info
        if (!string.IsNullOrWhiteSpace(UserInfo))
        {
            UserInfo = UserInfo.Trim();
        }

        // Validate date formats
        QueryFrom = ValidateDate(QueryFrom);
        QueryTo = ValidateDate(QueryTo);
        BaseLeaveValidFrom = ValidateDate(BaseLeaveValidFrom);
        BaseLeaveValidTo = ValidateDate(BaseLeaveValidTo);
        LastLeaveRemainingValidFrom = ValidateDate(LastLeaveRemainingValidFrom);
        LastLeaveRemainingValidTo = ValidateDate(LastLeaveRemainingValidTo);

        // Validate numeric ranges
        (BaseLeaveMin, BaseLeaveMax) = ValidateRange(BaseLeaveMin, BaseLeaveMax);
        (LastLeaveRemainingMin, LastLeaveRemainingMax) = ValidateRange(LastLeaveRemainingMin, LastLeaveRemainingMax);
        (UsedMin, UsedMax) = ValidateRange(UsedMin, UsedMax);
        (PersonalUsedMin, PersonalUsedMax) = ValidateRange(PersonalUsedMin, PersonalUsedMax);
        (OrgUsedMin, OrgUsedMax) = ValidateRange(OrgUsedMin, OrgUsedMax);
    }

    private static string? ValidateDate(string? dateString)
    {
        if (!string.IsNullOrWhiteSpace(dateString) && !DateTime.TryParse(dateString, out _))
        {
            return null; // Invalid date, ignore
        }
        return dateString;
    }

    private static (float?, float?) ValidateRange(float? min, float? max)
    {
        if (min.HasValue && max.HasValue && min.Value > max.Value)
        {
            // Swap if min > max
            (min, max) = (max, min);
        }

        // Ensure non-negative values
        if (min.HasValue && min.Value < 0) min = 0;
        if (max.HasValue && max.Value < 0) max = 0;

        return (min, max);
    }
}
