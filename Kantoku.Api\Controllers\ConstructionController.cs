using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Construction.Request;
using Kantoku.Api.Dtos.Construction.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ConstructionController : BaseController
{
    private readonly IConstructionService constructionService;
    private readonly IConstructionCostService constructionCostService;
    private readonly IAuditLogService auditLogService;

    public ConstructionController(IConstructionService constructionService,
        IConstructionCostService constructionCostService,
        IAuditLogService auditLogService,
        ITResponseFactory responseFactory) : base(responseFactory)
    {
        this.constructionService = constructionService;
        this.constructionCostService = constructionCostService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Retrieves detailed information for a specific construction by its ID
    /// </summary>
    /// <param name="id">The unique identifier of the construction to retrieve</param>
    /// <returns>Detailed information about the specified construction</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ConstructionResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ConstructionResponseDto>();

            var result = await constructionService.GetConstructionById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves all constructions associated with a specific project
    /// </summary>
    /// <param name="projectId">The unique identifier of the project to get constructions for</param>
    /// <returns>A list of construction details for the specified project</returns>
    [HttpGet("project/{projectId}")]
    public async Task<GeneralResponse<ConstructionsResponseDto>> GetByProjectId([FromRoute] Guid projectId)
    {
        try
        {
            var result = await constructionService.GetConstructionsByProjectId(projectId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves a list of simple construction details for a specific project
    /// </summary>
    /// <param name="projectId">The unique identifier of the project to get constructions for</param>
    /// <returns>A list of construction details for the specified project</returns>
    [HttpGet("project/{projectId}/simple")]
    public async Task<GeneralResponse<ConstructionSimpleResponseDto>> GetSimpleByProjectId([FromRoute] Guid projectId)
    {
        try
        {
            var result = await constructionService.GetConstructionSimpleByProjectId(projectId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionSimpleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves overview information for all constructions associated with a specific project
    /// </summary>
    /// <param name="projectId">The unique identifier of the project to retrieve</param>
    /// <returns>Overview information about the specified project</returns>
    [HttpGet("project/{projectId}/overview")]
    public async Task<GeneralResponse<ConstructionOverviewResponseDto>> GetOverviewByProjectId([FromRoute] Guid projectId)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ConstructionOverviewResponseDto>();

            var result = await constructionService.GetConstructionOverviewByProjectId(projectId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionOverviewResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Creates a new sub-construction within a specified project
    /// </summary>
    /// <param name="projectId">The ID of the project to create the construction under</param>
    /// <param name="requestDto">The construction details including name, description, and other required information</param>
    /// <returns>The newly created construction details</returns>
    [HttpPost("project/{projectId}")]
    public async Task<GeneralResponse<ConstructionResponseDto>> Create([FromRoute] Guid projectId, [FromBody] CreateConstructionRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ConstructionResponseDto>();

            var result = await constructionService.CreateConstruction(projectId, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Updates an existing construction's information
    /// </summary>
    /// <param name="id">The ID of the construction to update</param>
    /// <param name="requestDto">The updated construction details</param>
    /// <returns>The updated construction information</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<ConstructionResponseDto>> Update([FromRoute] Guid id, [FromBody] UpdateConstructionRequestDto requestDto)
    {
        try
        {
            var result = await constructionService.UpdateConstruction(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Deletes a specific construction from the system
    /// </summary>
    /// <param name="id">The ID of the construction to delete</param>
    /// <returns>Success response with no content</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            var result = await constructionService.DeleteConstruction(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves the audit log history for a specific construction
    /// </summary>
    /// <param name="id">The ID of the construction to get logs for</param>
    /// <param name="filter">Filter parameters to narrow down the audit log results</param>
    /// <returns>List of audit log entries for the specified construction</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Construction>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
