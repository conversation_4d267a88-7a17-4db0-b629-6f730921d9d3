using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Services;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Notification;
using Kantoku.Api.Dtos.Device.Request;

namespace Kantoku.Api.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class NotificationController : BaseController
{
    private readonly INotificationService _notificationService;

    public NotificationController(INotificationService notificationService, ITResponseFactory responseFactory)
        : base(responseFactory)
    {
        _notificationService = notificationService;
    }

    /// <summary>
    /// Create a new notification   
    /// </summary>
    /// <param name="notificationDto">The notification to create</param>
    /// <returns>The created notification</returns>
    [HttpPost]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<Guid>> CreateNotification([FromBody] CreateNotificationRequestDto notificationDto)
    {
        try
        {
            var notification = await _notificationService.CreateNotification(notificationDto);
            return SuccessFromResult(notification);
        }
        catch (BusinessException ex)
        {
            return Fail<Guid>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Update a notification
    /// </summary>
    /// <param name="notificationId">The ID of the notification to update</param>
    /// <param name="notificationDto">The updated notification</param>
    /// <returns>The updated notification</returns>
    [HttpPut("{notificationId}")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<bool>> UpdateNotification([FromRoute] Guid notificationId, [FromBody] UpdateNotificationRequestDto notificationDto)
    {
        try
        {
            var notification = await _notificationService.UpdateNotification(notificationId, notificationDto);
            return SuccessFromResult(notification);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>   
    /// Publish a notification
    /// </summary>
    /// <param name="token">The token of the device to publish the notification to</param>
    /// <param name="notification">The notification to publish</param>
    /// <returns>True if the notification was published, false otherwise</returns>
    [HttpPost("token/{token}")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<bool>> PublishNotification([FromRoute] string token, [FromBody] NotificationDto notification)
    {
        try
        {
            var result = await _notificationService.PublishNotification(token, notification);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>   
    /// Publish a notification
    /// </summary>
    /// <param name="notificationId">The ID of the notification to publish</param>
    /// <param name="targetId">The ID of the target to publish the notification to</param>
    /// <returns>True if the notification was published, false otherwise</returns>
    [HttpPost("{notificationId}/publish/{targetId}")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<bool>> PublishNotification([FromRoute] Guid notificationId, [FromRoute] Guid targetId)
    {
        try
        {
            var result = await _notificationService.PublishNotification(notificationId, targetId);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a notification
    /// </summary>
    /// <param name="notificationId">The ID of the notification to delete</param>
    /// <returns>True if the notification was deleted, false otherwise</returns>
    [HttpDelete("{notificationId}")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<bool>> DeleteNotification([FromRoute] Guid notificationId)
    {
        try
        {
            var result = await _notificationService.DeleteNotification(notificationId);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get notifications
    /// </summary>
    /// <param name="filter">The filter to apply to the notifications</param>
    /// <returns>The notifications</returns>
    [HttpGet]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<NotificationsResponseDto>> GetNotifications([FromQuery] OrgNotificationFilter filter)
    {
        try
        {
            var notifications = await _notificationService.GetByFilter(filter);
            return SuccessFromResult(notifications);
        }
        catch (BusinessException ex)
        {
            return Fail<NotificationsResponseDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get notifications
    /// </summary>
    /// <param name="notificationId">The ID of the notification to get</param>
    /// <returns>The notifications</returns>
    [HttpGet("{notificationId}")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<NotificationResponseDto>> GetNotifications([FromRoute] Guid notificationId)
    {
        try
        {
            var notifications = await _notificationService.GetById(notificationId);
            return SuccessFromResult(notifications);
        }
        catch (BusinessException ex)
        {
            return Fail<NotificationResponseDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get employee notifications
    /// </summary>
    /// <param name="filter">The filter to apply to the notifications</param>
    /// <returns>The notifications</returns>
    [HttpGet("employee")]
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    public async Task<GeneralResponse<EmployeeNotificationsResponseDto>> GetEmployeeNotifications([FromQuery] EmployeeNotificationFilter filter)
    {
        try
        {
            var notifications = await _notificationService.GetEmployeeNotifications(filter);
            return SuccessFromResult(notifications);
        }
        catch (BusinessException ex)
        {
            return Fail<EmployeeNotificationsResponseDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Mark a notification as read
    /// </summary>
    /// <param name="notificationId">The ID of the notification to mark as read</param>
    /// <returns>True if the notification was marked as read, false otherwise</returns>
    [HttpPut("{notificationId}/read")]
    public async Task<GeneralResponse<bool>> MarkAsRead([FromRoute] Guid notificationId)
    {
        try
        {
            var result = await _notificationService.MarkNotificationAsRead(notificationId);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Mark all notifications as read
    /// </summary>
    /// <returns>True if the notifications were marked as read, false otherwise</returns>
    [HttpPut("notification/read")]
    public async Task<GeneralResponse<bool>> MarkAllAsRead()
    {
        try
        {
            var result = await _notificationService.MarkAllNotificationsAsRead();
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Mark a notification as unread
    /// </summary>
    /// <param name="notificationId">The ID of the notification to mark as unread</param>
    /// <returns>True if the notification was marked as unread, false otherwise</returns>
    [HttpPut("{notificationId}/unread")]
    public async Task<GeneralResponse<bool>> MarkAsUnread([FromRoute] Guid notificationId)
    {
        try
        {
            var result = await _notificationService.MarkNotificationsAsUnread(notificationId);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Register a device token
    /// </summary>
    /// <param name="dto">The device token to register</param>
    /// <returns>True if the device token was registered, false otherwise</returns>
    [HttpPost("device/register")]
    public async Task<GeneralResponse<bool>> RegisterDeviceToken([FromBody] DeviceRequestDto dto)
    {
        try
        {
            var result = await _notificationService.RegisterDeviceToken(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Unregister a device token
    /// </summary>
    /// <param name="firebaseToken">The firebase token of the device to unregister</param>
    /// <returns>True if the device token was unregistered, false otherwise</returns>
    [HttpDelete("device/unregister/{firebaseToken}")]
    public async Task<GeneralResponse<bool>> UnregisterDeviceToken([FromRoute] string firebaseToken)
    {
        try
        {
            var result = await _notificationService.UnregisterDeviceToken(firebaseToken);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }
}