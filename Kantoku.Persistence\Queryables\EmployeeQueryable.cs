using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class EmployeeQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedUserInfo { get; set; } = false;
    public bool IncludedOrg { get; set; } = false;
    public bool IncludedPosition { get; set; } = false;
    public bool IncludedStructure { get; set; } = false;
    public bool IncludedEmployeeRoles { get; set; } = false;
    public bool IncludedEmployeeShifts { get; set; } = false;
    public bool IncludedEmployeeLeaves { get; set; } = false;
    public bool IncludedManagedProjects { get; set; } = false;
    public bool IncludedEmpContracts { get; set; } = false;
    public bool IncludedMonthlyReports { get; set; } = false;
    public bool IncludedEmployeeRanks { get; set; } = false;
    public bool IncludedEmployeeCosts { get; set; } = false;

    public EmployeeQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

}

public interface IEmployeeQueryable
{
    IQueryable<Employee> GetEmployeeQuery(
        EmployeeQueryableOptions options
    );

    IQueryable<Employee> GetEmployeesQueryIncluded(
        EmployeeQueryableOptions options,
        IQueryable<Employee>? query = null
    );

    IQueryable<Employee> GetEmployeesQueryFiltered(
        EmployeeFilter filter,
        EmployeeQueryableOptions options,
        IQueryable<Employee>? query = null
    );
}

public class EmployeeQueryable(ApplicationDbContext context) :
    BaseQueryable<Employee>(context), IEmployeeQueryable
{
    public IQueryable<Employee> GetEmployeeQuery(
        EmployeeQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(e => e.IsHidden == false && e.IsDeleted == false);
        return query;
    }

    public IQueryable<Employee> GetEmployeesQueryIncluded(
        EmployeeQueryableOptions options,
        IQueryable<Employee>? query = null
    )
    {
        query ??= GetEmployeeQuery(options);
        query = query.Include(e => e.Status);
        if (options.IncludedUserInfo || options.IncludedAll)
        {
            query = query.Include(e => e.Account)
                .ThenInclude(a => a.UserInfo);
        }
        if (options.IncludedOrg || options.IncludedAll)
        {
            query = query.Include(e => e.Org);
        }
        if (options.IncludedPosition || options.IncludedAll)
        {
            query = query.Include(e => e.Position);
        }
        if (options.IncludedStructure || options.IncludedAll)
        {
            query = query.Include(e => e.Structure);
        }
        if (options.IncludedEmployeeRoles || options.IncludedAll)
        {
            query = query.Include(e => e.EmployeeRoles)
                .ThenInclude(er => er.Role);
        }
        if (options.IncludedEmployeeShifts || options.IncludedAll)
        {
            query = query.Include(e => e.EmployeeShifts);
        }
        if (options.IncludedEmployeeLeaves || options.IncludedAll)
        {
            query = query.Include(e => e.EmployeeLeaves);
        }
        if (options.IncludedManagedProjects || options.IncludedAll)
        {
            query = query.Include(e => e.ProjectManagers)
                .ThenInclude(pm => pm.Project);
        }
        if (options.IncludedEmpContracts || options.IncludedAll)
        {
            query = query.Include(e => e.EmpContracts);
        }
        if (options.IncludedMonthlyReports || options.IncludedAll)
        {
            query = query.Include(e => e.MonthlyReportsAuthor);
        }
        if (options.IncludedEmployeeRanks)
        {
            query = query.Include(e => e.Ranking);
        }
        if (options.IncludedEmployeeCosts)
        {
            query = query.Include(e => e.EmployeeCosts);
        }
        return query;
    }

    public IQueryable<Employee> GetEmployeesQueryFiltered(
        EmployeeFilter filter,
        EmployeeQueryableOptions options,
        IQueryable<Employee>? query = null
    )
    {
        query ??= GetEmployeesQueryIncluded(options);
        if (!string.IsNullOrEmpty(filter.Keyword))
        {
            query = query.Where(e => e.EmployeeName.Contains(filter.Keyword)
                || e.EmployeeCode.Contains(filter.Keyword));
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(e => e.OrgUid == filter.OrgId);
        }
        if (filter.StructureId is not null && filter.StructureId != Guid.Empty)
        {
            query = query.Where(e => e.StructureUid == filter.StructureId);
        }
        if (!string.IsNullOrEmpty(filter.WorkingStatus))
        {
            query = query.Where(e => e.WorkingStatus.Equals(filter.WorkingStatus));
        }
        if (filter.PositionId is not null && filter.PositionId != Guid.Empty)
        {
            query = query.Where(e => e.PositionUid == filter.PositionId);
        }
        if (filter.RankId is not null && filter.RankId != Guid.Empty)
        {
            query = query.Where(e => e.RankingUid == filter.RankId);
        }
        if (filter.EmployeeType.HasValue)
        {
            query = query.Where(e => e.EmployeeType == filter.EmployeeType.Value);
        }
        if (filter.HasApprovalAuthority.HasValue)
        {
            query = query.Where(e => e.HasApprovalAuthority == filter.HasApprovalAuthority.Value);
        }

        return query;
    }
}
