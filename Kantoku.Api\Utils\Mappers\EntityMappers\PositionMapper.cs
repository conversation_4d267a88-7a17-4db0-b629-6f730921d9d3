using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Position.Request;
using Kantoku.Api.Dtos.Position.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class PositionMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Position entity to a PositionResponseDto
    /// </summary>
    /// <param name="position">The Position entity to map</param>
    /// <returns>A PositionResponseDto representing the Position entity</returns>
    public static PositionResponseDto ToPositionResponseDto(this Position position)
    {
        if (position == null)
            return new PositionResponseDto();

        return new PositionResponseDto
        {
            PositionId = position.PositionUid.ToString(),
            PositionCode = position.PositionCode,
            PositionName = position.PositionName,
            Description = position.Description,
            CreateTime = DateTimeHelper.ParseToLocalTime(position.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(position.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Maps a Position entity to a PositionInfoDto
    /// </summary>
    /// <param name="position">The Position entity to map</param>
    /// <returns>A PositionInfoDto representing the Position entity</returns>   
    public static PositionInfoDto ToPositionInfoDto(this Position position)
    {
        if (position == null)
            return new PositionInfoDto();

        return new PositionInfoDto
        {
            PositionId = position.PositionUid.ToString(),
            PositionName = position.PositionName
        };
    }

    /// <summary>
    /// Maps a collection of Position entities to a PositionsResponseDto
    /// </summary>
    /// <param name="positions">The collection of Position entities to map</param>
    /// <param name="pageNum">The current page number</param>
    /// <param name="pageSize">The number of items per page</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns>A PositionsResponseDto representing the collection of Position entities</returns>
    public static PositionsResponseDto ToPositionsResponseDto(
        this IEnumerable<Position> positions,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (positions == null)
            return new PositionsResponseDto();

        return new PositionsResponseDto
        {
            Items = positions.Select(p => p.ToPositionResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a collection of Position entities to a SimplePositionResponseDto
    /// </summary>      
    /// <param name="positions">The collection of Position entities to map</param>
    /// <param name="pageNum">The current page number</param>
    /// <param name="pageSize">The number of items per page</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns>A SimplePositionResponseDto representing the collection of Position entities</returns>
    public static SimplePositionResponseDto ToSimplePositionResponseDto(
        this IEnumerable<Position> positions,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (positions == null)
            return new SimplePositionResponseDto();

        return new SimplePositionResponseDto
        {
            Items = positions.Select(p => p.ToPositionInfoDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreatePositionRequestDto to a Position entity
    /// </summary>
    /// <param name="dto">The CreatePositionRequestDto to map</param>
    /// <param name="orgUid">The organization ID</param>
    /// <returns>A Position entity representing the CreatePositionRequestDto</returns>
    public static Position? ToEntity(this CreatePositionRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Position
        {
            PositionUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            PositionCode = dto.PositionCode,
            PositionName = dto.PositionName,
            Description = dto.Description,
            IsDeleted = false
        };

        return entity;
    }

    /// <summary>
    /// Updates a Position entity from an UpdatePositionRequestDto
    /// </summary>
    /// <param name="entity">The Position entity to update</param>
    /// <param name="dto">The UpdatePositionRequestDto to update from</param>
    public static void UpdateFromDto(this Position entity, UpdatePositionRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.PositionCode != null)
            entity.PositionCode = dto.PositionCode;

        if (dto.PositionName != null)
            entity.PositionName = dto.PositionName;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
}