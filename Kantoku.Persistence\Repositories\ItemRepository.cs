﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IItemRepository
{
    Task<(IEnumerable<Item>, int)> GetByFilter(DomainFilter.ItemFilter filter, ItemQueryableOptions options);
    Task<Item?> GetById(Guid itemId, ItemQueryableOptions options);
    Task<Item?> Create(Item item, ItemQueryableOptions options);
    Task<Item?> Update(Item item, ItemQueryableOptions options);

    Task<Guid?> Create(Item item);
    Task<bool> Update(Item item);
}

public class ItemRepository : BaseRepository<ItemRepository>, IItemRepository
{
    private readonly IItemQueryable queryable;
    public ItemRepository(
        ApplicationDbContext context,
        IItemQueryable queryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(IEnumerable<Item>, int)> GetByFilter(DomainFilter.ItemFilter filter, ItemQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetItemsQueryFiltered(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(i => i.ItemCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting items list paginated");
            return (new List<Item>(), 0);
        }
    }

    public async Task<Item?> GetById(Guid itemId, ItemQueryableOptions options)
    {
        try
        {
            var query = queryable.GetItemsQueryIncluded(options)
                .Where(h => h.ItemUid == itemId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting item by id");
            return null;
        }
    }

    public async Task<Item?> Create(Item item, ItemQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Items.AddAsync(item);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(item.ItemUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating item");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Item?> Update(Item item, ItemQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Entry(item).State = EntityState.Modified;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(item.ItemUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating item");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(Item item)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Items.AddAsync(item);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return item.ItemUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating item");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Item item)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Entry(item).State = EntityState.Modified;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating item");
            await transaction.RollbackAsync();
            return false;
        }
    }
}
