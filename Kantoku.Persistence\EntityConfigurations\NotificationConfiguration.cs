using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class NotificationConfiguration(string schema) : IEntityTypeConfiguration<Notification>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.HasKey(e => e.NotificationUid)
            .HasName("notification_pkey");

        builder.ToTable("notification", Schema);

        builder.Property(e => e.NotificationUid)
            .HasColumnName("notification_uid");
        builder.Property(e => e.Title)
            .HasColumnName("title");
        builder.Property(e => e.Body)
            .HasColumnName("body");
        builder.Property(e => e.NotificationType)
            .HasColumnName("notification_type");
        builder.Property(e => e.IsDeleted)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Notifications)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("notification_org_id_fkey");
    }
}