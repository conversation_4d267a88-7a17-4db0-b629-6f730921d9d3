using System.Globalization;

namespace Kantoku.SharedKernel.Helpers;

public static class LocaleHelper
{
    public static bool NeedTranslate(CultureInfo? culture)
    {
        if (culture is null) return false;
        return !culture.Name.Equals("ja-JP") && !culture.Name.Equals("ja");
    }

    public static string GetLanguageCode(CultureInfo? culture)
    {
        if (culture is null) return LanguageConstant.JA;
        return CultureInfoMapper.ToLanguage(culture);
    }

    public static TimeZoneInfo GetTimeZone(string? orgTimeZoneId)
    {
        if (string.IsNullOrEmpty(orgTimeZoneId))
        {
            return TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
        }
        return TimeZoneInfo.FindSystemTimeZoneById(orgTimeZoneId);
    }
}
public class LanguageConstant
{
    public const string JA = "JA";
    public const string EN = "EN";
    public const string VI = "VI";
}

public static class CultureInfoMapper
{
    public static string ToLanguage(CultureInfo? culture)
    {
        if (culture is null) return LanguageConstant.JA;
        return culture.Name switch
        {
            "ja-JP" or "ja" => LanguageConstant.JA,
            "vi-VN" or "vi" => LanguageConstant.VI,
            "en-US" or "en" => LanguageConstant.EN,
            _ => LanguageConstant.JA,
        };
    }
}
