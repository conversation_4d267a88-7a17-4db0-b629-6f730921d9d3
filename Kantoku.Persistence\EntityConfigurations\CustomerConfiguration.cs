using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class CustomerConfiguration(string schema) : IEntityTypeConfiguration<Customer>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Customer> builder)
    {
        builder.HasKey(e => e.CustomerUid).HasName("customer_pkey");

        builder.ToTable("customer", Schema);

        builder.Property(e => e.CustomerUid)
            .HasColumnName("customer_uid");
        builder.Property(e => e.CustomerCode)
            .HasColumnName("customer_code");
        builder.Property(e => e.CustomerName)
            .HasColumnName("customer_name");
        builder.Property(e => e.CustomerSubName)
            .HasColumnName("customer_sub_name");
        builder.Property(e => e.CustomerTypeCode)
            .HasColumnName("customer_type_code");
        builder.Property(e => e.CorporateNumber)
            .HasColumnName("corporate_number");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.ContactPerson)
            .HasColumnType("jsonb")
            .HasColumnName("contact_person")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ContactPerson>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ContactPerson>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.Equals(c2)),
                    c => c.GetHashCode(),
                    c => c
                )
            );
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Customers)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("customer_org_id_fkey");

        builder.HasOne(d => d.CustomerType)
            .WithMany(p => p.Customers)
            .HasForeignKey(d => d.CustomerTypeCode)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("customer_customer_type_code_fkey");
    }
} 