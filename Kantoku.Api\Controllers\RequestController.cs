using Kantoku.Api.Dtos.EmployeeRequest.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.EmployeeRequest.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;
using Kantoku.Domain.Models;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class RequestController : BaseController
{
    private readonly IRequestService requestService;
    private readonly IAuditLogService auditLogService;

    public RequestController(
        ITResponseFactory responseFactory,
        IRequestService requestService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.requestService = requestService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get request details by ID
    /// </summary>
    /// <param name="id">Request ID to retrieve</param>
    /// <returns>Request details</returns>
    [HttpGet("{id}")]   
    public async Task<GeneralResponse<RequestInfoResponseDto>> GetRequestInfo([FromRoute] Guid id)
    {
        try
        {
            var res = await requestService.GetRequestById(id);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get requests for current user with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for requests</param>
    /// <returns>List of user's requests</returns>
    [HttpGet("by-au")]
    public async Task<GeneralResponse<RequestsInfoResponseDto>> GetRequestsByUser(
        [FromQuery] RequestFilter filter)
    {
        try
        {
            var res = await requestService.GetRequestsByUser(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestsInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get requests for current user with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for requests</param>
    /// <returns>List of user's requests</returns>
    [HttpGet("by-au/no-pagination")]
    public async Task<GeneralResponse<RequestsInfoResponseDto>> GetRequestsByUserNoPagination(
        [FromQuery] RequestFilter filter)
    {
        try
        {
            filter.PageNum = 1;
            filter.PageSize = int.MaxValue;
            var res = await requestService.GetRequestsByUser(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestsInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get requests pending approval for current approver
    /// </summary>
    /// <param name="filter">Filter parameters for requests</param>
    /// <returns>List of requests pending approval</returns>
    [HttpGet("by-approver")]
    public async Task<GeneralResponse<RequestsInfoResponseDto>> GetApproverRequest(
        [FromQuery] RequestFilter filter)
    {
        try
        {
            var res = await requestService.GetRequestByApprover(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestsInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new request
    /// </summary>
    /// <param name="dto">Request creation data</param>
    /// <returns>Created request details</returns>
    [HttpPost]
    public async Task<GeneralResponse<RequestInfoResponseDto>> CreateRequestAsync([FromBody] CreateEmployeeRequestRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RequestInfoResponseDto>();

            var res = await requestService.CreateRequestAsync(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing request
    /// </summary>
    /// <param name="id">Request ID to update</param>
    /// <param name="dto">Updated request data</param>
    /// <returns>Updated request details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<RequestInfoResponseDto>> UpdateRequestAsync([FromRoute] Guid id, [FromBody] UpdateEmployeeRequestRequestDto dto)
    {
        try
        {
            var res = await requestService.UpdateRequestAsync(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Approve a request
    /// </summary>
    /// <param name="id">Request ID to approve</param>
    /// <returns>Approved request details</returns>
    [HttpPut("{id}/approve")]
    public async Task<GeneralResponse<RequestInfoResponseDto>> ApproveRequest([FromRoute] Guid id)
    {
        try
        {
            var res = await requestService.ProcessRequest(id, true);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Reject a request
    /// </summary>
    /// <param name="id">Request ID to reject</param>
    /// <param name="dto">Rejection details</param>
    /// <returns>Rejected request details</returns>
    [HttpPut("{id}/reject")]
    public async Task<GeneralResponse<RequestInfoResponseDto>> RejectRequest([FromRoute] Guid id, [FromBody] RejectEmployeeRequestRequestDto dto)
    {
        try
        {
            var res = await requestService.ProcessRequest(id, false, dto.RejectReason);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Cancel a request
    /// </summary>
    /// <param name="id">Request ID to cancel</param>
    /// <returns>Cancelled request details</returns>
    [HttpPut("{id}/cancel")]
    public async Task<GeneralResponse<RequestInfoResponseDto>> CancelRequest([FromRoute] Guid id)
    {
        try
        {
            var res = await requestService.CancelRequest(id);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RequestInfoResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a request
    /// </summary>
    /// <param name="id">Request ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteRequest([FromRoute] Guid id)
    {
        try
        {
            await requestService.DeleteRequest(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a request
    /// </summary>
    /// <param name="id">Request ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetRequestLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {   
            var result = await auditLogService.GetAuditLogsByEntity<Request>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}