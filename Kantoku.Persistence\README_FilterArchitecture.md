# Filter Architecture

## Problem
The persistence layer was directly using API-specific filter classes that contained HTTP-specific attributes like `[FromQuery]`, violating clean architecture principles by coupling the persistence layer to HTTP concerns.

## Solution
We've implemented a **Domain Filter Pattern** that separates API filters from domain filters through a mapping layer.

## Architecture

### 1. Domain Filters (Persistence Layer)
Located in `Kantoku.Persistence.Filters`
- **Pure business logic** - No HTTP attributes
- **Validation logic** - Built-in data validation and normalization
- **Domain-focused** - Represents business filtering requirements

```csharp
namespace Kantoku.Persistence.Filters;

public class EmployeeFilter : BaseFilter
{
    public string? Keyword { get; set; }
    public bool? EmployeeType { get; set; }
    public Guid? StructureId { get; set; }
    // ... other properties

    public override void Validate()
    {
        base.Validate();
        // Domain-specific validation logic
    }
}
```

### 2. API Filters (API Layer)
Located in `Kantoku.Api.Filters.Domains`
- **HTTP-specific** - Contains `[FromQuery]` attributes
- **Request binding** - Handles HTTP request parameter binding
- **API-focused** - Represents HTTP request structure

```csharp
namespace Kantoku.Api.Filters.Domains;

public class EmployeeFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }
    
    [FromQuery(Name = "employeeType")]
    public bool? EmployeeType { get; set; }
    // ... other properties with HTTP attributes
}
```

### 3. Filter Mappers (API Layer)
Located in `Kantoku.Api.Mappers`
- **Translation layer** - Maps API filters to domain filters
- **Validation** - Ensures data integrity during mapping
- **Abstraction** - Provides clean separation between layers

```csharp
public class EmployeeFilterMapper : FilterMapperBase<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeFilter apiFilter, DomainFilters.EmployeeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.EmployeeType = apiFilter.EmployeeType;
        // ... map other properties
    }
}
```

## Usage Patterns

### 1. API Controllers
```csharp
[HttpGet]
public async Task<IActionResult> GetEmployees([FromQuery] ApiFilters.EmployeeFilter apiFilter)
{
    // Map API filter to domain filter
    var domainFilter = apiFilter.ToDomain();
    
    // Use domain filter in repository
    var (employees, count) = await _employeeRepository.GetByFilter(domainFilter, options);
    
    return Ok(new { Data = employees, TotalCount = count });
}
```

### 2. Business Services
```csharp
public async Task<IEnumerable<Employee>> GetActiveEmployees()
{
    // Create domain filter directly
    var filter = new DomainFilters.EmployeeFilter
    {
        WorkingStatus = "ACTIVE",
        PageSize = 100
    };
    
    var (employees, _) = await _employeeRepository.GetByFilter(filter, options);
    return employees;
}
```

### 3. Background Jobs
```csharp
public async Task ProcessEmployees()
{
    // Background jobs use domain filters directly
    var filter = new DomainFilters.EmployeeFilter
    {
        WorkingStatus = "ACTIVE",
        PageSize = 1000
    };
    
    var (employees, _) = await _employeeRepository.GetByFilter(filter, options);
    // Process employees...
}
```

### 4. Unit Testing
```csharp
[Test]
public async Task TestEmployeeFiltering()
{
    // Tests use domain filters directly
    var filter = new DomainFilters.EmployeeFilter
    {
        Keyword = "John",
        EmployeeType = true
    };
    
    var (employees, count) = await repository.GetByFilter(filter, options);
    Assert.AreEqual(1, count);
}
```

## Migration Guide

### Step 1: Create Domain Filters
**Before:**
```csharp
// In Kantoku.Persistence.Filters.Domains
public class EmployeeFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }
}
```

**After:**
```csharp
// In Kantoku.Persistence.Filters
public class EmployeeFilter : BaseFilter
{
    public string? Keyword { get; set; }
    
    public override void Validate()
    {
        base.Validate();
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}
```

### Step 2: Create Filter Mappers
```csharp
// In Kantoku.Api.Mappers
public class EmployeeFilterMapper : FilterMapperBase<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeFilter apiFilter, DomainFilters.EmployeeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        // Map other properties...
    }
}
```

### Step 3: Update Controllers
**Before:**
```csharp
public async Task<IActionResult> GetEmployees([FromQuery] EmployeeFilter filter)
{
    var (employees, count) = await _repository.GetByFilter(filter, options);
    return Ok(employees);
}
```

**After:**
```csharp
public async Task<IActionResult> GetEmployees([FromQuery] ApiFilters.EmployeeFilter apiFilter)
{
    var domainFilter = apiFilter.ToDomain();
    var (employees, count) = await _repository.GetByFilter(domainFilter, options);
    return Ok(employees);
}
```

### Step 4: Update Repositories and Queryables
```csharp
// Update using statements
using Kantoku.Persistence.Filters; // Instead of Kantoku.Api.Filters.Domains

// Update method signatures to use domain filters
public async Task<(IEnumerable<Employee>, int)> GetByFilter(
    DomainFilters.EmployeeFilter filter, 
    EmployeeQueryableOptions options)
```

### Step 5: Register Filter Mappers
```csharp
// In Program.cs
builder.Services.AddFilterMappers();
```

## Benefits

1. **Clean Architecture** - Persistence layer no longer depends on HTTP concerns
2. **Testability** - Easy to create domain filters for testing
3. **Reusability** - Same filters work in web, console, and background job scenarios
4. **Validation** - Centralized filter validation logic
5. **Maintainability** - Clear separation between API and domain concerns
6. **Performance** - Domain filters can include optimization logic

## Best Practices

1. **Domain filters should be pure** - No HTTP attributes or dependencies
2. **Include validation** - Override `Validate()` method for business rules
3. **Use meaningful names** - Properties should reflect business concepts
4. **Keep mappers simple** - Focus on property mapping, not business logic
5. **Test domain filters** - Write unit tests for filter validation logic
6. **Document filter behavior** - Include XML comments for complex filters

## Alternative Approaches Considered

### 1. Shared Filter Classes
Use the same filter classes in both API and persistence layers.
- **Pros**: Simple, no mapping needed
- **Cons**: Violates clean architecture, couples layers

### 2. Parameter Objects
Pass individual parameters instead of filter objects.
- **Pros**: Simple, explicit
- **Cons**: Verbose, hard to maintain with many parameters

### 3. Query Objects
Use command/query pattern with separate query objects.
- **Pros**: Very explicit, follows CQRS
- **Cons**: More complex, requires more boilerplate

The **Domain Filter Pattern** was chosen as it provides the best balance of clean architecture, maintainability, and ease of use.
