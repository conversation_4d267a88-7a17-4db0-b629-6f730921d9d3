using Kantoku.Domain.Models;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Function.Request;
using Kantoku.Api.Dtos.Function.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Services;

public interface IFunctionService
{
    Task<ResultDto<bool>> CreateFunction(CreateFunctionRequestDto dto);
    Task<ResultDto<SimpleFunctionsResponseDto>> GetFunctionList();
}

[Service(ServiceLifetime.Scoped)]
public class FunctionService : BaseService<FunctionService>, IFunctionService
{
    private readonly IFunctionRepository functionRepository;

    public FunctionService(
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IFunctionRepository functionRepository) : base(logger, httpContextAccessor)
    {
        this.functionRepository = functionRepository;
    }

    public async Task<ResultDto<bool>> CreateFunction(CreateFunctionRequestDto dto)
    {
        var newFunction = new Function
        {
            FunctionName = dto.FunctionName,
            HideInMenu = dto.HideInMenu,
        };
        await functionRepository.Create(newFunction);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<SimpleFunctionsResponseDto>> GetFunctionList()
    {
        var functions = await functionRepository.GetAll();
        if (functions is null || !functions.Any())
        {
            logger.Error("No functions found");
            return new ErrorResultDto<SimpleFunctionsResponseDto>(ResponseCodeConstant.FUNCTIONAL_ROLE_NOT_EXIST);
        }
        var result = new SimpleFunctionsResponseDto
        {
            Items = functions.Select(f => new SimpleFunctionResponseDto
            {
                FunctionId = f.FunctionUid.ToString(),
                FunctionName = f.FunctionName,
                HideInMenu = f.HideInMenu,
            }),
            TotalRecords = functions.Count(),
        };
        return new SuccessResultDto<SimpleFunctionsResponseDto>(result);
    }
}