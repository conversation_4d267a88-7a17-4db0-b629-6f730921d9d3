using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class ContractorQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedConstructionPaymentRequests { get; set; } = false;
}

public interface IContractorQueryable
{
    IQueryable<Contractor> GetContractorQuery(
        ContractorQueryableOptions options
    );

    IQueryable<Contractor> GetContractorQueryIncluded(
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    );

    IQueryable<Contractor> GetContractorQueryFilter(
        ContractorFilter filter,
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    );
}

public class ContractorQueryable(ApplicationDbContext context) :
    BaseQueryable<Contractor>(context), IContractorQueryable
{
    public IQueryable<Contractor> GetContractorQuery(
        ContractorQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Contractor> GetContractorQueryIncluded(
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    )
    {
        query ??= GetContractorQuery(options);
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(c => c.Projects);
        }
        if (options.IncludedConstructionPaymentRequests || options.IncludedAll)
        {
            query = query.Include(c => c.ConstructionPaymentRequests);
        }
        return query;

    }

    public IQueryable<Contractor> GetContractorQueryFilter(
        ContractorFilter filter,
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    )
    {
        query ??= GetContractorQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(c => c.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(c => c.ContractorName.Contains(filter.Keyword));
        }
        return query;
    }
}

