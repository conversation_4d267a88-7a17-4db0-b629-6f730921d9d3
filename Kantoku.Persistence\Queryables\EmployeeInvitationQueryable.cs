using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Queryables;

public class EmployeeInvitationQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IEmployeeInvitationQueryable
{
    IQueryable<EmployeeInvitation> GetEmployeeInvitationQuery(
        EmployeeInvitationQueryableOptions options
    );
    IQueryable<EmployeeInvitation> GetEmployeeInvitationQueryIncluded(
        EmployeeInvitationQueryableOptions options,
        IQueryable<EmployeeInvitation>? query = null
    );

    IQueryable<EmployeeInvitation> GetEmployeeInvitationQueryFiltered(
        EmployeeInvitationFilter filter,
        EmployeeInvitationQueryableOptions options,
        IQueryable<EmployeeInvitation>? query = null
    );
}

public class EmployeeInvitationQueryable(ApplicationDbContext context) :
    BaseQueryable<EmployeeInvitation>(context), IEmployeeInvitationQueryable
{

    public IQueryable<EmployeeInvitation> GetEmployeeInvitationQuery(
        EmployeeInvitationQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<EmployeeInvitation> GetEmployeeInvitationQueryIncluded(
        EmployeeInvitationQueryableOptions options,
        IQueryable<EmployeeInvitation>? query = null
    )
    {
        query ??= GetEmployeeInvitationQuery(options);

        return query;
    }

    public IQueryable<EmployeeInvitation> GetEmployeeInvitationQueryFiltered(
        EmployeeInvitationFilter filter,
        EmployeeInvitationQueryableOptions options,
        IQueryable<EmployeeInvitation>? query = null
    )
    {
        query ??= GetEmployeeInvitationQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.InvitedEmail is not null)
        {
            query = query.Where(p => p.Email == filter.InvitedEmail);
        }
        if (filter.IsAccepted is not null)
        {
            query = query.Where(p => p.IsAccepted == filter.IsAccepted);
        }
        return query;
    }
}
