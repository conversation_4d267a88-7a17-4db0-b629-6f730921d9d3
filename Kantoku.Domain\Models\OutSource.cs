using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class OutSource : AuditableEntity
{
    public Guid OutSourceUid { get; set; }

    [AuditProperty]
    public string OutSourceCode { get; set; } = null!;

    [AuditProperty]
    public string OutSourceName { get; set; } = null!;

    [AuditProperty]
    public string? Description { get; set; }

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public string? PhoneNumber { get; set; }

    [AuditProperty]
    public string? Email { get; set; }

    [AuditProperty]
    public string? CorporateNumber { get; set; }

    [AuditProperty]
    public ContactPerson? ContactPerson { get; set; }

    [AuditProperty]
    public ICollection<string>? Expertise { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<OutSourceShift> OutSourceShifts { get; set; } = [];

    public virtual ICollection<OutSourcePrice> OutSourcePrices { get; set; } = [];
}
