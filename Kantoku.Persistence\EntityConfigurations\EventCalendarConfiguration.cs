using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class EventCalendarConfiguration(string schema) : IEntityTypeConfiguration<EventCalendar>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EventCalendar> builder)
    {
        builder.HasKey(e => e.EventUid).HasName("event_calendar_pkey");
        builder.HasIndex(e => e.EventName, "event_calendar_name_ukey").IsUnique();

        builder.ToTable("event_calendar", Schema);

        builder.Property(e => e.EventUid)
            .HasColumnName("event_id");
        builder.Property(e => e.EventName)
            .HasColumnName("event_name");
        builder.Property(e => e.EventStartDate)
            .HasColumnType("date")
            .HasColumnName("event_start_date");
        builder.Property(e => e.EventEndDate)
            .HasColumnType("date")
            .HasColumnName("event_end_date");
        builder.Property(e => e.EventStartTime)
            .HasColumnType("time")
            .HasColumnName("event_start_time");
        builder.Property(e => e.EventEndTime)
            .HasColumnType("time")
            .HasColumnName("event_end_time");
        builder.Property(e => e.IsRecurring)
            .HasColumnName("is_recurring");
        builder.Property(e => e.RecurringFrom)
            .HasColumnType("date")
            .HasColumnName("recurring_from");
        builder.Property(e => e.RecurringTo)
            .HasColumnType("date")
            .HasColumnName("recurring_to");
        builder.Property(e => e.RecurringType)
            .HasColumnName("recurring_type");
        builder.Property(e => e.RecurringDay)
            .HasColumnType("smallint[]")
            .HasColumnName("recurring_day");
        builder.Property(e => e.RecurringWeek)
            .HasColumnType("smallint[]")
            .HasColumnName("recurring_week");
        builder.Property(e => e.RecurringMonth)
            .HasColumnType("smallint[]")
            .HasColumnName("recurring_month");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.IsDayOff)
            .HasDefaultValue(true)
            .HasColumnName("is_day_off");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Org).WithMany(p => p.EventCalendars)
            .HasForeignKey(d => d.OrgUid)
            .HasConstraintName("event_calendar_org_id_fkey");
    }
}
