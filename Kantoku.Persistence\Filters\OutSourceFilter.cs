namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for outsource queries - contains only business logic, no HTTP concerns
/// </summary>
public class OutSourceFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in outsource name, code, or description
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by expertise areas
    /// </summary>
    public IEnumerable<string>? Expertise { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Normalize expertise collection
        if (Expertise != null)
        {
            Expertise = Expertise
                .Where(e => !string.IsNullOrWhiteSpace(e))
                .Select(e => e.Trim())
                .Distinct()
                .ToList();
        }
    }
}