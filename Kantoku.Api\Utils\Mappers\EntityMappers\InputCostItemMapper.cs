using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.InputCost.Request;
using Kantoku.Api.Dtos.InputCostItem.Request;
using Kantoku.Api.Dtos.InputCostItem.Response;
using Kantoku.Api.Dtos.Vendor.Response;
using Kantoku.SharedKernel.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class InputCostItemMapper
{
    #region Entity to DTO mappings

    public static InputCostItemResponseDto ToInputCostItemResponseDto(this InputCostItem item)
    {
        if (item == null)
            return new InputCostItemResponseDto();

        return new InputCostItemResponseDto
        {
            InputCostItemId = item.InputCostItemUid.ToString(),
            TransactionDate = item.TransactionDate.ToString(),
            ItemId = item.ItemUid.ToString(),
            ItemName = item.Item.ItemName,
            CategoryCode = item.Item.Category.CategoryCode,
            CategoryName = item.Item.Category.CategoryName,
            VendorId = item.VendorUid.ToString(),
            VendorName = item.Vendor?.VendorName,
            Unit = item.Unit,
            Quantity = item.Quantity,
            Price = item.Price,
            TaxRate = item.TaxRate,
            TotalNonTaxed = item.TotalNonTaxed,
            TotalTaxed = item.TotalTaxed,
            Description = item.Description,
            InputCostId = item.InputCostUid.ToString(),
            ConstructionId = item.ConstructionUid.ToString(),
            OriginalInputCostNumber = item.InputCost?.OriginalNumber,
            CreateTime = DateTimeHelper.ParseToLocalTime(item.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = DateTimeHelper.ParseToLocalTime(item.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    public static InputCostItemsResponseDto ToInputCostItemsResponseDto(this IEnumerable<InputCostItem> items, int pageNum, int pageSize, int totalRecords)
    {
        if (items == null)
            return new InputCostItemsResponseDto();

        return new InputCostItemsResponseDto
        {
            Items = items.Select(i => i.ToInputCostItemResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    public static InputCostItem? ToEntity(this CreateInputCostItemRequestDto dto, Guid orgUid, Guid? inputCostUid = null)
    {
        if (dto == null)
            return null;

        var entity = new InputCostItem
        {
            InputCostItemUid = GuidHelper.GenerateUUIDv7(),
            InputCostUid = inputCostUid,
            OrgUid = orgUid,
            ConstructionUid = dto.ConstructionId,
            TransactionDate = DateOnly.Parse(dto.TransactionDate),
            Unit = dto.Unit,
            Quantity = dto.Quantity,
            Price = dto.Price,
            TaxRate = dto.TaxRate,
            TotalNonTaxed = dto.TotalNonTaxed ?? (long)(dto.Price * dto.Quantity),
            TotalTaxed = dto.TotalTaxed ?? (long)(dto.Price * dto.Quantity * (1 + (dto.TaxRate ?? 0))),
            Description = dto.Description,
            IsDeleted = false
        };

        if (dto.Item is not null && Guid.TryParse(dto.Item.ItemId, out var itemUid))
        {
            entity.ItemUid = itemUid;
        }
        else if (dto.Item is not null && string.IsNullOrEmpty(dto.Item.ItemName) && Guid.TryParse(dto.Item.CategoryId, out var categoryUid))
        {
            var newItem = new Item
            {
                ItemUid = GuidHelper.GenerateUUIDv7(),
                OrgUid = orgUid,
                ItemCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                ItemName = dto.Item.ItemName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                CategoryUid = categoryUid,
                IsDeleted = false,
            };
            entity.ItemUid = newItem.ItemUid;
            entity.Item = newItem;
        }

        if (dto.Vendor is not null && Guid.TryParse(dto.Vendor.VendorId, out var vendorUid))
        {
            entity.VendorUid = vendorUid;
        }
        else if (dto.Vendor is not null && string.IsNullOrEmpty(dto.Vendor.VendorName))
        {
            var newVendor = new Vendor
            {
                VendorUid = GuidHelper.GenerateUUIDv7(),
                OrgUid = orgUid,
                VendorName = dto.Vendor.VendorName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                IsDeleted = false,
            };
            entity.VendorUid = newVendor.VendorUid;
            entity.Vendor = newVendor;
        }

        return entity;
    }

    // Map UpdateInputCostItemRequestDto to InputCostItem ent   ity
    public static IEnumerable<InputCostItem> ToEntities(this IEnumerable<UpdateMultipleInputCostItemRequestDto> dto, Guid orgUid)
    {
        if (dto == null)
            return [];

        var entities = new List<InputCostItem>();
        foreach (var item in dto)
        {
            if (item == null || item.ConstructionId == null || item.TransactionDate == null)
                continue;
            entities.Add(new InputCostItem
            {
                InputCostItemUid = GuidHelper.GenerateUUIDv7(),
                OrgUid = orgUid,
                ConstructionUid = item.ConstructionId.Value,
                TransactionDate = DateOnly.TryParse(item.TransactionDate, out var transactionDate)
                ? transactionDate
                : DateOnly.FromDateTime(DateTime.Now),
                Unit = item.Unit,
                Quantity = item.Quantity ?? 0,
                Price = item.Price ?? 0,
                TaxRate = item.TaxRate ?? 0,
                TotalNonTaxed = (long?)item.TotalNonTaxed ?? 0,
                TotalTaxed = (long?)item.TotalTaxed ?? 0,
                Description = item.Description
            });
        }
        return entities;
    }

    // Map UpdateInputCostItemRequestDto to InputCostItem entity
    public static void UpdateFromDto(this InputCostItem entity, UpdateInputCostItemRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        entity.ConstructionUid = dto.ConstructionId ?? entity.ConstructionUid;
        entity.TransactionDate = DateOnly.TryParse(dto.TransactionDate, out var transactionDate)
            ? transactionDate
            : entity.TransactionDate;
        entity.ItemUid = dto.ItemId ?? entity.ItemUid;
        entity.Unit = dto.Unit ?? entity.Unit;
        entity.VendorUid = dto.VendorId ?? entity.VendorUid;
        entity.Quantity = dto.Quantity ?? entity.Quantity;
        entity.Price = dto.Price ?? entity.Price;
        entity.TaxRate = dto.TaxRate ?? entity.TaxRate;
        entity.TotalNonTaxed = (long?)dto.TotalNonTaxed ?? entity.TotalNonTaxed;
        entity.TotalTaxed = (long?)dto.TotalTaxed ?? entity.TotalTaxed;
        entity.Description = dto.Description ?? entity.Description;
    }



    // Map UpdateInputCostItemRequestDto to InputCostItem entity
    public static void UpdateFromDto(this IEnumerable<InputCostItem> entities, UpdateMultipleInputCostItemsRequestDto dto)
    {
        if (entities == null || dto == null)
            return;

        foreach (var entity in entities)
        {
            var match = dto.Items
                .Where(i => i.InputCostItemId == entity.InputCostItemUid)
                .FirstOrDefault();
            if (match == null)
                continue;

            entity.ConstructionUid = match.ConstructionId ?? entity.ConstructionUid;
            entity.TransactionDate = DateOnly.TryParse(match.TransactionDate, out var transactionDate)
                ? transactionDate
                : entity.TransactionDate;
            entity.ItemUid = match.ItemId ?? entity.ItemUid;
            entity.Unit = match.Unit ?? entity.Unit;
            entity.VendorUid = match.VendorId ?? entity.VendorUid;
            entity.Quantity = match.Quantity ?? entity.Quantity;
            entity.Price = match.Price ?? entity.Price;
            entity.TaxRate = match.TaxRate ?? entity.TaxRate;
            entity.TotalNonTaxed = (long?)match.TotalNonTaxed ?? entity.TotalNonTaxed;
            entity.TotalTaxed = (long?)match.TotalTaxed ?? entity.TotalTaxed;
            entity.Description = match.Description ?? entity.Description;
        }
    }

    // Map UpdateInputCostItemRequestDto to InputCostItem entity
    public static void UpdateFromDto(this InputCostItem entity, UpdateInputCostItemDto dto)
    {
        if (entity == null || dto == null)
            return;

        entity.TransactionDate = DateOnly.TryParse(dto.TransactionDate, out var transactionDate)
            ? transactionDate
            : entity.TransactionDate;
        entity.Unit = dto.Unit ?? entity.Unit;
        entity.Quantity = dto.Quantity ?? entity.Quantity;
        entity.Price = dto.Price ?? entity.Price;
        entity.TaxRate = dto.TaxRate ?? entity.TaxRate;
        entity.TotalNonTaxed = dto.TotalNonTaxed ?? entity.TotalNonTaxed;
        entity.TotalTaxed = dto.TotalTaxed ?? entity.TotalTaxed;
        entity.Description = dto.Description ?? entity.Description;

        if (dto.Item is not null && Guid.TryParse(dto.Item.ItemId, out var itemUid))
        {
            entity.ItemUid = itemUid;
        }
        else if (dto.Item is not null && string.IsNullOrEmpty(dto.Item.ItemName) && Guid.TryParse(dto.Item.CategoryId, out var categoryUid))
        {
            var newItem = new Item
            {
                ItemUid = GuidHelper.GenerateUUIDv7(),
                OrgUid = entity.OrgUid,
                ItemCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                ItemName = dto.Item.ItemName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                CategoryUid = categoryUid,
                IsDeleted = false,
            };
            entity.ItemUid = newItem.ItemUid;
            entity.Item = newItem;
        }
    }

    #endregion
}