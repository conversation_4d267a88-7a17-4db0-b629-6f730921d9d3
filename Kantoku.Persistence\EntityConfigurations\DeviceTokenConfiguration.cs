using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Persistence.EntityConfigurations;

public class DeviceTokenConfiguration(string schema) : IEntityTypeConfiguration<DeviceToken>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<DeviceToken> builder)
    {
        builder.HasKey(e => e.DeviceTokenUid).HasName("device_token_pkey");

        builder.ToTable("device_token", Schema);

        builder.Property(e => e.DeviceTokenUid)
            .HasColumnName("device_token_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.Platform)
            .HasColumnName("platform");
        builder.Property(e => e.OsVersion)
            .HasColumnName("os_version");
        builder.Property(e => e.DeviceId)
            .HasColumnName("device_id");
        builder.Property(e => e.LastActive)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("last_active");
        builder.Property(e => e.AppVersion)
            .HasColumnName("app_version");
        builder.Property(e => e.FirebaseToken)
            .HasColumnName("firebase_token");

        builder.HasOne(d => d.Employee)
            .WithMany(p => p.DeviceTokens)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("device_token_employee_uid_fkey");
    }
}