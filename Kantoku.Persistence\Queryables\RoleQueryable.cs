using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class RoleQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedStructure { get; set; } = false;
    public bool IncludedFunctions { get; set; } = false;
    public bool IncludedEmployees { get; set; } = false;
}


public interface IRoleQueryable
{
    IQueryable<Role> GetRolesQuery(
        RoleQueryableOptions options
    );

    IQueryable<Role> GetRolesQueryIncluded(
        RoleQueryableOptions options,
        IQueryable<Role>? query = null
    );

    IQueryable<Role> GetRolesQueryByFilter(
        RoleFilter filter,
        RoleQueryableOptions options,
        IQueryable<Role>? query = null
    );
}

public class RoleQueryable(ApplicationDbContext context) :
    BaseQueryable<Role>(context), IRoleQueryable
{
    public IQueryable<Role> GetRolesQuery(
        RoleQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(r => r.IsDeleted == false && r.IsHidden == false);
        return query;
    }

    public IQueryable<Role> GetRolesQueryIncluded(
        RoleQueryableOptions options,
        IQueryable<Role>? query = null
    )
    {
        query ??= GetRolesQuery(options);
        if (options.IncludedFunctions || options.IncludedAll)
        {
            query = query.Include(r => r.RoleFunctions)
                .ThenInclude(rf => rf.Function)
                    .ThenInclude(f => f.Children);
        }
        if (options.IncludedEmployees || options.IncludedAll)
        {
            query = query.Include(r => r.EmployeeRoles)
                .ThenInclude(er => er.Employee);
        }
        if (options.IncludedStructure || options.IncludedAll)
        {
            query = query.Include(r => r.Structure);
        }
        return query;
    }

    public IQueryable<Role> GetRolesQueryByFilter(
        RoleFilter filter,
        RoleQueryableOptions options,
        IQueryable<Role>? query = null
    )
    {
        query ??= GetRolesQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(r => r.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(r => r.RoleName.Contains(filter.Keyword));
        }
        if (filter.StructureId is not null && filter.StructureId != Guid.Empty)
        {
            query = query.Where(r => r.StructureUid == filter.StructureId);
        }

        return query;
    }
}