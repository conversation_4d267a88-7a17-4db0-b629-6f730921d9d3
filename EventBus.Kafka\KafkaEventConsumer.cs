using Confluent.Kafka;
using EventBus.Interfaces;
using EventBus.Kafka.Configuration;
using EventBus.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Text;

namespace EventBus.Kafka;

/// <summary>
/// Kafka implementation of the event consumer
/// </summary>
public class KafkaEventConsumer : IEventConsumer, IDisposable
{
    private readonly KafkaConsumerConfig _config;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<KafkaEventConsumer> _logger;
    private readonly ConcurrentDictionary<string, List<EventSubscription>> _subscriptions;
    private IConsumer<string, string>? _consumer;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _consumerTask;
    private bool _disposed;

    public KafkaEventConsumer(
        IOptions<KafkaConsumerConfig> config,
        IServiceProvider serviceProvider,
        ILogger<KafkaEventConsumer> logger)
    {
        _config = config.Value;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _subscriptions = new ConcurrentDictionary<string, List<EventSubscription>>();
    }

    public void Subscribe<T, TH>()
        where T : class, IIntegrationEvent
        where TH : class, IEventHandler<T>
    {
        var eventType = typeof(T).Name;
        var handlerType = typeof(TH).Name;

        var subscription = new EventSubscription
        {
            EventType = typeof(T).AssemblyQualifiedName ?? typeof(T).FullName ?? eventType,
            HandlerType = typeof(TH).AssemblyQualifiedName ?? typeof(TH).FullName ?? handlerType
        };

        _subscriptions.AddOrUpdate(eventType,
            [subscription],
            (key, existing) =>
            {
                existing.Add(subscription);
                return existing;
            });

        _logger.LogInformation("Subscribed {HandlerType} to {EventType}", handlerType, eventType);
    }

    public void Unsubscribe<T, TH>()
        where T : class, IIntegrationEvent
        where TH : class, IEventHandler<T>
    {
        var eventType = typeof(T).Name;
        var handlerType = typeof(TH).Name;

        if (_subscriptions.TryGetValue(eventType, out var subscriptions))
        {
            subscriptions.RemoveAll(s => s.HandlerType == handlerType);
            if (subscriptions.Count == 0)
            {
                _subscriptions.TryRemove(eventType, out _);
            }
        }

        _logger.LogInformation("Unsubscribed {HandlerType} from {EventType}", handlerType, eventType);
    }

    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_consumer != null)
        {
            _logger.LogWarning("Consumer is already started");
            return Task.CompletedTask;
        }

        var consumerConfig = new ConsumerConfig
        {
            BootstrapServers = _config.BootstrapServers,
            GroupId = _config.GroupId,
            AutoOffsetReset = ParseAutoOffsetReset(_config.AutoOffsetReset),
            EnableAutoCommit = _config.EnableAutoCommit,
            SessionTimeoutMs = _config.SessionTimeoutMs,
            FetchMinBytes = _config.FetchMinBytes,
            MaxPollIntervalMs = _config.MaxPollIntervalMs,
            HeartbeatIntervalMs = _config.HeartbeatIntervalMs,
            AutoCommitIntervalMs = _config.AutoCommitIntervalMs
        };

        _consumer = new ConsumerBuilder<string, string>(consumerConfig).Build();

        // Subscribe to all topics for registered event types
        var topics = _subscriptions.Keys.Select(GetTopicName).ToList();
        if (topics.Count > 0)
        {
            _consumer.Subscribe(topics);
            _logger.LogInformation("Subscribed to topics: {Topics}", string.Join(", ", topics));
        }

        _cancellationTokenSource = new CancellationTokenSource();
        _consumerTask = Task.Run(() => ConsumeMessages(_cancellationTokenSource.Token), cancellationToken);

        _logger.LogInformation("Kafka event consumer started");
        return Task.CompletedTask;
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_consumer == null)
        {
            _logger.LogWarning("Consumer is not started");
            return;
        }

        _logger.LogInformation("Stopping Kafka event consumer...");

        _cancellationTokenSource?.Cancel();

        if (_consumerTask != null)
        {
            try
            {
                await _consumerTask;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        _consumer?.Close();
        _consumer?.Dispose();
        _consumer = null;

        _cancellationTokenSource?.Dispose();
        _cancellationTokenSource = null;
        _consumerTask = null;

        _logger.LogInformation("Kafka event consumer stopped");
    }

    private async Task ConsumeMessages(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested && _consumer != null)
            {
                try
                {
                    // Use a shorter timeout for consume to avoid poll interval issues
                    var consumeResult = _consumer.Consume(TimeSpan.FromSeconds(1));
                    if (consumeResult?.Message != null)
                    {
                        try
                        {
                            await ProcessMessage(consumeResult.Message);

                            // Manually commit if auto-commit is disabled
                            if (!_config.EnableAutoCommit)
                            {
                                _consumer?.Commit(consumeResult);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error processing message");
                        }
                    }
                }
                catch (ConsumeException ex)
                {
                    _logger.LogError(ex, "Error consuming message");
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error in consume loop");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in consumer loop");
        }
    }

    private async Task ProcessMessage(Message<string, string> message)
    {
        try
        {
            var eventTypeHeader = message.Headers.FirstOrDefault(h => h.Key == "EventType");
            if (eventTypeHeader == null)
            {
                _logger.LogWarning("Message missing EventType header");
                return;
            }

            var eventType = Encoding.UTF8.GetString(eventTypeHeader.GetValueBytes());

            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
            {
                _logger.LogDebug("No subscriptions found for event type {EventType}", eventType);
                return;
            }

            foreach (var subscription in subscriptions.Where(s => s.IsActive))
            {
                await ProcessSubscription(subscription, message.Value, eventType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message");
        }
    }

    private async Task ProcessSubscription(EventSubscription subscription, string messageValue, string eventType)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();

            // Try to resolve handler type
            var handlerType = Type.GetType(subscription.HandlerType) ??
                             AppDomain.CurrentDomain.GetAssemblies()
                                 .SelectMany(a => a.GetTypes())
                                 .FirstOrDefault(t => t.FullName == subscription.HandlerType || t.Name == subscription.HandlerType);

            if (handlerType == null)
            {
                _logger.LogError("Handler type {HandlerType} not found", subscription.HandlerType);
                return;
            }

            var handler = scope.ServiceProvider.GetService(handlerType);
            if (handler == null)
            {
                _logger.LogError("Handler {HandlerType} not registered in DI container", subscription.HandlerType);
                return;
            }

            // Try to resolve event type
            var eventTypeObj = Type.GetType(subscription.EventType) ??
                              AppDomain.CurrentDomain.GetAssemblies()
                                  .SelectMany(a => a.GetTypes())
                                  .FirstOrDefault(t => t.FullName == subscription.EventType || t.Name == eventType);

            if (eventTypeObj == null)
            {
                _logger.LogError("Event type {EventType} not found", subscription.EventType);
                return;
            }

            var eventObj = JsonConvert.DeserializeObject(messageValue, eventTypeObj);
            if (eventObj == null)
            {
                _logger.LogError("Failed to deserialize event {EventType}", eventTypeObj.Name);
                return;
            }

            // Call the handler
            var handleMethod = handlerType.GetMethod("HandleAsync");
            if (handleMethod != null)
            {
                var task = (Task?)handleMethod.Invoke(handler, [eventObj, CancellationToken.None]);
                if (task != null)
                {
                    await task;
                    _logger.LogInformation("Successfully processed event {EventType} with handler {HandlerType}",
                        eventTypeObj.Name, handlerType.Name);
                }
            }
            else
            {
                _logger.LogError("HandleAsync method not found on handler {HandlerType}", handlerType.Name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing subscription {HandlerType} for event {EventType}",
                subscription.HandlerType, eventType);
        }
    }

    private string GetTopicName(string eventType)
    {
        return $"{_config.TopicPrefix}.{eventType.ToLowerInvariant()}";
    }

    private static AutoOffsetReset ParseAutoOffsetReset(string autoOffsetReset)
    {
        return autoOffsetReset.ToLowerInvariant() switch
        {
            "earliest" => AutoOffsetReset.Earliest,
            "latest" => AutoOffsetReset.Latest,
            "error" => AutoOffsetReset.Error,
            _ => AutoOffsetReset.Earliest
        };
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Kafka consumer");
        }
        finally
        {
            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }
}
