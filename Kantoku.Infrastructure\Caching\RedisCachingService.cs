using System.Text.Json;

namespace Kantoku.Infrastructure.Caching
{
    public class RedisCachingService : ICachingService
    {
        private readonly IDistributedCache _distributedCache;

        public RedisCachingService(IDistributedCache distributedCache)
        {
            _distributedCache = distributedCache ?? throw new ArgumentNullException(nameof(distributedCache));
        }

        public async Task<T?> GetAsync<T>(string key)
        {
            var jsonData = await _distributedCache.GetStringAsync(key);
            if (jsonData is null)
            {
                return default;
            }
            return JsonSerializer.Deserialize<T>(jsonData);
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpireTime = null, TimeSpan? slidingExpireTime = null)
        {
            var options = new DistributedCacheEntryOptions();

            if (absoluteExpireTime.HasValue)
            {
                options.SetAbsoluteExpiration(absoluteExpireTime.Value);
            }
            else if (slidingExpireTime.HasValue)
            {
                options.SetSlidingExpiration(slidingExpireTime.Value);
            }
            else
            {
                // Default expiration if none is specified (e.g., 1 hour)
                options.SetSlidingExpiration(TimeSpan.FromHours(1));
            }

            var jsonData = JsonSerializer.Serialize(value);
            await _distributedCache.SetStringAsync(key, jsonData, options);
        }

        public async Task RemoveAsync(string key)
        {
            await _distributedCache.RemoveAsync(key);
        }

        public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? absoluteExpireTime = null, TimeSpan? slidingExpireTime = null)
        {
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            var newValue = await factory();
            if (newValue != null)
            {
                await SetAsync(key, newValue, absoluteExpireTime, slidingExpireTime);
            }
            return newValue;
        }
    }
} 