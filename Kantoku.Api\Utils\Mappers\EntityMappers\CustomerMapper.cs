using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Customer.Request;
using Kantoku.Api.Dtos.Customer.Response;
using Kantoku.SharedKernel.Helpers;
using System.Linq;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class CustomerMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Customer entity to a CustomerResponseDto
    /// </summary>
    /// <param name="customer">The Customer entity to map</param>
    /// <param name="languageCode">The language code</param>
    /// <returns>The mapped CustomerResponseDto</returns>
    public static CustomerResponseDto ToCustomerResponseDto(this Customer customer, string languageCode)
    {
        if (customer == null)
            return new CustomerResponseDto();
        var customerType = customer.CustomerType;
        var responseDto = new CustomerResponseDto
        {
            CustomerId = customer.CustomerUid.ToString(),
            CustomerCode = customer.CustomerCode,
            CustomerName = customer.CustomerName,
            CustomerSubName = customer.CustomerSubName,
            CustomerTypeCode = customer.CustomerTypeCode,
            CustomerTypeName = customerType?.TranslatedCustomerType?.FirstOrDefault(t => t.LanguageCode == languageCode)?.CustomerTypeName,
            CorporateNumber = customer.CorporateNumber,
            Address = customer.Address,
            PhoneNumber = customer.PhoneNumber,
            Email = customer.Email,
            Description = customer.Description,
            LogoUrl = customer.LogoUrl,
            ContactPerson = customer.ContactPerson == null ? null : new ContactPerson
            {
                Name = customer.ContactPerson.Name,
                Email = customer.ContactPerson.Email,
                PhoneNumber = customer.ContactPerson.PhoneNumber
            },
            CreateTime = customer.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = customer.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss")
        };

        return responseDto;
    }

    public static IEnumerable<CustomerResponseDto> ToCustomerResponseDtos(
        this IEnumerable<Customer> customers,
        string languageCode)
    {
        if (customers == null || !customers.Any())
            return [];

        return customers.Select(c => c.ToCustomerResponseDto(languageCode));
    }

    /// <summary>
    /// Maps a collection of Customer entities to a CustomersResponseDto
    /// </summary>
    /// <param name="customers">The collection of Customer entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <param name="languageCode">The language code</param>
    /// <returns></returns>
    public static CustomersResponseDto ToCustomersResponseDto(
        this IEnumerable<Customer> customers,
        int pageNum,
        int pageSize,
        int totalRecords,
        string languageCode)
    {
        return new CustomersResponseDto
        {
            Items = customers.ToCustomerResponseDtos(languageCode),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a Customer entity to a CustomerDetailResponseDto
    /// </summary>
    /// <param name="customer">The Customer entity to map</param>
    /// <param name="projects">The projects to map</param>
    /// <param name="languageCode">The language code</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns></returns>
    public static CustomerDetailResponseDto ToCustomerDetailResponseDto(
        this Customer customer,
        IEnumerable<Project> projects,
        string languageCode,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (customer == null)
            return new CustomerDetailResponseDto();
        var customerType = customer.CustomerType;

        return new CustomerDetailResponseDto
        {
            CustomerId = customer.CustomerUid.ToString(),
            CustomerCode = customer.CustomerCode,
            CustomerName = customer.CustomerName,
            CustomerSubName = customer.CustomerSubName,
            CustomerTypeCode = customer.CustomerTypeCode,
            CustomerTypeName = customerType?.TranslatedCustomerType?.FirstOrDefault(t => t.LanguageCode == languageCode)?.CustomerTypeName,
            CorporateNumber = customer.CorporateNumber,
            Address = customer.Address,
            PhoneNumber = customer.PhoneNumber,
            Email = customer.Email,
            Description = customer.Description,
            LogoUrl = customer.LogoUrl,
            ContactPerson = customer.ContactPerson == null ? null : new ContactPerson
            {
                Name = customer.ContactPerson.Name,
                Email = customer.ContactPerson.Email,
                PhoneNumber = customer.ContactPerson.PhoneNumber
            },
            CreateTime = customer.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = customer.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            Projects = projects.ToContractedProjectResponseDto(),
            ProjectListPageNum = pageNum,
            ProjectListPageSize = pageSize,
            ProjectListTotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateCustomerRequestDto to a Customer entity
    /// </summary>
    /// <param name="dto">The CreateCustomerRequestDto to map</param>
    /// <param name="orgUid">The organization UID</param>
    /// <returns>The mapped Customer entity</returns>
    public static Customer? ToEntity(this CreateCustomerRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Customer
        {
            CustomerUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            CustomerCode = dto.CustomerCode,
            CustomerName = dto.CustomerName,
            CustomerSubName = dto.CustomerSubName,
            CustomerTypeCode = dto.CustomerTypeCode,
            CorporateNumber = dto.CorporateNumber,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            Description = dto.Description,
            IsDeleted = false,
            ContactPerson = dto.ContactPerson == null ? null : new ContactPerson
            {
                Name = dto.ContactPerson.Name,
                Email = dto.ContactPerson.Email,
                PhoneNumber = dto.ContactPerson.PhoneNumber
            }
        };

        return entity;
    }

    /// <summary>
    /// Maps an UpdateCustomerRequestDto to a Customer entity
    /// </summary>
    /// <param name="entity">The Customer entity to update</param>
    /// <param name="dto">The UpdateCustomerRequestDto to map</param>
    /// <param name="logoUrl">The logo URL</param>
    /// <returns>The mapped Customer entity</returns>
    public static void UpdateFromDto(this Customer entity, UpdateCustomerRequestDto dto, string? logoUrl = null)
    {
        if (entity == null || dto == null)
            return;

        if (dto.CustomerCode != null)
            entity.CustomerCode = dto.CustomerCode;

        if (dto.CustomerName != null)
            entity.CustomerName = dto.CustomerName;

        if (dto.CustomerSubName != null)
            entity.CustomerSubName = dto.CustomerSubName;

        if (dto.CustomerTypeCode != null)
            entity.CustomerTypeCode = dto.CustomerTypeCode;

        if (dto.CorporateNumber != null)
            entity.CorporateNumber = dto.CorporateNumber;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (logoUrl != null)
            entity.LogoUrl = logoUrl;

        if (dto.ContactPerson != null)
        {
            entity.ContactPerson = new ContactPerson
            {
                Name = dto.ContactPerson.Name,
                Email = dto.ContactPerson.Email,
                PhoneNumber = dto.ContactPerson.PhoneNumber
            };
        }
    }

    #endregion
}