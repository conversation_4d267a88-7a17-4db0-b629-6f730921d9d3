﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IConstructionCostRepository
{
    Task<IEnumerable<ConstructionCost>> GetByFilter(DomainFilter.ConstructionCostFilter filter, ConstructionCostQueryableOptions options);
    Task<ConstructionCost?> GetById(Guid constructionCostId, ConstructionCostQueryableOptions options);
    Task<ConstructionCost?> Create(ConstructionCost constructionCost);
    Task<ConstructionCost?> Update(ConstructionCost constructionCost);
}

public class ConstructionCostRepository : BaseRepository<ConstructionCost>, IConstructionCostRepository
{
    private readonly IConstructionCostQueryable constructionCostQueryable;

    public ConstructionCostRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IConstructionCostQueryable constructionCostQueryable
    ) : base(context, logger, tenantContext)
    {
        this.constructionCostQueryable = constructionCostQueryable;
    }

    public async Task<IEnumerable<ConstructionCost>> GetByFilter(DomainFilter.ConstructionCostFilter filter, ConstructionCostQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);

            var query = constructionCostQueryable.GetConstructionCostQueryFilter(filter, options);

            var result = await query
                .OrderBy(c => c.ConstructionUid)
                .ToListAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all constructions");
            return [];
        }
    }

    public async Task<ConstructionCost?> GetById(Guid constructionCostId, ConstructionCostQueryableOptions options)
    {
        try
        {
            var query = constructionCostQueryable.GetConstructionCostQueryIncluded(options)
                .Where(c => c.ConstructionCostUid == constructionCostId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting construction cost by id: {constructionCostId}", constructionCostId);
            return null;
        }
    }

    public async Task<ConstructionCost?> Create(ConstructionCost constructionCost)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.ConstructionCosts.AddAsync(constructionCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(constructionCost.ConstructionCostUid, new ConstructionCostQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating construction");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<ConstructionCost?> Update(ConstructionCost constructionCost)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ConstructionCosts.Update(constructionCost);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(constructionCost.ConstructionCostUid, new ConstructionCostQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating construction");
            await transaction.RollbackAsync();
            return null;
        }
    }
}
