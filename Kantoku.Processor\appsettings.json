{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AppConfig": {"GeoCodingAPK": "AIz000000000000000000000000000000"}, "ApplicationDbConfig": {"DbHost": "localhost", "DbPort": "5432", "DbName": "kantoku_app", "Schema": "public", "Username": "********", "Password": "********"}, "BatchProcessing": {"PollIntervalSeconds": 30, "MaxConcurrentJobs": 5, "JobTimeoutMinutes": 60}, "KafkaConsumerConfig": {"BootstrapServers": "************:9092", "GroupId": "kantoku-processor", "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "SessionTimeoutMs": 30000, "TopicPrefix": "kantoku", "DefaultPartitions": 3, "DefaultReplicationFactor": 1, "FetchMinBytes": 1, "MaxPollIntervalMs": 600000, "HeartbeatIntervalMs": 3000, "AutoCommitIntervalMs": 5000}, "HangfireConfig": {"ConnectionString": "Host=************;Database=kantoku_hangfire;Username=********;Password=********;Port=5432", "Schema": "hangfire", "DashboardPath": "/hangfire", "EnableDashboard": true, "WorkerCount": 4, "RetryAttempts": 3, "JobTimeoutMinutes": 30, "EnableDetailedLogging": true}}