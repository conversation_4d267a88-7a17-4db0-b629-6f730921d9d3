﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class ProjectSchedule : AuditableEntity
{
    public Guid ProjectScheduleUid { get; set; }
    public Guid ProjectUid { get; set; }

    [AuditProperty]
    public DateOnly WorkingDate { get; set; }

    [AuditProperty]
    public float PlannedWorkload { get; set; }

    [AuditProperty]
    public float PresignedWorkload { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual Project Project { get; set; } = null!;

    public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = [];

    public virtual ICollection<OutSourceShift> OutSourceShifts { get; set; } = [];
}
