using Kantoku.Domain.Models;

namespace Kantoku.Api.Dtos.OutSource.Request;

public class UpdateOutSourceRequestDto
{
    /// <summary>
    /// The code assigned to the position
    /// </summary>
    public string? OutSourceCode { get; set; }

    /// <summary>
    /// The name of the position
    /// </summary>
    public string? OutSourceName { get; set; }

    /// <summary>
    /// Additional details about the position
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Collection of expertise areas associated with the outsource (optional)
    /// </summary>
    public ICollection<string>? Expertise { get; set; }

    /// <summary>
    /// The corporate number of the outsource
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the outsource
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the outsource
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the outsource
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the outsource
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The price of the outsource
    /// </summary>
    public OutSourcePriceRequestDto? Price { get; set; }

    /// <summary>
    /// The logo of the outsource
    /// </summary>
    public IFormFile? Logo { get; set; }
}
