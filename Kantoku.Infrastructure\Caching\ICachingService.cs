using System;
using System.Threading.Tasks;

namespace Kantoku.Infrastructure.Caching
{
    public interface ICachingService
    {
        Task<T?> GetAsync<T>(string key);
        Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpireTime = null, TimeSpan? slidingExpireTime = null);
        Task RemoveAsync(string key);
        Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? absoluteExpireTime = null, TimeSpan? slidingExpireTime = null);
    }
} 