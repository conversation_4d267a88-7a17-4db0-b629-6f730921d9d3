using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Position.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Position.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class PositionController : BaseController
{
    private readonly IPositionService positionService;
    private readonly IAuditLogService auditLogService;

    public PositionController(
        ITResponseFactory responseFactory,
        IPositionService positionService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.positionService = positionService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated list of positions
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>Paginated list of positions</returns>
    [HttpGet]
    public async Task<GeneralResponse<PositionsResponseDto>> GetAll([FromQuery] PositionFilter filter)
    {
        try
        {
            var result = await positionService.GetPositionByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PositionsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get position details by ID
    /// </summary>
    /// <param name="id">Position ID to retrieve</param>
    /// <returns>Position details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<PositionResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<PositionResponseDto>();

            var result = await positionService.GetPositionById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PositionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get simplified position information with optional pagination
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>Simplified position information</returns>
    [HttpGet("simple")]

    public async Task<GeneralResponse<SimplePositionResponseDto>> GetSimplePositionInfo([FromQuery] PositionFilter filter)
    {
        try
        {
            var result = await positionService.GetSimplePositionInfo(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimplePositionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new position
    /// </summary>
    /// <param name="requestDto">Position creation data</param>
    /// <returns>Created position details</returns>
    [HttpPost]
    public async Task<GeneralResponse<PositionResponseDto>> CreatePosition([FromBody] CreatePositionRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<PositionResponseDto>();

            var result = await positionService.CreatePosition(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PositionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing position
    /// </summary>
    /// <param name="id">Position ID to update</param>
    /// <param name="requestDto">Updated position data</param>
    /// <returns>Updated position details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<PositionResponseDto>> UpdatePosition([FromRoute] Guid id, [FromBody] UpdatePositionRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<PositionResponseDto>();

            var result = await positionService.UpdatePosition(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PositionResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a position
    /// </summary>
    /// <param name="id">Position ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeletePosition([FromRoute] Guid id)
    {
        try
        {
            var result = await positionService.DeletePosition(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a position
    /// </summary>
    /// <param name="id">Position ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetPositionLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Position>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
