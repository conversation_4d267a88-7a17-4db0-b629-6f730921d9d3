using System.ComponentModel.DataAnnotations;
using Kantoku.Domain.Models;

namespace Kantoku.Api.Dtos.Contractor.Request;
public class CreateContractorRequestDto
{
    /// <summary>
    /// Contractor code (*)
    /// </summary>
    [Required]
    public string ContractorCode { get; set; } = null!;

    /// <summary>
    /// Contractor name (*)
    /// </summary>
    [Required]
    public string ContractorName { get; set; } = null!;

    /// <summary>
    /// Contractor sub name
    /// </summary>
    public string? ContractorSubName { get; set; }

    /// <summary>
    /// Contractor description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The corporate number of the contractor
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the contractor
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the contractor
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the contractor
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the contractor
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The logo of the contractor
    /// </summary>
    public IFormFile? Logo { get; set; }
}
