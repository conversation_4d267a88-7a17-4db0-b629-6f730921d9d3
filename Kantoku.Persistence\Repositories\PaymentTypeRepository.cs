﻿using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IPaymentTypeRepository
{
    Task<(int, IEnumerable<PaymentType>)> GetByFilter(DomainFilter.PaymentTypeFilter filter, PaymentTypeQueryableOptions options);
    Task<PaymentType?> GetById(Guid paymentTypeId, PaymentTypeQueryableOptions options);
    Task<PaymentType?> Create(PaymentType paymentType);
    Task<PaymentType?> Update(PaymentType paymentType);
}

public class PaymentTypeRepository : BaseRepository<PaymentTypeRepository>, IPaymentTypeRepository
{
    private readonly IPaymentTypeQueryable queryable;
    public PaymentTypeRepository(
        ApplicationDbContext context,
        IPaymentTypeQueryable queryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(int, IEnumerable<PaymentType>)> GetByFilter(DomainFilter.PaymentTypeFilter filter, PaymentTypeQueryableOptions options)
    {
        try
        {
            var query = queryable.GetPaymentTypesQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .OrderBy(pt => pt.PaymentTypeUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting payment types");
            throw;
        }
    }

    public async Task<PaymentType?> GetById(Guid paymentTypeId, PaymentTypeQueryableOptions options)
    {
        try
        {
            var query = queryable.GetPaymentTypesQueryIncluded(options)
                .Where(pt => pt.PaymentTypeUid == paymentTypeId);
            var paymentType = await query.FirstOrDefaultAsync();
            return paymentType;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting payment type");
            throw;
        }
    }

    public async Task<PaymentType?> Create(PaymentType paymentType)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.PaymentTypes.AddAsync(paymentType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(paymentType.PaymentTypeUid, new PaymentTypeQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating payment type");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<PaymentType?> Update(PaymentType paymentType)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.PaymentTypes.Update(paymentType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(paymentType.PaymentTypeUid, new PaymentTypeQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating payment type");
            await transaction.RollbackAsync();
            return null;
        }
    }
}
