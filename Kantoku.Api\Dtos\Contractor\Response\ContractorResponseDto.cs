using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Contractor.Response;

public class ContractorResponseDto : BaseResponseDto
{
    /// <summary>
    /// Contractor UID
    /// </summary>
    public string? ContractorId { get; set; }

    /// <summary>
    /// Contractor code
    /// </summary>
    public string? ContractorCode { get; set; }

    /// <summary>
    /// Contractor name
    /// </summary>
    public string? ContractorName { get; set; }

    /// <summary>
    /// Contractor sub name
    /// </summary>
    public string? ContractorSubName { get; set; }

    /// <summary>
    /// Corporate number
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Contact person
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// Logo URL
    /// </summary>
    public string? LogoUrl { get; set; }
}
