using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Persistence.Repositories;

public interface IOutSourcePriceRepository
{
    Task<(IEnumerable<OutSourcePrice>, int)> GetByFilter(DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options);
    Task<(IEnumerable<OutSourcePrice>, int)> GetByOutSourceUid(Guid outsourceId, DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options);
    Task<IEnumerable<OutSourcePrice>> GetByOutSourceUids(IEnumerable<Guid> outsourceIds, DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options);
    Task<OutSourcePrice?> GetLatestPrice(Guid outsourceId, OutSourcePriceQueryableOptions options);
    Task<OutSourcePrice?> GetById(Guid outsourcePriceId, OutSourcePriceQueryableOptions options);
    Task<OutSourcePrice?> Create(OutSourcePrice outsourcePrice, OutSourcePriceQueryableOptions outSourcePriceOptions);
    Task<OutSourcePrice?> Update(OutSourcePrice outsourcePrice, OutSourcePriceQueryableOptions outSourcePriceOptions);
}


public class OutSourcePriceRepository : BaseRepository<OutSourcePrice>, IOutSourcePriceRepository
{
    private readonly IOutSourcePriceQueryable outSourcePriceQueryable;

    public OutSourcePriceRepository(
        ApplicationDbContext context,
        IOutSourcePriceQueryable outSourcePriceQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.outSourcePriceQueryable = outSourcePriceQueryable;
    }

    public async Task<(IEnumerable<OutSourcePrice>, int)> GetByFilter(DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = outSourcePriceQueryable.GetOutSourcePriceQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var items = await query
                .OrderByDescending(x => x.EffectiveDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (items!, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outsource prices");
            return ([], 0);
        }
    }

    public async Task<(IEnumerable<OutSourcePrice>, int)> GetByOutSourceUid(Guid outsourceId, DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = outSourcePriceQueryable.GetOutSourcePriceQueryFiltered(filter, options)
                .Where(x => x.OutSourceUid == outsourceId);

            var total = await query.CountAsync();

            var items = await query
                .OrderByDescending(x => x.EffectiveDate)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (items, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outsource prices");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<OutSourcePrice>> GetByOutSourceUids(IEnumerable<Guid> outsourceUids, DomainFilter.OutSourcePriceFilter filter, OutSourcePriceQueryableOptions options)
    {
        try
        {
            var query = outSourcePriceQueryable.GetOutSourcePriceQueryFiltered(filter, options)
                .Where(x => outsourceUids.Contains(x.OutSourceUid));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outsource prices");
            return [];
        }
    }

    public async Task<OutSourcePrice?> GetLatestPrice(Guid outsourceId, OutSourcePriceQueryableOptions options)
    {
        try
        {
            var query = outSourcePriceQueryable.GetOutSourcePriceQueryIncluded(options)
                .Where(x => x.OutSourceUid == outsourceId);

            return await query
                .OrderByDescending(x => x.EffectiveDate)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting latest outsource price for outsource {outsourceId}", outsourceId);
            return null;
        }
    }

    public async Task<OutSourcePrice?> GetById(Guid outsourcePriceId, OutSourcePriceQueryableOptions options)
    {
        try
        {
            var query = outSourcePriceQueryable.GetOutSourcePriceQueryIncluded(options)
                .Where(x => x.OutSourcePriceUid == outsourcePriceId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outsource price by id {outsourcePriceId}", outsourcePriceId);
            return null;
        }
    }

    public async Task<OutSourcePrice?> Create(OutSourcePrice outsourcePrice, OutSourcePriceQueryableOptions outSourcePriceOptions)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.OutSourcePrices.AddAsync(outsourcePrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outsourcePrice.OutSourcePriceUid, outSourcePriceOptions);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating outsource price {OutSourcePrice}", outsourcePrice);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<OutSourcePrice?> Update(OutSourcePrice outsourcePrice, OutSourcePriceQueryableOptions outSourcePriceOptions)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.OutSourcePrices.Update(outsourcePrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outsourcePrice.OutSourcePriceUid, outSourcePriceOptions);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating outsource price");
            await transaction.RollbackAsync();
            return null;
        }
    }
}