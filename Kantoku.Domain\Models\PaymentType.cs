using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class PaymentType : AuditableEntity
{
    public Guid PaymentTypeUid { get; set; }

    [AuditProperty]
    public string PaymentTypeName { get; set; } = null!;

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<InputCost>? InputCosts { get; set; }
}
