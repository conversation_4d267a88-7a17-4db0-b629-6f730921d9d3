FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file and all project files first
COPY ["Kantoku.Api.csproj", "Kantoku.Api/"]
COPY ["../EventBus/EventBus.csproj", "EventBus/"]
COPY ["../EventBus.Kafka/EventBus.Kafka.csproj", "EventBus.Kafka/"]

# Restore packages
RUN dotnet restore "Kantoku.Api/Kantoku.Api.csproj"

# Copy the rest of the source code
COPY . ./Kantoku.Api/
COPY ../EventBus/ ./EventBus/
COPY ../EventBus.Kafka/ ./EventBus.Kafka/

ARG configuration=Release
RUN dotnet publish "Kantoku.Api/Kantoku.Api.csproj" -c $configuration -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/publish .
EXPOSE 4869
ENV ASPNETCORE_URLS=http://+:4869
USER app
ENTRYPOINT ["dotnet", "Kantoku.Api.dll"]