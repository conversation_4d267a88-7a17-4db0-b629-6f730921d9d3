using Kantoku.Domain.Models;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.WorkShift.Request;
using Kantoku.Api.Dtos.WorkShift.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.Base;
using Kantoku.Persistence.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Persistence.Filters;
using ApiFilter = Kantoku.Api.Filters.Domains;
using DomainFilter = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Services;

public interface IWorkShiftService
{
    Task<ResultDto<WorkShiftsResponseDto>> GetAllWorkShifts(ApiFilter.WorkShiftFilter filter);
    Task<ResultDto<WorkShiftResponseDto>> GetWorkShiftById(Guid workShiftId);

    // CRUD use 
    Task<ResultDto<WorkShiftResponseDto>> CreateWorkShift(CreateWorkShiftRequestDto createDto);
    Task<ResultDto<WorkShiftResponseDto>> UpdateWorkShift(Guid workShiftId, UpdateWorkShiftRequestDto updateDto);
    Task<ResultDto<bool>> DeleteWorkShift(Guid workShiftId);

    // // CQRS use
    // Task<ResultDto<Guid>> CreateWorkShift2(CreateWorkShiftRequestDto createDto);
    // Task<ResultDto<Guid>> UpdateWorkShift2(Guid workShiftId, UpdateWorkShiftRequestDto updateDto);
}

[Service(ServiceLifetime.Scoped)]
public class WorkShiftService : BaseService<WorkShiftService>, IWorkShiftService
{
    private readonly IWorkShiftRepository workShiftRepository;
    private readonly IFilterMapper<ApiFilter.WorkShiftFilter, DomainFilter.WorkShiftFilter> workShiftFilterMapper;

    public WorkShiftService(
        IWorkShiftRepository workShiftRepository,
        IFilterMapper<ApiFilter.WorkShiftFilter, DomainFilter.WorkShiftFilter> workShiftFilterMapper,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
    : base(logger, httpContextAccessor)
    {
        this.workShiftRepository = workShiftRepository;
        this.workShiftFilterMapper = workShiftFilterMapper;
    }

    public async Task<ResultDto<WorkShiftsResponseDto>> GetAllWorkShifts(ApiFilter.WorkShiftFilter filter)
    {
        var domainFilter = workShiftFilterMapper.MapToDomain(filter);
        var (workShifts, totalRecords) = await workShiftRepository.GetByFilter(domainFilter, new WorkShiftQueryableOptions());
        if (workShifts is null || !workShifts.Any() || totalRecords == 0)
        {
            logger.Warning("No work shifts found");
            return new ErrorResultDto<WorkShiftsResponseDto>(ResponseCodeConstant.WORK_SHIFT_NOT_EXIST);
        }
        var result = workShifts.ToWorkShiftsResponseDto(filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<WorkShiftsResponseDto>(result);
    }

    public async Task<ResultDto<WorkShiftResponseDto>> GetWorkShiftById(Guid workShiftId)
    {
        var option = new WorkShiftQueryableOptions();
        var workShift = await workShiftRepository.GetById(workShiftId, option);
        if (workShift is null)
        {
            logger.Warning("Work shift with ID {Id} not found", workShiftId);
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_NOT_EXIST);
        }
        var result = workShift.ToWorkShiftResponseDto();
        return new SuccessResultDto<WorkShiftResponseDto>(result);
    }

    public async Task<ResultDto<WorkShiftResponseDto>> CreateWorkShift(CreateWorkShiftRequestDto createDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var isValid = ValidateWorkShiftDto(createDto.CheckInTime, createDto.CheckOutTime, createDto.WorkShiftBreaks);
        if (!isValid)
        {
            logger.Warning("Invalid work shift data");
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_INPUT_DATA_INVALID);
        }
        var workShift = createDto.ToEntity(orgUid);
        if (workShift is null)
        {
            logger.Error("Failed to create work shift");
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_INPUT_DATA_INVALID);
        }

        var createdWorkShift = await workShiftRepository.Create(workShift, new WorkShiftQueryableOptions());
        if (createdWorkShift is null)
        {
            logger.Warning("Failed to create work shift");
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_CREATE_FAILED);
        }
        var result = createdWorkShift.ToWorkShiftResponseDto();
        return new SuccessResultDto<WorkShiftResponseDto>(result);
    }

    public async Task<ResultDto<WorkShiftResponseDto>> UpdateWorkShift(Guid workshiftUid, UpdateWorkShiftRequestDto updateDto)
    {
        var existWorkShift = await workShiftRepository.GetById(workshiftUid, new WorkShiftQueryableOptions());
        if (existWorkShift is null)
        {
            logger.Warning("Work shift with ID {Id} not found for update", workshiftUid);
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_NOT_EXIST);
        }

        existWorkShift.UpdateFromDto(updateDto);

        var isValid = ValidateWorkShift(existWorkShift.CheckInTime, existWorkShift.CheckOutTime, existWorkShift.WorkShiftBreakTimes);
        if (!isValid)
        {
            logger.Warning("Invalid work shift data");
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_INPUT_DATA_INVALID);
        }

        var updatedWorkShift = await workShiftRepository.Update(existWorkShift, new WorkShiftQueryableOptions());
        if (updatedWorkShift is null)
        {
            logger.Warning("Failed to update work shift with ID {Id}", workshiftUid);
            return new ErrorResultDto<WorkShiftResponseDto>(ResponseCodeConstant.WORK_SHIFT_UPDATE_FAILED);
        }
        var result = updatedWorkShift.ToWorkShiftResponseDto();
        return new SuccessResultDto<WorkShiftResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteWorkShift(Guid workshiftUid)
    {
        var existWorkShift = await workShiftRepository.GetById(workshiftUid, new WorkShiftQueryableOptions());
        if (existWorkShift is null)
        {
            logger.Warning("Work shift with ID {Id} not found for deletion", workshiftUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.WORK_SHIFT_NOT_EXIST, false);
        }
        existWorkShift.IsDeleted = true;
        var isDeleted = await workShiftRepository.Update(existWorkShift);
        if (!isDeleted)
        {
            logger.Warning("Failed to delete work shift with ID {Id}", workshiftUid);
            return new ErrorResultDto<bool>(ResponseCodeConstant.WORK_SHIFT_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private static bool ValidateWorkShiftDto(string checkInTime, string checkOutTime, IEnumerable<WorkShiftBreakRequestDto> workShiftBreaks)
    {
        if (!TimeOnly.TryParse(checkInTime, out var checkInTimeOnly) ||
            !TimeOnly.TryParse(checkOutTime, out var checkOutTimeOnly))
        {
            return false;
        }

        if (checkInTimeOnly >= checkOutTimeOnly)
        {
            return false;
        }

        var breakTimeDto = workShiftBreaks
            .Where(br =>
            {
                if (!TimeOnly.TryParse(br.BreakInTime, out var breakInTimeOnly) ||
                    !TimeOnly.TryParse(br.BreakOutTime, out var breakOutTimeOnly))
                    return false;

                return breakInTimeOnly >= checkInTimeOnly &&
                       breakOutTimeOnly <= checkOutTimeOnly &&
                       breakInTimeOnly < breakOutTimeOnly;
            })
            .Select(b => new WorkShiftBreakTime
            {
                BreakInTime = TimeOnly.Parse(b.BreakInTime),
                BreakOutTime = TimeOnly.Parse(b.BreakOutTime)
            });

        var totalRequiredTime = (float)((checkOutTimeOnly - checkInTimeOnly).TotalSeconds - breakTimeDto.Sum(b => (b.BreakOutTime - b.BreakInTime).TotalSeconds)) / 3600;
        totalRequiredTime = (float)(Math.Round(totalRequiredTime * 4) / 4);

        if (totalRequiredTime <= 0)
        {
            return false;
        }

        return true;
    }

    private static bool ValidateWorkShift(TimeOnly checkInTime, TimeOnly checkOutTime, IEnumerable<WorkShiftBreakTime> workShiftBreaks)
    {
        if (checkInTime >= checkOutTime)
        {
            return false;
        }

        var breakTimeDto = workShiftBreaks
            .Where(br =>
            {
                if (br.BreakInTime >= br.BreakOutTime)
                    return false;

                return br.BreakInTime >= checkInTime &&
                       br.BreakOutTime <= checkOutTime &&
                       br.BreakInTime < br.BreakOutTime;
            })
            .Select(b => new WorkShiftBreakTime
            {
                BreakInTime = b.BreakInTime,
                BreakOutTime = b.BreakOutTime
            });

        var totalRequiredTime = (float)((checkOutTime - checkInTime).TotalSeconds - breakTimeDto.Sum(b => (b.BreakOutTime - b.BreakInTime).TotalSeconds)) / 3600;
        totalRequiredTime = (float)(Math.Round(totalRequiredTime * 4) / 4);

        if (totalRequiredTime <= 0)
        {
            return false;
        }

        return true;
    }
}
