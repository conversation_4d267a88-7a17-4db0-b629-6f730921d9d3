namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for contractor queries - contains only business logic, no HTTP concerns
/// </summary>
public class ContractorFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in contractor name, code, or contact information
    /// </summary>
    public string? Keyword { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}

