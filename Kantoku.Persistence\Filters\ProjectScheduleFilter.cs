namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for project schedule queries - contains only business logic, no HTTP concerns
/// </summary>
public class ProjectScheduleFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in schedule details
    /// </summary>
    public string? SearchKeyword { get; set; }

    /// <summary>
    /// Start date for schedule filtering (required)
    /// </summary>
    public DateOnly FromDate { get; set; }

    /// <summary>
    /// End date for schedule filtering (required)
    /// </summary>
    public DateOnly ToDate { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize search keyword
        if (!string.IsNullOrWhiteSpace(SearchKeyword))
        {
            SearchKeyword = SearchKeyword.Trim();
        }

        // Validate date range
        if (FromDate > ToDate)
        {
            // Swap if from date is after to date
            (FromDate, ToDate) = (ToDate, FromDate);
        }

        // Set default dates if not provided
        if (FromDate == default)
        {
            FromDate = DateOnly.FromDateTime(DateTime.Today);
        }

        if (ToDate == default)
        {
            ToDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30));
        }
    }
}

