
namespace Kantoku.SharedKernel.Helpers;

public static partial class StringHelper
{
    private const string PasswordGeneratorPattern = "abcdefghijklmnopqrstuvwxyz0123456789";
    public static string PasswordGenerate()
    {
        var random = new Random();
        return new string(Enumerable.Repeat(PasswordGeneratorPattern, 6)
          .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    public static string GenerateOTP()
    {
        var random = new Random();
        return random.Next(100000, 999999).ToString();
    }

    public static string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, 12);
    }

    public static bool VerifyPassword(string providedPassword, string hashedPassword)
    {
        try
        {
            return BCrypt.Net.BCrypt.Verify(providedPassword, hashedPassword);
        }
        catch (Exception)
        {
            return false;
        }
    }

    public static bool IsNotNullOrEmpty(string? value)
    {
        //check if value is null
        if (value is null)
            return false;

        //check if value is empty
        if (value.Equals(string.Empty))
            return false;

        //check if value is whitespace
        if (value.Replace(" ", "").Equals(string.Empty))
            return false;

        return true;
    }

    public static bool IsNullOrEmpty(string? value)
    {
        return string.IsNullOrEmpty(value);
    }
}
