using Kantoku.Domain.Models;
using Kantoku.Persistence.Repositories;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;

namespace Kantoku.Api.Services;

public interface IEmpContractService
{
    Task<EmpContract?> CreateEmpContract(EmpContract e);
    Task<EmpContract?> UpdateEmpContract(EmpContract e);
}

[Service(ServiceLifetime.Scoped)]
public class EmpContractService : IEmpContractService
{
    private readonly IEmpContractRepository empContractRepository;
    private readonly Serilog.ILogger logger;
    public EmpContractService(IEmpContractRepository empContractRepository, Serilog.ILogger logger)
    {
        this.empContractRepository = empContractRepository;
        this.logger = logger.ForContext<EmpContractService>();
    }

    public async Task<EmpContract?> CreateEmpContract(EmpContract e)
    {
        try
        {
            var result = await empContractRepository.Create(e);
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employee contract for employee {EmployeeUid}", e.EmployeeUid);
            throw new BusinessException(ResponseCodeConstant.CONTRACT_CREATE_FAILED);
        }
    }

    public async Task<EmpContract?> UpdateEmpContract(EmpContract e)
    {
        try
        {
            var result = await empContractRepository.Update(e);
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee contract for employee {EmployeeUid}", e.EmployeeUid);
            throw new BusinessException(ResponseCodeConstant.CONTRACT_UPDATE_FAILED);
        }
    }
}