using Kantoku.Domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Kantoku.Persistence.Config;
using Kantoku.Persistence.Services;
using Kantoku.Persistence.EntityConfigurations;
using File = Kantoku.Domain.Models.File;

namespace Kantoku.Persistence.Contexts;

public partial class ApplicationDbContext : AuditableDbContext
{
    private readonly ApplicationDbConfig dbConfig;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        IOptions<ApplicationDbConfig> dbConfig,
        ITenantContext tenantContext
    )
        : base(options, tenantContext)
    {
        this.dbConfig = dbConfig.Value;
    }

    public virtual required DbSet<Account> Accounts { get; set; }
    public virtual required DbSet<Category> Categories { get; set; }
    public virtual required DbSet<EmpContract> EmpContracts { get; set; }
    public virtual required DbSet<Employee> Employees { get; set; }
    public virtual required DbSet<EmployeeInvitation> EmployeeInvitations { get; set; }
    public virtual required DbSet<EmployeeRole> EmployeeRoles { get; set; }
    public virtual required DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
    public virtual required DbSet<EmployeeShift> EmployeeShifts { get; set; }
    public virtual required DbSet<File> Files { get; set; }
    public virtual required DbSet<Function> Functions { get; set; }
    public virtual required DbSet<GlobalConfig> GlobalConfigs { get; set; }
    public virtual required DbSet<InputCost> InputCosts { get; set; }
    public virtual required DbSet<InputCostItem> InputCostItems { get; set; }
    public virtual required DbSet<Item> Items { get; set; }
    public virtual required DbSet<ItemPrice> ItemPrices { get; set; }
    public virtual required DbSet<Language> Languages { get; set; }
    public virtual required DbSet<LeaveType> LeaveTypes { get; set; }
    public virtual required DbSet<MonthlyReport> MonthlyReports { get; set; }
    public virtual required DbSet<Org> Orgs { get; set; }
    public virtual required DbSet<PaymentType> PaymentTypes { get; set; }
    public virtual required DbSet<Position> Positions { get; set; }
    public virtual required DbSet<Project> Projects { get; set; }
    public virtual required DbSet<ProjectDailyReport> ProjectDailyReports { get; set; }
    public virtual required DbSet<ProjectManager> ProjectManagers { get; set; }
    public virtual required DbSet<ProjectSchedule> ProjectSchedules { get; set; }
    public virtual required DbSet<OutSource> OutSources { get; set; }
    public virtual required DbSet<OutSourcePrice> OutSourcePrices { get; set; }
    public virtual required DbSet<OutSourceShift> OutSourceShifts { get; set; }
    public virtual required DbSet<ProjectType> ProjectTypes { get; set; }
    public virtual required DbSet<ProjectRankingCost> ProjectRankingCosts { get; set; }
    public virtual required DbSet<Ranking> Rankings { get; set; }
    public virtual required DbSet<EmployeeCost> EmployeeCosts { get; set; }
    public virtual required DbSet<Request> Requests { get; set; }
    public virtual required DbSet<RequestType> RequestTypes { get; set; }
    public virtual required DbSet<Role> Roles { get; set; }
    public virtual required DbSet<Structure> Structures { get; set; }
    public virtual required DbSet<Status> Statuses { get; set; }
    public virtual required DbSet<UserInfo> UserInfos { get; set; }
    public virtual required DbSet<RoleFunction> RoleFunctions { get; set; }
    public virtual required DbSet<Vendor> Vendors { get; set; }
    public virtual required DbSet<WorkShift> WorkShifts { get; set; }
    public virtual required DbSet<ProjectWorkShift> ProjectWorkShifts { get; set; }
    public virtual required DbSet<EntryType> EntryTypes { get; set; }
    public virtual required DbSet<Manufacturer> Manufacturers { get; set; }
    public virtual required DbSet<EventCalendar> EventCalendars { get; set; }
    public virtual required DbSet<Construction> Constructions { get; set; }
    public virtual required DbSet<ConstructionCost> ConstructionCosts { get; set; }
    public virtual required DbSet<Contractor> Contractors { get; set; }
    public virtual required DbSet<Customer> Customers { get; set; }
    public virtual required DbSet<CustomerType> CustomerTypes { get; set; }
    public virtual required DbSet<Notification> Notifications { get; set; }
    public virtual required DbSet<EmployeeNotification> EmployeeNotifications { get; set; }
    public virtual required DbSet<NotificationTarget> NotificationTargets { get; set; }
    public virtual required DbSet<DeviceToken> DeviceTokens { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseNpgsql(dbConfig.BuildConnectionString(), x => x.MigrationsHistoryTable("__EFMigrationsHistory", dbConfig.Schema));

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema(dbConfig.Schema);

        modelBuilder.HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

        var dateTimeConverter = new ValueConverter<DateTime, DateTime>(
            v => v.Kind == DateTimeKind.Unspecified ? DateTime.SpecifyKind(v, DateTimeKind.Utc) : v.ToUniversalTime(),
            v => DateTime.SpecifyKind(v, DateTimeKind.Utc)
        );

        var nullableDateTimeConverter = new ValueConverter<DateTime?, DateTime?>(
            v => v.HasValue ? (v.Value.Kind == DateTimeKind.Unspecified ?
                 DateTime.SpecifyKind(v.Value, DateTimeKind.Utc) :
                 v.Value.ToUniversalTime()) : null,
            v => v.HasValue ? DateTime.SpecifyKind(v.Value, DateTimeKind.Utc) : null
        );

        // remove when migration to utc is done
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.GetColumnType() == "timestamp with time zone" || property.GetColumnType() == "timestamptz")
                {
                    if (property.ClrType == typeof(DateTime))
                    {
                        property.SetValueConverter(dateTimeConverter);
                    }
                    else if (property.ClrType == typeof(DateTime?))
                    {
                        property.SetValueConverter(nullableDateTimeConverter);
                    }
                }
            }
        }

        foreach (var entityType in modelBuilder.Model.GetEntityTypes()
        .Where(e => typeof(IAuditableEntity).IsAssignableFrom(e.ClrType)))
        {
            modelBuilder.Entity(entityType.ClrType)
                .Property("CreatedBy")
                .HasColumnName("created_by");

            modelBuilder.Entity(entityType.ClrType)
                .Property("LastModifiedBy")
                .HasColumnName("last_modified_by");

            modelBuilder.Entity(entityType.ClrType)
                .Property("CreatedTime")
                .HasColumnName("created_time")
                .HasColumnType("timestamp with time zone");

            modelBuilder.Entity(entityType.ClrType)
                .Property("LastModifiedTime")
                .HasColumnName("last_modified_time")
                .HasColumnType("timestamp with time zone");
        }

        modelBuilder.ApplyConfiguration(new AccountConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new AuditLogConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new CategorizedCostConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new CategoryConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ConstructionConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ConstructionCostConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ConstructionPaymentRequestConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ContractorConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new CustomerConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new CustomerTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new DeviceTokenConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmpContractConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeInvitationConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeLeaveConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeNotificationConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeCostConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeRoleConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeShiftConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EmployeeShiftDetailConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EntryTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new EventCalendarConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new FileConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new FunctionConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new GlobalConfigConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new InputCostConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new InputCostItemConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ItemConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ItemPriceConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new LanguageConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new LeaveTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ManufacturerConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new MonthlyReportConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new NotificationConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new NotificationTargetConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new OrgConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new OutSourceConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new OutSourcePriceConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new OutSourceShiftConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new PaymentTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new PositionConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectDailyReportConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectManagerConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectRankingCostConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectScheduleConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new ProjectWorkShiftConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new RankingConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new RequestConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new RequestTypeConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new RoleConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new RoleFunctionConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new StatusConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new StructureConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new UserInfoConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new VendorConfiguration(dbConfig.Schema));
        modelBuilder.ApplyConfiguration(new WorkShiftConfiguration(dbConfig.Schema));

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
