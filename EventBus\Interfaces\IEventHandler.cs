namespace EventBus.Interfaces;

/// <summary>
/// Interface for handling events
/// </summary>
/// <typeparam name="T">Type of the event to handle</typeparam>
public interface IEventHandler<in T> where T : class, IIntegrationEvent
{
    /// <summary>
    /// Handles the event
    /// </summary>
    /// <param name="event">The event to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task HandleAsync(T @event, CancellationToken cancellationToken = default);
}
