namespace Kantoku.Persistence.Filters;

public class EventCalendarFilter : BaseFilter
{
    /// <summary>
    /// The keyword to search for
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Whether the event is a day off
    /// </summary>
    public bool? IsDayOff { get; set; }

    /// <summary>
    /// Whether the event is recurring
    /// </summary>
    public bool? IsRecurring { get; set; }

    public override void Validate()
    {
        base.Validate();

        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }
    }
}