# Tenant Context Architecture

## Problem
The persistence layer was directly depending on `IHttpContextAccessor` to access tenant-specific information (accountId, orgId, employeeId), which violated clean architecture principles by coupling the persistence layer to HTTP concerns.

## Solution
We've implemented a **Tenant Context Service** pattern that abstracts tenant information access through the `ITenantContext` interface.

## Architecture

### Core Interface
```csharp
public interface ITenantContext
{
    Guid GetCurrentAccountUid();
    Guid GetCurrentOrgUid();
    Guid GetCurrentEmployeeUid();
    bool TryGetCurrentAccountUid(out Guid accountUid);
    bool TryGetCurrentOrgUid(out Guid orgUid);
    bool TryGetCurrentEmployeeUid(out Guid employeeUid);
    Task<bool> IsSuperUserAsync();
    Task<bool> IsOrgSuperUserAsync();
}
```

### Implementations

#### 1. HttpTenantContext (API Layer)
- Reads tenant information from `HttpContext.Items`
- Used in web API scenarios
- Located in `Kantoku.Api.Services`

#### 2. StaticTenantContext (Persistence Layer)
- Uses pre-configured tenant information
- Used in background jobs, console apps, testing
- Located in `Kantoku.Persistence.Services`

## Usage Examples

### 1. API Layer Setup (Program.cs)
```csharp
// Add persistence services
builder.Services.AddDbContext(...);
builder.Services.AddRepositories();
builder.Services.AddQueryables();
builder.Services.AddTenantContext(); // Adds default StaticTenantContext

// Override with HTTP-based implementation for API scenarios
builder.Services.AddHttpTenantContext();
```

### 2. Background Job Setup
```csharp
// For tenant-specific background jobs
services.AddStaticTenantContext(accountUid, orgUid, employeeUid);

// For system-level background jobs
services.AddSystemTenantContext();
```

### 3. Console Application Setup
```csharp
var services = new ServiceCollection();
services.AddDbContext<ApplicationDbContext>(...);
services.AddRepositories();
services.AddQueryables();

// For specific tenant operations
services.AddStaticTenantContext(accountUid, orgUid, employeeUid);

// Or for system operations
services.AddSystemTenantContext();

var serviceProvider = services.BuildServiceProvider();
```

### 4. Unit Testing
```csharp
[Test]
public async Task TestRepository()
{
    var services = new ServiceCollection();
    services.AddDbContext<ApplicationDbContext>(options => 
        options.UseInMemoryDatabase("TestDb"));
    
    // Mock tenant context for testing
    var mockTenantContext = new Mock<ITenantContext>();
    mockTenantContext.Setup(x => x.GetCurrentOrgUid())
                    .Returns(testOrgId);
    services.AddSingleton(mockTenantContext.Object);
    
    services.AddScoped<IEmployeeRepository, EmployeeRepository>();
    
    var serviceProvider = services.BuildServiceProvider();
    var repository = serviceProvider.GetService<IEmployeeRepository>();
    
    // Test repository methods...
}
```

## Migration Guide

### Step 1: Update Repository Constructors
**Before:**
```csharp
public EmployeeRepository(
    ApplicationDbContext context,
    Serilog.ILogger logger,
    ITenantContext tenantContext)
    : base(context, logger, httpContextAccessor)
```

**After:**
```csharp
public EmployeeRepository(
    ApplicationDbContext context,
    Serilog.ILogger logger,
    ITenantContext tenantContext)
    : base(context, logger, tenantContext)
```

### Step 2: Update Service Registration
**Before:**
```csharp
// In API Program.cs
builder.Services.AddScoped<IEmployeeRepository, EmployeeRepository>();
```

**After:**
```csharp
// In API Program.cs
builder.Services.AddRepositories(); // Registers all repositories
builder.Services.AddHttpTenantContext(); // Configures HTTP-based tenant context
```

### Step 3: Background Jobs
**Before:**
```csharp
// Background job couldn't easily access tenant context
```

**After:**
```csharp
// In background job setup
services.AddStaticTenantContext(accountUid, orgUid, employeeUid);
// Now repositories work seamlessly in background jobs
```

## Benefits

1. **Clean Architecture**: Persistence layer no longer depends on HTTP concerns
2. **Testability**: Easy to mock tenant context for unit tests
3. **Flexibility**: Same repositories work in web, console, and background job scenarios
4. **Maintainability**: Centralized tenant context logic
5. **Performance**: No need to access HttpContext in every repository method

## Alternative Approaches Considered

### 1. Parameter Passing
Pass tenant IDs as parameters to every repository method.
- **Pros**: Simple, explicit
- **Cons**: Verbose, error-prone, breaks existing API

### 2. Ambient Context (ThreadStatic)
Use thread-local storage for tenant context.
- **Pros**: No dependency injection needed
- **Cons**: Not async-safe, hard to test, global state

### 3. Repository Factory Pattern
Create repositories with pre-configured tenant context.
- **Pros**: Immutable tenant context per repository instance
- **Cons**: More complex DI setup, multiple repository instances

The **Tenant Context Service** pattern was chosen as it provides the best balance of clean architecture, testability, and ease of use.
