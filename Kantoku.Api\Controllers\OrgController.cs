using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Dtos.Orgz.Request;
using Microsoft.AspNetCore.Authorization;
using Orgz = Kantoku.Domain.Models.Org;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Orgz.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
public class OrgController : BaseController
{
    private readonly IOrgService orgService;
    private readonly IAuditLogService auditLogService;

    public OrgController(
        ITResponseFactory responseFactory,
        IOrgService orgService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.orgService = orgService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Gets all organizations associated with the current authenticated account
    /// </summary>
    /// <returns>List of associated organizations</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet]
    public async Task<GeneralResponse<OrgsResponseDto>> GetAssociatedOrgs()
    {
        try
        {
            var result = await orgService.GetAssociatedOrgs();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Gets organization details by ID for associated account or system admin
    /// </summary>
    /// <param name="id">Organization ID to retrieve</param>
    /// <returns>Organization details</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet("{id}")]
    public async Task<GeneralResponse<OrgResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            var result = await orgService.GetOrgById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Creates a new organization for the authenticated account
    /// </summary>
    /// <param name="requestDto">Organization creation details</param>
    /// <returns>Created organization details</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPost]
    public async Task<GeneralResponse<OrgResponseDto>> CreateOrg([FromBody] CreateOrgRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OrgResponseDto>();

            var result = await orgService.CreateOrg(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Updates an organization's details (restricted to organization owner)
    /// </summary>
    /// <param name="id">Organization ID to update</param>
    /// <param name="requestDto">Updated organization details</param>
    /// <returns>Updated organization details</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPut("{id}")]
    public async Task<GeneralResponse<OrgResponseDto>> UpdateOrg([FromRoute] Guid id, [FromBody] UpdateOrgRequestDto requestDto)
    {
        try
        {
            var result = await orgService.UpdateOrg(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Gets all organizations owned by the current authenticated account
    /// </summary>
    /// <returns>List of owned organizations</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet("owned-orgs")]
    public async Task<GeneralResponse<OrgsResponseDto>> GetOwnedOrgs()
    {
        try
        {
            var result = await orgService.GetOwnedOrgs();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Uploads a logo for an organization
    /// </summary>
    /// <param name="id">The unique identifier of the organization</param>
    /// <param name="file">The logo file to upload</param>
    /// <returns>The uploaded logo details</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPost("{id}/logo")]
    public async Task<GeneralResponse<AvatarResponseDto>> UploadLogo([FromRoute] Guid id, IFormFile file)
    {
        try
        {
            var result = await orgService.UploadLogo(id, file);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AvatarResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Downloads the logo for an organization
    /// </summary>
    /// <param name="id">The unique identifier of the organization</param>
    /// <returns>The downloaded logo details</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet("{id}/logo")]
    public async Task<GeneralResponse<AvatarResponseDto>> DownloadLogo([FromRoute] Guid id)
    {
        try
        {
            var result = await orgService.DownloadLogo(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AvatarResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Gets all organizations associated with a login ID (deprecated)
    /// </summary>
    /// <param name="loginId">Login ID to query organizations for</param>
    /// <returns>List of organizations associated with the login ID</returns>
    [AllowAnonymous]
    [HttpGet("get-orgs-login")]
    [Obsolete("This endpoint will be deprecated soon")]
    public async Task<GeneralResponse<OrgsResponseDto>> GetOrgLogin([FromQuery] string loginId)
    {
        try
        {
            var result = await orgService.GetOrgsByLoginId(loginId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OrgsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Gets audit logs for a specific organization
    /// </summary>
    /// <param name="id">Organization ID to get logs for</param>
    /// <param name="filter">Filter parameters for the audit logs</param>
    /// <returns>Filtered audit log entries for the organization</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetOrgLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Orgz>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
