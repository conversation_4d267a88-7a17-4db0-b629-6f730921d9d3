using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.EmployeeCost.Response;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EmployeeCostMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a collection of EmployeeCost entities to an EmployeeCostsResponseDto
    /// </summary>  
    /// <param name="employeeCosts">The collection of EmployeeCost entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <param name="rankings">The rankings</param>
    /// <returns>The mapped EmployeeCostsResponseDto</returns>   
    public static EmployeeCostsResponseDto ToEmployeeCostsResponseDto(
        this IEnumerable<EmployeeCost> employeeCosts,
        int pageNum,
        int pageSize,
        int totalRecords,
        IEnumerable<Ranking> rankings)
    {
        if (employeeCosts == null)
            return new EmployeeCostsResponseDto();

        var items = employeeCosts.ToEmployeeCostResponseDto(rankings);

        return new EmployeeCostsResponseDto
        {
            Items = items,
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    private static IEnumerable<EmployeeCostResponseDto> ToEmployeeCostResponseDto(
        this IEnumerable<EmployeeCost> employeeCosts,
        IEnumerable<Ranking> rankings
    )
    {
        if (employeeCosts == null)
            yield break;

        foreach (var ec in employeeCosts)
        {
            var employee = ec.Employee;
            var ranking = rankings
                .Where(r => r.RankingUid == employee.RankingUid)
                .FirstOrDefault();

            yield return new EmployeeCostResponseDto
            {
                EmployeeId = employee.EmployeeUid,
                EmployeeName = employee.EmployeeName,
                RankId = ranking?.RankingUid,
                RankName = ranking?.RankingName,
                CostAmount = ec.DailyCostAmount,
                AverageCostAmount = ranking?.AverageValue
            };
        }
    }

    #endregion
}