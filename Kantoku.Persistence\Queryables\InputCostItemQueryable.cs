using Kantoku.Persistence.Contexts;
using Kantoku.Domain.Models;
using Kantoku.Persistence.Filters;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Queryables;

public class InputCostItemQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedItem { get; set; } = false;
    public bool IncludedVendor { get; set; } = false;
    public bool IncludedConstruction { get; set; } = false;
    public bool IncludedInputCost { get; set; } = false;
}

public interface IInputCostItemQueryable
{
    IQueryable<InputCostItem> GetInputCostItemsQuery(
        InputCostItemQueryableOptions options
    );
    IQueryable<InputCostItem> GetInputCostItemsQueryIncluded(
        InputCostItemQueryableOptions options,
        IQueryable<InputCostItem>? query = null
    );
    IQueryable<InputCostItem> GetInputCostItemsQueryFiltered(
        InputCostItemCategoryFilter filter,
        InputCostItemQueryableOptions options,
        IQueryable<InputCostItem>? query = null
    );
}

public class InputCostItemQueryable(ApplicationDbContext context) :
    BaseQueryable<InputCostItem>(context), IInputCostItemQueryable
{
    public IQueryable<InputCostItem> GetInputCostItemsQuery(
        InputCostItemQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<InputCostItem> GetInputCostItemsQueryIncluded(
        InputCostItemQueryableOptions options,
        IQueryable<InputCostItem>? query = null
    )
    {
        query ??= GetInputCostItemsQuery(options);
        if (options.IncludedItem || options.IncludedAll)
        {
            query = query.Include(p => p.Item)
                .ThenInclude(p => p.Category);
        }
        if (options.IncludedVendor || options.IncludedAll)
        {
            query = query.Include(p => p.Vendor);
        }
        if (options.IncludedConstruction || options.IncludedAll)
        {
            query = query.Include(p => p.Construction);
        }
        if (options.IncludedInputCost || options.IncludedAll)
        {
            query = query.Include(p => p.InputCost);
        }

        return query;
    }

    public IQueryable<InputCostItem> GetInputCostItemsQueryFiltered(
        InputCostItemCategoryFilter filter,
        InputCostItemQueryableOptions options,
        IQueryable<InputCostItem>? query = null
    )
    {
        query ??= GetInputCostItemsQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.InputCostId is not null && filter.InputCostId != Guid.Empty)
        {
            query = query.Where(p => p.InputCostUid == filter.InputCostId);
        }
        if (filter.ConstructionId is not null && filter.ConstructionId != Guid.Empty)
        {
            query = query.Where(p => p.ConstructionUid == filter.ConstructionId);
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(p => p.Construction.ProjectUid == filter.ProjectId);
        }
        if (filter.CategoryId is not null && filter.CategoryId != Guid.Empty)
        {
            query = query.Where(p => p.Item.CategoryUid == filter.CategoryId);
        }
        if (filter.VendorId is not null && filter.VendorId != Guid.Empty)
        {
            query = query.Where(p => p.VendorUid == filter.VendorId);
        }
        if (DateOnly.TryParse(filter.DateFrom, out var dateFrom))
        {
            query = query.Where(p => p.TransactionDate >= dateFrom);
        }
        if (DateOnly.TryParse(filter.DateTo, out var dateTo))
        {
            query = query.Where(p => p.TransactionDate <= dateTo);
        }
        return query;
    }

}

