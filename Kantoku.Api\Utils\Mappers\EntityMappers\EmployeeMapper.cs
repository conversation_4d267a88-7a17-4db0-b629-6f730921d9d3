using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.Employee.Request;
using Kantoku.Api.Dtos.Employee.Response;
using Kantoku.Api.Dtos.EmployeeCost.Response;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class EmployeeMapper
{
    #region Entity to DTO mappings
    /// <summary>
    /// Map an employee entity to a base employee response dto
    /// </summary>
    /// <param name="employee">The employee entity to map</param>
    /// <returns>A base employee response dto</returns>
    public static BaseEmployeeResponseDto ToBaseEmployeeResponseDto(this Employee employee)
    {
        if (employee == null)
            return new BaseEmployeeResponseDto();

        return new BaseEmployeeResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            EmployeeCode = employee.EmployeeCode,
            EmployeeName = employee.EmployeeName
        };
    }

    /// <summary>
    /// Map an employee entity to a base employees response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <returns>A base employees response dto</returns>
    public static IEnumerable<BaseEmployeeResponseDto> ToBaseEmployeeResponseDto(
        this IEnumerable<Employee> employees)
    {
        if (employees == null || !employees.Any())
            yield break;

        foreach (var employee in employees)
        {
            yield return ToBaseEmployeeResponseDto(employee);
        }
    }

    /// <summary>
    /// Map an employee entity to a base employees response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <returns>A base employees response dto</returns>
    public static BaseEmployeesResponseDto ToBaseEmployeesResponseDto(
        this IEnumerable<Employee> employees)
    {
        if (employees == null || !employees.Any())
            return new BaseEmployeesResponseDto();

        return new BaseEmployeesResponseDto
        {
            Items = employees.ToBaseEmployeeResponseDto()
        };
    }

    /// <summary>
    /// Map an employee entity to a simple employee response dto
    /// </summary>
    /// <param name="employee">The employee entity to map</param>
    /// <returns>A simple employee response dto</returns>
    public static SimpleEmployeeResponseDto ToSimpleEmployeeResponseDto(this Employee employee)
    {
        if (employee == null)
            return new SimpleEmployeeResponseDto();

        return new SimpleEmployeeResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            EmployeeCode = employee.EmployeeCode,
            EmployeeName = employee.EmployeeName,
            RankId = employee.Ranking?.RankingUid.ToString(),
            RankName = employee.Ranking?.RankingName
        };
    }

    /// <summary>
    /// Map an employee entity to a simple employee response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <returns>A simple employee response dto</returns>
    public static IEnumerable<SimpleEmployeeResponseDto> ToSimpleEmployeeResponseDto(
        this IEnumerable<Employee> employees)
    {
        if (employees == null || !employees.Any())
            yield break;

        foreach (var employee in employees)
        {
            yield return ToSimpleEmployeeResponseDto(employee);
        }
    }

    /// <summary>
    /// Map an employee entity to a simple employees response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <param name="ranks">The ranks entity to map</param>
    /// <returns>A simple employees response dto</returns>
    public static SimpleEmployeesResponseDto ToSimpleEmployeesResponseDto(
        this IEnumerable<Employee> employees,
        IEnumerable<Ranking> ranks)
    {
        if (employees == null || !employees.Any())
            return new SimpleEmployeesResponseDto();

        return new SimpleEmployeesResponseDto
        {
            Items = employees.ToSimpleEmployeeResponseDto()
        };
    }

    /// <summary>
    /// Map an employee entity to an employee info response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <param name="pageIndex">The page index</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total records</param>
    /// <returns>An employee info response dto</returns>
    public static EmployeesResponseDto ToEmployeeInfosResponseDto(
        this IEnumerable<Employee> employees,
        int pageIndex,
        int pageSize,
        int totalRecords)
    {
        if (employees == null || employees.Count() == 0)
            return new EmployeesResponseDto();

        return new EmployeesResponseDto
        {
            Items = employees.ToEmployeeInfosResponseDto(),
            PageIndex = pageIndex,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Map an employee entity to an employee info response dto
    /// </summary>
    /// <param name="employees">The employees entity to map</param>
    /// <returns>An employee info response dto</returns>
    public static IEnumerable<EmployeeResponseDto> ToEmployeeInfosResponseDto(
        this IEnumerable<Employee> employees)
    {
        if (employees == null || !employees.Any())
            yield break;

        foreach (var employee in employees)
        {
            yield return ToEmployeeInfoResponseDto(employee);
        }
    }

    /// <summary>
    /// Map an employee entity to an employee info response dto
    /// </summary>
    /// <param name="employee">The employee entity to map</param>
    /// <returns>An employee info response dto</returns>
    public static EmployeeResponseDto ToEmployeeInfoResponseDto(this Employee employee)
    {
        if (employee == null)
            return new EmployeeResponseDto();

        return new EmployeeResponseDto
        {
            EmployeeId = employee.EmployeeUid.ToString(),
            EmployeeCode = employee.EmployeeCode,
            EmployeeName = employee.EmployeeName,
            EmployeeMails = employee.EmployeeMails?.Select(m => new EmployeeMailInfoDto
            {
                Email = m.Email,
                IsPrimary = m.IsPrimary
            }),
            EmployeePhones = employee.EmployeePhones?.Select(p => new EmployeePhoneInfoDto
            {
                Phone = p.Phone,
                IsPrimary = p.IsPrimary
            }),
            EmployeeAddress = employee.EmployeeAddress,
            Roles = employee.EmployeeRoles?.Select(r => new EmployeeRoleInfoDto
            {
                RoleId = r.RoleUid.ToString(),
                RoleName = r.Role.RoleName
            }),
            WorkingStatus = employee.WorkingStatus,
            WorkingFromDate = employee.WorkingFromDate?.ToString("yyyy-MM-dd"),
            WorkingToDate = employee.WorkingToDate?.ToString("yyyy-MM-dd"),
            StructureId = employee.StructureUid.ToString(),
            StructureName = employee.Structure?.StructureName,
            PositionId = employee.PositionUid.ToString(),
            PositionName = employee.Position?.PositionName,
            StandardWorkingHours = employee.StandardWorkingHours,
            SalaryInMonth = employee.SalaryInMonth,
            HasApprovalAuthority = employee.HasApprovalAuthority,
            RankingId = employee.Ranking?.RankingUid.ToString(),
            RankingName = employee.Ranking?.RankingName
        };
    }

    /// <summary>
    /// Map an employee entity to an employee role response dto
    /// </summary>
    /// <param name="employee">The employee entity to map</param>
    /// <returns>An employee role response dto</returns>
    public static EmployeeRolesResponseDto ToEmployeeRoleResponseDto(
        this Employee employee)
    {
        if (employee == null)
            return new EmployeeRolesResponseDto();

        var roles = employee.EmployeeRoles?
            .Select(er => er.Role)
            .DistinctBy(r => r.RoleUid)
            .Select(r => new EmployeeRoleResponseDto
            {
                RoleId = r.RoleUid.ToString(),
                RoleName = r.RoleName
            }) ?? [];
        return new EmployeeRolesResponseDto
        {
            Items = roles
        };
    }

    /// <summary>
    /// Map an employee entity to an employee cost response dto
    /// </summary>
    /// <param name="employee">The employees entity to map</param>
    /// <param name="dateFrom">The date from filter</param>
    /// <param name="dateTo">The date to filter</param>
    /// <returns>An employee cost response dto</returns>
    public static EmployeeCostsResponseDto ToEmployeeCostsResponseDto(
        this Employee employee,
        DateOnly dateFrom,
        DateOnly dateTo
    )
    {
        if (employee == null)
            return new EmployeeCostsResponseDto();

        var orderedCosts = employee.EmployeeCosts
            .Where(ec => ec.EffectiveDate >= dateFrom && ec.EffectiveDate <= dateTo)
            .OrderBy(ec => ec.EffectiveDate)
            .ToList();

        var items = ToEmployeeCostResponseDto(orderedCosts, employee.Ranking);

        return new EmployeeCostsResponseDto
        {
            Items = items,
            PageNum = 1,
            PageSize = orderedCosts.Count,
            TotalRecords = employee.EmployeeCosts.Count
        };
    }

    private static IEnumerable<EmployeeCostResponseDto> ToEmployeeCostResponseDto(
        IEnumerable<EmployeeCost> employeeCosts,
        Ranking? ranking
    )
    {
        if (employeeCosts == null || !employeeCosts.Any())
            yield break;

        foreach (var employeeCost in employeeCosts)
        {
            var employee = employeeCost.Employee;
            yield return new EmployeeCostResponseDto
            {
                EmployeeId = employee.EmployeeUid,
                EmployeeName = employee.EmployeeName,
                RankId = ranking?.RankingUid,
                RankName = ranking?.RankingName,
                CostAmount = employeeCost.DailyCostAmount,
                AverageCostAmount = ranking?.AverageValue,
            };
        }
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Map an employee invitation request dto to an employee invitation entity
    /// </summary>
    /// <param name="employee">The employee entity to update</param>
    /// <param name="requestDto">The employee invitation request dto to map</param>
    /// <returns>An employee invitation entity</returns>
    public static void UpdateFromDto(
        this Employee employee,
        UpdateEmployeeRequestDto requestDto)
    {
        if (employee == null || requestDto == null)
            return;

        employee.EmployeeMails = requestDto.EmployeeMails?.Any() == true
                ? requestDto.EmployeeMails.Select(mail => new EmployeeMail
                {
                    Email = mail.Email,
                    IsPrimary = mail.IsPrimary,
                }).ToList()
                : employee.EmployeeMails;
        employee.EmployeePhones = requestDto.EmployeePhones?.Any() == true
                ? requestDto.EmployeePhones.Select(phone => new EmployeePhone
                {
                    Phone = phone.Phone,
                    IsPrimary = phone.IsPrimary,
                }).ToList()
                : employee.EmployeePhones;
        employee.EmployeeRoles = requestDto.RoleIds?.Any() == true
                ? requestDto.RoleIds.Select(roleId => new EmployeeRole
                {
                    EmployeeUid = employee.EmployeeUid,
                    RoleUid = Guid.Parse(roleId),
                }).ToList()
                : employee.EmployeeRoles;
        employee.EmployeeAddress = requestDto.EmployeeAddress ?? employee.EmployeeAddress;
        employee.WorkingStatus = requestDto.WorkingStatus ?? employee.WorkingStatus;
        employee.EmployeeType = requestDto.EmployeeType ?? employee.EmployeeType;
        employee.SalaryInMonth = requestDto.SalaryInMonth ?? employee.SalaryInMonth;
        employee.StandardWorkingHours = requestDto.StandardWorkingHours ?? employee.StandardWorkingHours;
        employee.WorkingFromDate = DateTime.TryParse(requestDto.WorkingFromDate, out var workingFromDate)
            ? workingFromDate
            : employee.WorkingFromDate;
        employee.WorkingToDate = DateTime.TryParse(requestDto.WorkingToDate, out var workingToDate)
            ? workingToDate
            : employee.WorkingToDate;
    }


    #endregion
}
