using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Domain.Models;
using Kantoku.Api.Dtos.OutSource.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.OutSource.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class OutSourceController : BaseController
{
    private readonly IOutSourceService outSourceService;
    private readonly IAuditLogService auditLogService;

    public OutSourceController(
        ITResponseFactory responseFactory,
        IOutSourceService outSourceService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.outSourceService = outSourceService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated list of outSources
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>Paginated list of outSources</returns>
    [HttpGet]

    public async Task<GeneralResponse<OutSourcesResponseDto>> GetAll([FromQuery] OutSourceFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourcesResponseDto>();

            var result = await outSourceService.GetOutSourceByFilter(filter);
            return SuccessFromResult(result);
        }

        catch (BusinessException e)
        {
            return Fail<OutSourcesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get outSource details by ID
    /// </summary>
    /// <param name="id">OutSource ID to retrieve</param>
    /// <returns>OutSource details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<OutSourceResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourceResponseDto>();

            var result = await outSourceService.GetOutSourceById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get outSource logo
    /// </summary>
    /// <param name="id">OutSource ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>OutSource logo</returns>
    [AllowAnonymous]
    [HttpGet("{id}/logo")]
    public async Task<dynamic> GetLogo([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await outSourceService.GetLogo(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.OUTSOURCE_LOGO_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get outSource prices
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>OutSource prices</returns>
    [HttpGet("prices")]
    public async Task<GeneralResponse<OutSourcePricesResponseDto>> GetOutSourcePrices([FromQuery] OutSourcePriceFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourcePricesResponseDto>();

            var result = await outSourceService.GetOutSourcePrice(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourcePricesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get outSource prices by ID
    /// </summary>
    /// <param name="id">OutSource ID to retrieve</param>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>OutSource prices</returns>
    [HttpGet("{id}/prices")]
    public async Task<GeneralResponse<OutSourcePricesResponseDto>> GetOutSourcePrices([FromRoute] Guid id, [FromQuery] OutSourcePriceFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourcePricesResponseDto>();

            var result = await outSourceService.GetOutSourcePriceByOutSourceUid(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourcePricesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update OutSource prices by ID
    /// </summary>
    /// <param name="id">OutSource ID to retrieve</param>
    /// <param name="requestDto">Filter parameters for pagination</param>
    /// <returns>OutSource prices</returns>
    [HttpPost("{id}/prices")]
    public async Task<GeneralResponse<OutSourcePriceResponseDto>> CreateOutSourcePrice([FromRoute] Guid id, [FromBody] OutSourcePriceRequestDto requestDto)
    {
        try
        {
            var result = await outSourceService.CreateOutSourcePrice(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourcePriceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update OutSource prices by ID
    /// </summary>
    /// <param name="priceId">OutSource Price ID to retrieve</param>
    /// <param name="requestDto">Filter parameters for pagination</param>
    /// <returns>OutSource prices</returns>
    [HttpPut("prices/{priceId}")]
    public async Task<GeneralResponse<OutSourcePriceResponseDto>> UpdateOutSourcePrice([FromRoute] Guid priceId, [FromBody] OutSourcePriceRequestDto requestDto)
    {
        try
        {
            var result = await outSourceService.UpdateOutSourcePrice(priceId, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourcePriceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new outSource
    /// </summary>
    /// <param name="requestDto">OutSource creation data</param>
    /// <returns>Created outSource details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<OutSourceResponseDto>> CreateOutSource([FromForm] CreateOutSourceRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourceResponseDto>();

            var result = await outSourceService.CreateOutSource(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing outSource
    /// </summary>
    /// <param name="id">OutSource ID to update</param>
    /// <param name="requestDto">Updated outSource data</param>
    /// <returns>Updated outSource details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<OutSourceResponseDto>> UpdateOutSource([FromRoute] Guid id, [FromForm] UpdateOutSourceRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<OutSourceResponseDto>();

            var result = await outSourceService.UpdateOutSource(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<OutSourceResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a outSource
    /// </summary>
    /// <param name="id">OutSource ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteOutSource([FromRoute] Guid id)
    {
        try
        {
            var result = await outSourceService.DeleteOutSource(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a outSource
    /// </summary>
    /// <param name="id">OutSource ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetOutSourceLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<OutSource>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
