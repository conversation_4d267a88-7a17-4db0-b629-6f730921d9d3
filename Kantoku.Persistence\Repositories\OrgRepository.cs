using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Orgz = Kantoku.Domain.Models.Org;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IOrgRepository
{
    Task<IEnumerable<Orgz>> GetOwned();
    Task<IEnumerable<Orgz>> GetAssociated();
    Task<IEnumerable<Orgz>> GetByLoginId(string loginId);
    Task<Orgz?> GetById(Guid orgUid);
    Task<Orgz?> Create(Orgz org);
    Task<Orgz?> Update(Orgz org);
}

public class OrgRepository : BaseRepository<Orgz>, IOrgRepository
{
    public OrgRepository(
        ApplicationDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
    }

    public async Task<IEnumerable<Orgz>> GetOwned()
    {
        var accountId = GetCurrentAccountUid();
        try
        {
            return await context.Orgs
                    .Where(o => o.Employees.Any(e => e.IsOrgOwner && e.AccountUid == accountId))
                    .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting owned orgs for account {AccountId}", accountId);
            return [];
        }
    }

    public async Task<IEnumerable<Orgz>> GetAssociated()
    {
        var accountId = GetCurrentAccountUid();
        try
        {
            return await context.Orgs
                .Include(o => o.Employees)
                    .ThenInclude(e => e.Account)
                .Where(o => o.Employees.Any(e => e.AccountUid == accountId))
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all orgs");
            return [];
        }
    }

    public async Task<IEnumerable<Orgz>> GetByLoginId(string loginId)
    {
        try
        {
            return await context.Orgs
                .Include(o => o.Employees)
                    .ThenInclude(e => e.Account)
                .Where(o => o.Employees.Any(e => e.Account.LoginId.Equals(loginId)))
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting orgs by login id");
            return [];
        }
    }

    public async Task<Orgz?> GetById(Guid orgId)
    {
        try
        {
            var accountId = GetCurrentAccountUid();
            return await context.Orgs
                .Include(o => o.Employees)
                        .ThenInclude(e => e.Account)
                    .Where(o => o.OrgUid == orgId)
                    .Where(o => o.Employees.Any(e => e.AccountUid == accountId))
                    .Where(o => !o.IsDeleted)
                    .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting org by id {Id}", orgId);
            return null;
        }
    }

    public async Task<Orgz?> Create(Orgz org)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Orgs.AddAsync(org);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(org.OrgUid);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating org: {Org}", org);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Orgz?> Update(Orgz org)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Orgs.Update(org);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(org.OrgUid);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating org: {Org}", org);
            await transaction.RollbackAsync();
            return null;
        }
    }
}

