﻿using Kantoku.SharedKernel.Attributes.Properties;

namespace Kantoku.Domain.Models;

public class UserInfo : AuditableEntity
{
    public Guid UserInfoUid { get; set; }
    public Guid AccountUid { get; set; }

    [AuditProperty]
    public string Name { get; set; } = null!;

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public string? Phone { get; set; }

    [AuditProperty]
    public bool? Gender { get; set; }

    [AuditProperty]
    public DateOnly? Birthday { get; set; }

    public string? AvatarUrl { get; set; }

    public virtual Account Account { get; set; } = null!;
}
